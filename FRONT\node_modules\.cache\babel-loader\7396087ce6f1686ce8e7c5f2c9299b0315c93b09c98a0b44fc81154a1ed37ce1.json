{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NEW_LMS_FIXING\\\\FRONT\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport { BrowserRouter, Routes, Route, Navigate, useNavigate, useLocation } from 'react-router-dom';\nimport { useEffect, useRef } from 'react';\nimport AuthRoutes from './routes/AuthRoutes.jsx';\nimport UserRoutes from './routes/UserRoutes.jsx';\nimport AdminRoutes from './routes/AdminRoutes.jsx';\nimport PublicRoutes from './routes/PublicRoutes.jsx';\nimport { NotificationProvider } from './context/NotificationContext';\nimport { PermissionsProvider } from './context/PermissionsContext';\nimport 'bootstrap/dist/css/bootstrap.min.css';\nimport 'bootstrap/dist/js/bootstrap.bundle.min.js';\nimport '../src/assets/styles/custom.css';\nimport './App.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction AppRouter() {\n  _s();\n  const token = localStorage.getItem('token');\n  const role = localStorage.getItem('role');\n  const navigate = useNavigate();\n  const location = useLocation();\n  const initialLoadRef = useRef(true);\n\n  // Check if current path is a public route\n  const isPublicRoute = location.pathname.startsWith('/public/course/');\n  useEffect(() => {\n    // Only redirect on initial load and not for public routes\n    if (initialLoadRef.current && token && window.location.pathname === '/' && !isPublicRoute) {\n      initialLoadRef.current = false;\n      if (role === 'trainee') {\n        navigate('/user/dashboard');\n      } else {\n        // For any other role (admin, trainer, etc.)\n        navigate('/admin/dashboard');\n      }\n    }\n  }, [token, role, navigate, isPublicRoute]);\n\n  // If it's a public route, always show public routes regardless of auth status\n  if (isPublicRoute) {\n    return /*#__PURE__*/_jsxDEV(PublicRoutes, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [!token && /*#__PURE__*/_jsxDEV(AuthRoutes, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 18\n    }, this), token && role === 'trainee' && /*#__PURE__*/_jsxDEV(UserRoutes, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 39\n    }, this), token && role !== 'trainee' && /*#__PURE__*/_jsxDEV(AdminRoutes, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 39\n    }, this)]\n  }, void 0, true);\n}\n_s(AppRouter, \"bZktQ/46Xvat+15Ja6z8Nsq+oSI=\", false, function () {\n  return [useNavigate, useLocation];\n});\n_c = AppRouter;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(PermissionsProvider, {\n    children: /*#__PURE__*/_jsxDEV(NotificationProvider, {\n      children: /*#__PURE__*/_jsxDEV(BrowserRouter, {\n        children: /*#__PURE__*/_jsxDEV(AppRouter, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 5\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 55,\n    columnNumber: 5\n  }, this);\n}\n_c2 = App;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"AppRouter\");\n$RefreshReg$(_c2, \"App\");", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "Navigate", "useNavigate", "useLocation", "useEffect", "useRef", "AuthRoutes", "UserRoutes", "AdminRoutes", "PublicRoutes", "NotificationProvider", "PermissionsProvider", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AppRouter", "_s", "token", "localStorage", "getItem", "role", "navigate", "location", "initialLoadRef", "isPublicRoute", "pathname", "startsWith", "current", "window", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "_c", "App", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/NEW_LMS_FIXING/FRONT/src/App.js"], "sourcesContent": ["import { BrowserRouter, Routes, Route, Navigate, useNavigate, useLocation } from 'react-router-dom';\r\nimport { useEffect, useRef } from 'react';\r\n\r\nimport AuthRoutes from './routes/AuthRoutes.jsx';\r\nimport UserRoutes from './routes/UserRoutes.jsx';\r\nimport AdminRoutes from './routes/AdminRoutes.jsx';\r\nimport PublicRoutes from './routes/PublicRoutes.jsx';\r\nimport { NotificationProvider } from './context/NotificationContext';\r\nimport { PermissionsProvider } from './context/PermissionsContext';\r\n\r\nimport 'bootstrap/dist/css/bootstrap.min.css';\r\nimport 'bootstrap/dist/js/bootstrap.bundle.min.js';\r\nimport '../src/assets/styles/custom.css';\r\nimport './App.css';\r\n\r\nfunction AppRouter() {\r\n  const token = localStorage.getItem('token');\r\n  const role = localStorage.getItem('role');\r\n  const navigate = useNavigate();\r\n  const location = useLocation();\r\n  const initialLoadRef = useRef(true);\r\n\r\n  // Check if current path is a public route\r\n  const isPublicRoute = location.pathname.startsWith('/public/course/');\r\n\r\n  useEffect(() => {\r\n    // Only redirect on initial load and not for public routes\r\n    if (initialLoadRef.current && token && window.location.pathname === '/' && !isPublicRoute) {\r\n      initialLoadRef.current = false;\r\n      if (role === 'trainee') {\r\n        navigate('/user/dashboard');\r\n      } else {\r\n        // For any other role (admin, trainer, etc.)\r\n        navigate('/admin/dashboard');\r\n      }\r\n    }\r\n  }, [token, role, navigate, isPublicRoute]);\r\n\r\n  // If it's a public route, always show public routes regardless of auth status\r\n  if (isPublicRoute) {\r\n    return <PublicRoutes />;\r\n  }\r\n\r\n  return (\r\n    <>\r\n      {!token && <AuthRoutes />}\r\n      {token && role === 'trainee' && <UserRoutes />}\r\n      {token && role !== 'trainee' && <AdminRoutes />}\r\n    </>\r\n  );\r\n}\r\n\r\nfunction App() {\r\n  return (\r\n    <PermissionsProvider>\r\n    <NotificationProvider>\r\n      <BrowserRouter>\r\n        <AppRouter />\r\n      </BrowserRouter>\r\n    </NotificationProvider>\r\n    </PermissionsProvider>\r\n  );\r\n}\r\n\r\nexport default App;\r\n"], "mappings": ";;AAAA,SAASA,aAAa,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACnG,SAASC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAEzC,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,SAASC,oBAAoB,QAAQ,+BAA+B;AACpE,SAASC,mBAAmB,QAAQ,8BAA8B;AAElE,OAAO,sCAAsC;AAC7C,OAAO,2CAA2C;AAClD,OAAO,iCAAiC;AACxC,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnB,SAASC,SAASA,CAAA,EAAG;EAAAC,EAAA;EACnB,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,MAAMC,IAAI,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;EACzC,MAAME,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAC9B,MAAMqB,QAAQ,GAAGpB,WAAW,CAAC,CAAC;EAC9B,MAAMqB,cAAc,GAAGnB,MAAM,CAAC,IAAI,CAAC;;EAEnC;EACA,MAAMoB,aAAa,GAAGF,QAAQ,CAACG,QAAQ,CAACC,UAAU,CAAC,iBAAiB,CAAC;EAErEvB,SAAS,CAAC,MAAM;IACd;IACA,IAAIoB,cAAc,CAACI,OAAO,IAAIV,KAAK,IAAIW,MAAM,CAACN,QAAQ,CAACG,QAAQ,KAAK,GAAG,IAAI,CAACD,aAAa,EAAE;MACzFD,cAAc,CAACI,OAAO,GAAG,KAAK;MAC9B,IAAIP,IAAI,KAAK,SAAS,EAAE;QACtBC,QAAQ,CAAC,iBAAiB,CAAC;MAC7B,CAAC,MAAM;QACL;QACAA,QAAQ,CAAC,kBAAkB,CAAC;MAC9B;IACF;EACF,CAAC,EAAE,CAACJ,KAAK,EAAEG,IAAI,EAAEC,QAAQ,EAAEG,aAAa,CAAC,CAAC;;EAE1C;EACA,IAAIA,aAAa,EAAE;IACjB,oBAAOZ,OAAA,CAACJ,YAAY;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACzB;EAEA,oBACEpB,OAAA,CAAAE,SAAA;IAAAmB,QAAA,GACG,CAAChB,KAAK,iBAAIL,OAAA,CAACP,UAAU;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACxBf,KAAK,IAAIG,IAAI,KAAK,SAAS,iBAAIR,OAAA,CAACN,UAAU;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAC7Cf,KAAK,IAAIG,IAAI,KAAK,SAAS,iBAAIR,OAAA,CAACL,WAAW;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA,eAC/C,CAAC;AAEP;AAAChB,EAAA,CAnCQD,SAAS;EAAA,QAGCd,WAAW,EACXC,WAAW;AAAA;AAAAgC,EAAA,GAJrBnB,SAAS;AAqClB,SAASoB,GAAGA,CAAA,EAAG;EACb,oBACEvB,OAAA,CAACF,mBAAmB;IAAAuB,QAAA,eACpBrB,OAAA,CAACH,oBAAoB;MAAAwB,QAAA,eACnBrB,OAAA,CAACf,aAAa;QAAAoC,QAAA,eACZrB,OAAA,CAACG,SAAS;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAE1B;AAACI,GAAA,GAVQD,GAAG;AAYZ,eAAeA,GAAG;AAAC,IAAAD,EAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAH,EAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}