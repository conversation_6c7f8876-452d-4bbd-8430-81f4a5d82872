{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NEW_LMS_FIXING\\\\FRONT\\\\src\\\\pages\\\\public\\\\PublicCourseDetails.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { FaVideo, FaDownload, FaInfinity, FaCertificate, FaShareAlt, FaTwitter, FaLinkedin, FaChevronDown, FaChevronUp, FaLock } from 'react-icons/fa';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { toast } from 'react-toastify';\nimport { Icon } from '@iconify/react';\nimport courseImg from '../../assets/images/course/course1.png';\nimport './PublicCourseDetails.css';\nimport DefaultProfile from '../../assets/images/profile/default-profile.png';\nimport { decodeData, encodeData } from '../../utils/encodeAndEncode';\nimport { getPublicCourseDetails } from '../../services/userService';\nimport NoData from '../../components/common/NoData';\n\n// Helper function to get currency symbol\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst getCurrencySymbol = currency => {\n  switch (currency === null || currency === void 0 ? void 0 : currency.toUpperCase()) {\n    case 'INR':\n      return '₹';\n    case 'USD':\n      return '$';\n    case 'SGD':\n      return 'S$';\n    case 'EUR':\n      return '€';\n    case 'GBP':\n      return '£';\n    default:\n      return '$';\n    // Default to USD symbol\n  }\n};\nconst domain = window.location.origin;\nfunction PublicCourseDetails() {\n  _s();\n  var _courseData$courseMet, _courseData$courseMet2, _courseData$courseMet3, _courseData$trainer, _courseData$trainer2, _courseData$trainer3, _courseData$trainer4, _courseData$trainer5, _courseData$trainer6, _courseData$trainer7, _courseData$trainer8, _courseData$trainer9, _courseData$course_ty, _courseData$course_ty2, _courseData$course_ty3, _courseData$courseMet4, _courseData$courseMet5;\n  console.log(\"🚀 === PublicCourseDetails Component Mounted ===\");\n  const navigate = useNavigate();\n  const [expandedModules, setExpandedModules] = useState(['module1']);\n  const [isEnrolling, setIsEnrolling] = useState(false);\n  const [isPaidEnrolling, setIsPaidEnrolling] = useState(false);\n  const {\n    encodedId\n  } = useParams();\n  console.log(\"🔍 URL Parameter encodedId:\", encodedId);\n  const decoded = decodeData(encodedId);\n  console.log(\"🔍 Decoded data:\", decoded);\n  const courseId = decoded === null || decoded === void 0 ? void 0 : decoded.id;\n  console.log(\"🔍 Extracted courseId:\", courseId);\n  useEffect(() => {\n    console.log('🔍 useEffect triggered - Decoded Course ID:', courseId);\n    console.log('🔍 Domain from env:', domain);\n  }, [courseId]);\n  const [courseData, setCourseData] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  // Function to process course data and parse JSON fields\n  const processCourseData = data => {\n    if (!data) return data;\n    const processed = {\n      ...data\n    };\n\n    // Parse tags if it's a string\n    if (typeof processed.tags === 'string') {\n      try {\n        processed.tags = JSON.parse(processed.tags);\n        console.log('Parsed tags:', processed.tags);\n      } catch (error) {\n        console.error('Error parsing tags:', error);\n        processed.tags = [];\n      }\n    }\n\n    // Parse course_info if it's a string\n    if (typeof processed.course_info === 'string') {\n      try {\n        processed.course_info = JSON.parse(processed.course_info);\n        console.log('Parsed course_info:', processed.course_info);\n      } catch (error) {\n        console.error('Error parsing course_info:', error);\n        processed.course_info = [];\n      }\n    }\n    return processed;\n  };\n  useEffect(() => {\n    getCourseDetails();\n  }, [courseId]);\n  async function getCourseDetails() {\n    try {\n      setIsLoading(true);\n      console.log('🚀 === getCourseDetails Function Called ===');\n      console.log('🔍 Request parameters:', {\n        domain: domain,\n        course_id: courseId\n      });\n      const response = await getPublicCourseDetails({\n        domain: domain,\n        course_id: courseId\n      });\n      console.log('📡 API Response received:', response);\n      console.log('📡 Response success:', response.success);\n      console.log('📡 Response data:', response.data);\n      if (response.success) {\n        console.log('✅ API call successful');\n        // Process the course data to parse JSON fields\n        const processedData = processCourseData(response.data);\n        console.log('🔧 Processed course data:', processedData);\n        console.log('🔧 Course name:', processedData.course_name);\n        console.log('🔧 Course modules:', processedData.modules);\n        setCourseData(processedData);\n        console.log('✅ Course data set in state');\n      } else {\n        console.error('❌ API call failed:', response.message);\n        toast.error('Failed to load course details');\n      }\n    } catch (error) {\n      console.error('💥 Error in getCourseDetails:', error);\n      console.error('💥 Error message:', error.message);\n      console.error('💥 Error stack:', error.stack);\n      toast.error('Error loading course details');\n    } finally {\n      setIsLoading(false);\n      console.log('🏁 getCourseDetails function completed');\n    }\n  }\n  async function EnrollFreeCourse() {\n    try {\n      setIsEnrolling(true);\n\n      // Simulate 2-second loader\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      console.log('Enrolling in free course...');\n\n      // Show toast after loading\n      toast.success('Redirecting to login...', {\n        position: 'top-right',\n        autoClose: 1000\n      });\n\n      // Wait 1 second after toast before redirecting\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      // Redirect to login page for enrollment\n      navigate('/login', {\n        state: {\n          redirectTo: `/public/courseDetails/${encodedId}`,\n          message: 'Please login to enroll in this course'\n        }\n      });\n    } catch (error) {\n      console.error('Error enrolling in course:', error);\n      toast.error('Failed to enroll in course. Please try again.');\n    } finally {\n      setIsEnrolling(false);\n    }\n  }\n  const toggleModule = moduleId => {\n    setExpandedModules(prev => prev.includes(moduleId) ? prev.filter(id => id !== moduleId) : [...prev, moduleId]);\n  };\n  const handlePaidEnrollment = async () => {\n    setIsPaidEnrolling(true);\n\n    // Simulate 2-second loading\n    await new Promise(resolve => setTimeout(resolve, 2000));\n\n    // Log the data being passed\n    console.log('Passing to OrderDetails:', {\n      courseId,\n      courseData\n    });\n\n    // Redirect to login page for paid enrollment\n    toast.success('Redirecting to login...', {\n      position: 'top-right',\n      autoClose: 1000\n    });\n\n    // Wait 1 second after toast before redirecting\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    navigate('/login', {\n      state: {\n        redirectTo: `/public/courseDetails/${encodedId}`,\n        message: 'Please login to purchase this course'\n      }\n    });\n    setIsPaidEnrolling(false);\n  };\n  if (isLoading) {\n    console.log('🔄 Component in loading state');\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-center align-items-center\",\n      style: {\n        minHeight: '60vh'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner-border text-primary\",\n        role: \"status\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"visually-hidden\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 211,\n      columnNumber: 7\n    }, this);\n  }\n  if (!courseData) {\n    console.log('❌ No course data available');\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-center align-items-center\",\n      style: {\n        minHeight: '60vh'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: \"Course Not Found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-muted\",\n          children: \"The course you're looking for doesn't exist or is not available.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary\",\n          onClick: () => navigate('/'),\n          children: \"Go Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 7\n    }, this);\n  }\n  console.log('🎨 Rendering course details with data:', courseData);\n  const hasModules = courseData.modules && Object.keys(courseData.modules).length > 0;\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 p-0\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"course-details-header\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"container\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex justify-content-between align-items-start mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"fw-bold mb-0\",\n                children: courseData.course_name || 'Untitled Course'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn btn-outline-primary btn-sm\",\n                  onClick: () => navigate('/login'),\n                  children: [/*#__PURE__*/_jsxDEV(Icon, {\n                    icon: \"material-symbols:login\",\n                    className: \"me-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 252,\n                    columnNumber: 21\n                  }, this), \"Login to Enroll\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"btn btn-outline-secondary btn-sm\",\n                  onClick: () => navigate('/register'),\n                  children: [/*#__PURE__*/_jsxDEV(Icon, {\n                    icon: \"material-symbols:person-add\",\n                    className: \"me-1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 259,\n                    columnNumber: 21\n                  }, this), \"Sign Up\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"font-size-8\",\n              children: courseData.tags && Array.isArray(courseData.tags) ? courseData.tags.join(' | ') : 'No tags available'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex flex-wrap align-items-center gap-4 mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(Icon, {\n                  icon: \"material-symbols:star\",\n                  className: \"text-warning\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ms-2\",\n                  children: [(courseData === null || courseData === void 0 ? void 0 : (_courseData$courseMet = courseData.courseMeta) === null || _courseData$courseMet === void 0 ? void 0 : _courseData$courseMet.averageRating) || '0.0', \" rating\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(Icon, {\n                  icon: \"material-symbols:menu-book\",\n                  className: \"me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [(courseData === null || courseData === void 0 ? void 0 : (_courseData$courseMet2 = courseData.courseMeta) === null || _courseData$courseMet2 === void 0 ? void 0 : _courseData$courseMet2.modulesCount) || 0, \" Modules\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(Icon, {\n                  icon: \"material-symbols:update\",\n                  className: \"me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Duration: \", (courseData === null || courseData === void 0 ? void 0 : (_courseData$courseMet3 = courseData.courseMeta) === null || _courseData$courseMet3 === void 0 ? void 0 : _courseData$courseMet3.totalVideoDuration) || '00:00:00']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(Icon, {\n                  icon: \"material-symbols:school\",\n                  className: \"me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Level: \", (courseData === null || courseData === void 0 ? void 0 : courseData.levels) || 'Not specified']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center\",\n                children: [/*#__PURE__*/_jsxDEV(Icon, {\n                  icon: \"material-symbols:language\",\n                  className: \"me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Language: \", (courseData === null || courseData === void 0 ? void 0 : courseData.course_language) || 'Not specified']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"instructor d-flex align-items-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"instructor-image\",\n                style: {\n                  width: '40px',\n                  height: '40px',\n                  minWidth: '40px'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: (courseData === null || courseData === void 0 ? void 0 : (_courseData$trainer = courseData.trainer) === null || _courseData$trainer === void 0 ? void 0 : _courseData$trainer.profile) || DefaultProfile,\n                  alt: courseData === null || courseData === void 0 ? void 0 : (_courseData$trainer2 = courseData.trainer) === null || _courseData$trainer2 === void 0 ? void 0 : _courseData$trainer2.name,\n                  className: \"rounded-circle\",\n                  style: {\n                    width: '100%',\n                    height: '100%',\n                    objectFit: 'cover'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex flex-column\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-size-1\",\n                  children: [\"By \", (courseData === null || courseData === void 0 ? void 0 : (_courseData$trainer3 = courseData.trainer) === null || _courseData$trainer3 === void 0 ? void 0 : _courseData$trainer3.name) || 'Loading...']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-size-1\",\n                  children: (courseData === null || courseData === void 0 ? void 0 : (_courseData$trainer4 = courseData.trainer) === null || _courseData$trainer4 === void 0 ? void 0 : _courseData$trainer4.role) || 'Instructor'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 304,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"row justify-content-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"col-12 col-md-11\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"course-details-container\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"row\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-lg-8\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"what-youll-learn mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"h4 mb-3\",\n                      children: \"Course Info\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 316,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"learning-points\",\n                      children: courseData.course_info && Array.isArray(courseData.course_info) ? courseData.course_info.map((point, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"learning-point mb-3 d-flex align-items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(Icon, {\n                          icon: \"material-symbols:check-circle\",\n                          className: \"text-success me-3\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 321,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: point\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 322,\n                          columnNumber: 31\n                        }, this)]\n                      }, index, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 320,\n                        columnNumber: 29\n                      }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"learning-point mb-3 d-flex align-items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(Icon, {\n                          icon: \"material-symbols:check-circle\",\n                          className: \"text-success me-3\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 327,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: \"Course information not available\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 328,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 326,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 317,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 21\n                  }, this), courseData.course_desc ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"about-course mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"h4 mb-3\",\n                      children: \"About This Course\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 336,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"about-content\",\n                      children: /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-muted mb-3\",\n                        style: {\n                          wordBreak: 'break-word',\n                          whiteSpace: 'normal'\n                        },\n                        children: courseData.course_desc\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 338,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 337,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 335,\n                    columnNumber: 23\n                  }, this) : null, /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"course-content mb-4 shadow-none\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"h4 mb-3\",\n                      children: \"Course Content\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 352,\n                      columnNumber: 23\n                    }, this), hasModules ? /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"modules-list\",\n                      children: Object.entries(courseData.modules).map(([moduleName, contents]) => /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"module-item\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"module-header d-flex align-items-center justify-content-between p-3\",\n                          onClick: () => toggleModule(moduleName),\n                          style: {\n                            cursor: 'pointer'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"d-flex align-items-center\",\n                            children: [expandedModules.includes(moduleName) ? /*#__PURE__*/_jsxDEV(FaChevronDown, {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 363,\n                              columnNumber: 75\n                            }, this) : /*#__PURE__*/_jsxDEV(FaChevronUp, {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 363,\n                              columnNumber: 95\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"ms-2\",\n                              children: moduleName\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 364,\n                              columnNumber: 35\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 362,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"module-meta text-muted\",\n                            children: /*#__PURE__*/_jsxDEV(\"small\", {\n                              children: [contents.length, \" items\"]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 367,\n                              columnNumber: 35\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 366,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 357,\n                          columnNumber: 31\n                        }, this), expandedModules.includes(moduleName) && /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"module-content\",\n                          children: contents.map((content, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"lecture-item d-flex align-items-center justify-content-between p-3\",\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"d-flex align-items-center\",\n                              children: [content.type === 'Video' && /*#__PURE__*/_jsxDEV(Icon, {\n                                icon: \"material-symbols:play-circle-outline\",\n                                className: \"me-2\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 376,\n                                columnNumber: 43\n                              }, this), content.type === 'Document' && /*#__PURE__*/_jsxDEV(Icon, {\n                                icon: \"material-symbols:description-outline\",\n                                className: \"me-2\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 379,\n                                columnNumber: 43\n                              }, this), content.type === 'Assessment' && /*#__PURE__*/_jsxDEV(Icon, {\n                                icon: \"material-symbols:quiz-outline\",\n                                className: \"me-2\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 382,\n                                columnNumber: 43\n                              }, this), content.type === 'Survey' && /*#__PURE__*/_jsxDEV(Icon, {\n                                icon: \"material-symbols:analytics-outline\",\n                                className: \"me-2\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 385,\n                                columnNumber: 43\n                              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                children: content.title\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 387,\n                                columnNumber: 41\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 374,\n                              columnNumber: 39\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              className: \"d-flex align-items-center\",\n                              children: [content.type === 'Video' && /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"ms-3 text-muted\",\n                                children: content.duration\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 391,\n                                columnNumber: 43\n                              }, this), content.type === 'Document' && /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"ms-3 text-muted\",\n                                children: content.fileType\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 394,\n                                columnNumber: 43\n                              }, this), content.type === 'Assessment' && content.questions && /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"ms-3 text-muted\",\n                                children: content.questions\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 397,\n                                columnNumber: 43\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 389,\n                              columnNumber: 39\n                            }, this)]\n                          }, idx, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 373,\n                            columnNumber: 37\n                          }, this))\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 371,\n                          columnNumber: 33\n                        }, this)]\n                      }, moduleName, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 356,\n                        columnNumber: 29\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 354,\n                      columnNumber: 25\n                    }, this) : /*#__PURE__*/_jsxDEV(NoData, {\n                      caption: \"No course content available yet\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 408,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 351,\n                    columnNumber: 21\n                  }, this), courseData.trainer ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"instructor-profile mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"h4 mb-4\",\n                      children: \"Instructor\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 414,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"d-flex flex-column flex-md-row gap-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"instructor-image-lg d-none d-md-block\",\n                        style: {\n                          width: '150px',\n                          height: '150px',\n                          minWidth: '150px',\n                          overflow: 'hidden',\n                          borderRadius: '12px',\n                          position: 'relative',\n                          backgroundColor: '#f8f9fa'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"img\", {\n                          src: (courseData === null || courseData === void 0 ? void 0 : (_courseData$trainer5 = courseData.trainer) === null || _courseData$trainer5 === void 0 ? void 0 : _courseData$trainer5.profile) || DefaultProfile,\n                          alt: courseData === null || courseData === void 0 ? void 0 : (_courseData$trainer6 = courseData.trainer) === null || _courseData$trainer6 === void 0 ? void 0 : _courseData$trainer6.name,\n                          style: {\n                            width: '100%',\n                            height: '100%',\n                            objectFit: 'cover'\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 428,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 416,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"instructor-info\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                          className: \"h5 mb-1\",\n                          children: (courseData === null || courseData === void 0 ? void 0 : (_courseData$trainer7 = courseData.trainer) === null || _courseData$trainer7 === void 0 ? void 0 : _courseData$trainer7.name) || 'Loading...'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 439,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-muted mb-3\",\n                          children: (courseData === null || courseData === void 0 ? void 0 : (_courseData$trainer8 = courseData.trainer) === null || _courseData$trainer8 === void 0 ? void 0 : _courseData$trainer8.role) || 'Instructor'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 440,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-muted mb-4\",\n                          children: (courseData === null || courseData === void 0 ? void 0 : (_courseData$trainer9 = courseData.trainer) === null || _courseData$trainer9 === void 0 ? void 0 : _courseData$trainer9.bio) || 'No bio available'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 443,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 438,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 415,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 413,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(NoData, {\n                    caption: \"Instructor information not available\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-lg-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"course-card card shadow-sm\",\n                    style: {\n                      position: 'relative',\n                      top: '-5rem'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      src: courseData.banner_image || courseImg,\n                      className: \"img-fluid border-2 m-2\",\n                      alt: courseData.course_name || \"Course Preview\",\n                      onError: e => {\n                        e.target.src = courseImg;\n                        e.target.onerror = null;\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 456,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"card-body\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-flex justify-content-between align-items-center mb-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"price-tag\",\n                          children: [getCurrencySymbol(courseData === null || courseData === void 0 ? void 0 : courseData.currency), \" \", (courseData === null || courseData === void 0 ? void 0 : (_courseData$course_ty = courseData.course_type) === null || _courseData$course_ty === void 0 ? void 0 : _courseData$course_ty.toLowerCase()) === 'free' ? '0' : (courseData === null || courseData === void 0 ? void 0 : courseData.course_price) || '0']\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 467,\n                          columnNumber: 27\n                        }, this), (courseData === null || courseData === void 0 ? void 0 : (_courseData$course_ty2 = courseData.course_type) === null || _courseData$course_ty2 === void 0 ? void 0 : _courseData$course_ty2.toLowerCase()) === 'free' ? /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"original-price\",\n                          children: [getCurrencySymbol(courseData === null || courseData === void 0 ? void 0 : courseData.currency), \"0\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 469,\n                          columnNumber: 29\n                        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"original-price\",\n                          children: [getCurrencySymbol(courseData === null || courseData === void 0 ? void 0 : courseData.currency), Number((courseData === null || courseData === void 0 ? void 0 : courseData.course_price) || 0) + 5]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 471,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 466,\n                        columnNumber: 25\n                      }, this), (courseData === null || courseData === void 0 ? void 0 : (_courseData$course_ty3 = courseData.course_type) === null || _courseData$course_ty3 === void 0 ? void 0 : _courseData$course_ty3.toLowerCase()) === 'paid' ? /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: handlePaidEnrollment,\n                        className: \"paid-btn\",\n                        disabled: isPaidEnrolling,\n                        children: isPaidEnrolling ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"spinner-border spinner-border-sm me-2\",\n                            role: \"status\",\n                            \"aria-hidden\": \"true\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 483,\n                            columnNumber: 33\n                          }, this), \"Processing...\"]\n                        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                          children: [/*#__PURE__*/_jsxDEV(Icon, {\n                            icon: \"mdi:lock\",\n                            className: \"btn-icon\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 488,\n                            columnNumber: 33\n                          }, this), \"Enroll Now\"]\n                        }, void 0, true)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 476,\n                        columnNumber: 27\n                      }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"watch-now-btn free-btn\",\n                        onClick: EnrollFreeCourse,\n                        disabled: isEnrolling,\n                        children: isEnrolling ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"spinner-border spinner-border-sm me-2\",\n                            role: \"status\",\n                            \"aria-hidden\": \"true\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 504,\n                            columnNumber: 33\n                          }, this), \"Enrolling...\"]\n                        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                          children: [/*#__PURE__*/_jsxDEV(Icon, {\n                            icon: \"mdi:play-circle\",\n                            className: \"btn-icon\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 509,\n                            columnNumber: 33\n                          }, this), \"Watch Now\"]\n                        }, void 0, true)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 497,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"d-flex justify-content-center align-items-center\",\n                        children: [/*#__PURE__*/_jsxDEV(FaInfinity, {\n                          className: \"text-muted me-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 520,\n                          columnNumber: 27\n                        }, this), \"Full lifetime access\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 519,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"course-includes\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                          className: \"h6 mb-3\",\n                          children: \"This course includes:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 525,\n                          columnNumber: 27\n                        }, this), hasModules ? /*#__PURE__*/_jsxDEV(\"ul\", {\n                          className: \"list-unstyled\",\n                          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                            className: \"mb-2 d-flex align-items-center\",\n                            children: [/*#__PURE__*/_jsxDEV(Icon, {\n                              icon: \"material-symbols:menu-book\",\n                              className: \"text-muted me-2\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 529,\n                              columnNumber: 33\n                            }, this), (courseData === null || courseData === void 0 ? void 0 : (_courseData$courseMet4 = courseData.courseMeta) === null || _courseData$courseMet4 === void 0 ? void 0 : _courseData$courseMet4.modulesCount) || 0, \" Modules\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 528,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                            className: \"mb-2 d-flex align-items-center\",\n                            children: [/*#__PURE__*/_jsxDEV(FaVideo, {\n                              className: \"text-muted me-2\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 533,\n                              columnNumber: 33\n                            }, this), (courseData === null || courseData === void 0 ? void 0 : courseData.modules) && Object.values(courseData.modules).flat().filter(item => item.type === 'Video').length || 0, \" Videos \\u2022 \", (courseData === null || courseData === void 0 ? void 0 : (_courseData$courseMet5 = courseData.courseMeta) === null || _courseData$courseMet5 === void 0 ? void 0 : _courseData$courseMet5.totalVideoDuration) || '00:00:00', \" total duration\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 532,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                            className: \"mb-2 d-flex align-items-center\",\n                            children: [/*#__PURE__*/_jsxDEV(Icon, {\n                              icon: \"material-symbols:quiz\",\n                              className: \"text-muted me-2\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 537,\n                              columnNumber: 33\n                            }, this), (courseData === null || courseData === void 0 ? void 0 : courseData.modules) && Object.values(courseData.modules).flat().filter(item => item.type === 'Assessment').length || 0, \" Assessments\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 536,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                            className: \"mb-2 d-flex align-items-center\",\n                            children: [/*#__PURE__*/_jsxDEV(Icon, {\n                              icon: \"material-symbols:description-outline\",\n                              className: \"text-muted me-2\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 541,\n                              columnNumber: 33\n                            }, this), (courseData === null || courseData === void 0 ? void 0 : courseData.modules) && Object.values(courseData.modules).flat().filter(item => item.type === 'Document').length || 0, \" Documents\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 540,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                            className: \"mb-2 d-flex align-items-center\",\n                            children: [/*#__PURE__*/_jsxDEV(Icon, {\n                              icon: \"material-symbols:analytics-outline\",\n                              className: \"text-muted me-2\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 545,\n                              columnNumber: 33\n                            }, this), (courseData === null || courseData === void 0 ? void 0 : courseData.modules) && Object.values(courseData.modules).flat().filter(item => item.type === 'Survey').length || 0, \" Surveys\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 544,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                            className: \"mb-2 d-flex align-items-center\",\n                            children: [/*#__PURE__*/_jsxDEV(FaInfinity, {\n                              className: \"text-muted me-2\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 549,\n                              columnNumber: 33\n                            }, this), \"Full lifetime access\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 548,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                            className: \"mb-2 d-flex align-items-center\",\n                            children: [/*#__PURE__*/_jsxDEV(FaCertificate, {\n                              className: \"text-muted me-2\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 553,\n                              columnNumber: 33\n                            }, this), \"Certificate of completion\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 552,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 527,\n                          columnNumber: 29\n                        }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-muted\",\n                          children: \"Course content is being prepared\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 558,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 524,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 465,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 455,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 7\n    }, this)\n  }, void 0, false);\n}\n_s(PublicCourseDetails, \"7bjbcg1i+a/k9kcg+UWgFa9PSIo=\", false, function () {\n  return [useNavigate, useParams];\n});\n_c = PublicCourseDetails;\nexport default PublicCourseDetails;\nvar _c;\n$RefreshReg$(_c, \"PublicCourseDetails\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "FaVideo", "FaDownload", "FaInfinity", "FaCertificate", "FaShareAlt", "FaTwitter", "FaLinkedin", "FaChevronDown", "FaChevronUp", "FaLock", "useNavigate", "useParams", "toast", "Icon", "courseImg", "DefaultProfile", "decodeData", "encodeData", "getPublicCourseDetails", "NoData", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "getCurrencySymbol", "currency", "toUpperCase", "domain", "window", "location", "origin", "PublicCourseDetails", "_s", "_courseData$courseMet", "_courseData$courseMet2", "_courseData$courseMet3", "_courseData$trainer", "_courseData$trainer2", "_courseData$trainer3", "_courseData$trainer4", "_courseData$trainer5", "_courseData$trainer6", "_courseData$trainer7", "_courseData$trainer8", "_courseData$trainer9", "_courseData$course_ty", "_courseData$course_ty2", "_courseData$course_ty3", "_courseData$courseMet4", "_courseData$courseMet5", "console", "log", "navigate", "expandedModules", "setExpandedModules", "isEnrolling", "setIsEnrolling", "isPaidEnrolling", "setIsPaidEnrolling", "encodedId", "decoded", "courseId", "id", "courseData", "setCourseData", "isLoading", "setIsLoading", "processCourseData", "data", "processed", "tags", "JSON", "parse", "error", "course_info", "getCourseDetails", "course_id", "response", "success", "processedData", "course_name", "modules", "message", "stack", "EnrollFreeCourse", "Promise", "resolve", "setTimeout", "position", "autoClose", "state", "redirectTo", "toggleModule", "moduleId", "prev", "includes", "filter", "handlePaidEnrollment", "className", "style", "minHeight", "children", "role", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "hasModules", "Object", "keys", "length", "icon", "Array", "isArray", "join", "courseMeta", "averageRating", "modulesCount", "totalVideoDuration", "levels", "course_language", "width", "height", "min<PERSON><PERSON><PERSON>", "src", "trainer", "profile", "alt", "name", "objectFit", "map", "point", "index", "course_desc", "wordBreak", "whiteSpace", "entries", "moduleName", "contents", "cursor", "content", "idx", "type", "title", "duration", "fileType", "questions", "caption", "overflow", "borderRadius", "backgroundColor", "bio", "top", "banner_image", "onError", "e", "target", "onerror", "course_type", "toLowerCase", "course_price", "Number", "disabled", "values", "flat", "item", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/NEW_LMS_FIXING/FRONT/src/pages/public/PublicCourseDetails.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport {\r\n  FaVideo, FaDownload, FaInfinity, FaCertificate, FaShareAlt,\r\n  FaTwitter, FaLinkedin, FaChevronDown, FaChevronUp, FaLock\r\n} from 'react-icons/fa';\r\nimport { useNavigate, useParams } from 'react-router-dom';\r\nimport { toast } from 'react-toastify';\r\nimport { Icon } from '@iconify/react';\r\nimport courseImg from '../../assets/images/course/course1.png';\r\nimport './PublicCourseDetails.css';\r\nimport DefaultProfile from '../../assets/images/profile/default-profile.png';\r\nimport { decodeData, encodeData } from '../../utils/encodeAndEncode';\r\nimport { getPublicCourseDetails } from '../../services/userService';\r\nimport NoData from '../../components/common/NoData';\r\n\r\n// Helper function to get currency symbol\r\nconst getCurrencySymbol = (currency) => {\r\n  switch (currency?.toUpperCase()) {\r\n    case 'INR':\r\n      return '₹';\r\n    case 'USD':\r\n      return '$';\r\n    case 'SGD':\r\n      return 'S$';\r\n    case 'EUR':\r\n      return '€';\r\n    case 'GBP':\r\n      return '£';\r\n    default:\r\n      return '$'; // Default to USD symbol\r\n  }\r\n};\r\n\r\nconst domain = window.location.origin;\r\n\r\nfunction PublicCourseDetails() {\r\n  console.log(\"🚀 === PublicCourseDetails Component Mounted ===\");\r\n  const navigate = useNavigate(); \r\n  const [expandedModules, setExpandedModules] = useState(['module1']);\r\n  const [isEnrolling, setIsEnrolling] = useState(false);\r\n  const [isPaidEnrolling, setIsPaidEnrolling] = useState(false);\r\n\r\n  const { encodedId } = useParams();\r\n  console.log(\"🔍 URL Parameter encodedId:\", encodedId);\r\n  const decoded = decodeData(encodedId);\r\n  console.log(\"🔍 Decoded data:\", decoded);\r\n  const courseId = decoded?.id;\r\n  console.log(\"🔍 Extracted courseId:\", courseId);\r\n\r\n  useEffect(() => {\r\n    console.log('🔍 useEffect triggered - Decoded Course ID:', courseId);\r\n    console.log('🔍 Domain from env:', domain);\r\n  }, [courseId]);\r\n\r\n  const [courseData, setCourseData] = useState(null);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n\r\n  // Function to process course data and parse JSON fields\r\n  const processCourseData = (data) => {\r\n    if (!data) return data;\r\n    \r\n    const processed = { ...data };\r\n    \r\n    // Parse tags if it's a string\r\n    if (typeof processed.tags === 'string') {\r\n      try {\r\n        processed.tags = JSON.parse(processed.tags);\r\n        console.log('Parsed tags:', processed.tags);\r\n      } catch (error) {\r\n        console.error('Error parsing tags:', error);\r\n        processed.tags = [];\r\n      }\r\n    }\r\n    \r\n    // Parse course_info if it's a string\r\n    if (typeof processed.course_info === 'string') {\r\n      try {\r\n        processed.course_info = JSON.parse(processed.course_info);\r\n        console.log('Parsed course_info:', processed.course_info);\r\n      } catch (error) {\r\n        console.error('Error parsing course_info:', error);\r\n        processed.course_info = [];\r\n      }\r\n    }\r\n    \r\n    return processed;\r\n  };\r\n\r\n  useEffect(() => {\r\n    getCourseDetails();\r\n  }, [courseId]);\r\n\r\n  async function getCourseDetails() {\r\n    try {\r\n      setIsLoading(true);\r\n      console.log('🚀 === getCourseDetails Function Called ===');\r\n      console.log('🔍 Request parameters:', {\r\n        domain: domain,\r\n        course_id: courseId\r\n      });\r\n      \r\n      const response = await getPublicCourseDetails({\r\n        domain: domain,\r\n        course_id: courseId\r\n      });\r\n\r\n      console.log('📡 API Response received:', response);\r\n      console.log('📡 Response success:', response.success);\r\n      console.log('📡 Response data:', response.data);\r\n      \r\n      if (response.success) {\r\n        console.log('✅ API call successful');\r\n        // Process the course data to parse JSON fields\r\n        const processedData = processCourseData(response.data);\r\n        console.log('🔧 Processed course data:', processedData);\r\n        console.log('🔧 Course name:', processedData.course_name);\r\n        console.log('🔧 Course modules:', processedData.modules);\r\n        setCourseData(processedData);\r\n        console.log('✅ Course data set in state');\r\n      } else {\r\n        console.error('❌ API call failed:', response.message);\r\n        toast.error('Failed to load course details');\r\n      }\r\n    } catch (error) {\r\n      console.error('💥 Error in getCourseDetails:', error);\r\n      console.error('💥 Error message:', error.message);\r\n      console.error('💥 Error stack:', error.stack);\r\n      toast.error('Error loading course details');\r\n    } finally {\r\n      setIsLoading(false);\r\n      console.log('🏁 getCourseDetails function completed');\r\n    }\r\n  }\r\n\r\n  async function EnrollFreeCourse() {\r\n    try {\r\n      setIsEnrolling(true);\r\n\r\n      // Simulate 2-second loader\r\n      await new Promise(resolve => setTimeout(resolve, 2000));\r\n\r\n      console.log('Enrolling in free course...');\r\n\r\n      // Show toast after loading\r\n      toast.success('Redirecting to login...', {\r\n        position: 'top-right',\r\n        autoClose: 1000,\r\n      });\r\n\r\n      // Wait 1 second after toast before redirecting\r\n      await new Promise(resolve => setTimeout(resolve, 1000));\r\n\r\n      // Redirect to login page for enrollment\r\n      navigate('/login', { \r\n        state: { \r\n          redirectTo: `/public/courseDetails/${encodedId}`,\r\n          message: 'Please login to enroll in this course'\r\n        } \r\n      });\r\n\r\n    } catch (error) {\r\n      console.error('Error enrolling in course:', error);\r\n      toast.error('Failed to enroll in course. Please try again.');\r\n    } finally {\r\n      setIsEnrolling(false);\r\n    }\r\n  }\r\n\r\n  const toggleModule = (moduleId) => {\r\n    setExpandedModules(prev =>\r\n      prev.includes(moduleId)\r\n        ? prev.filter(id => id !== moduleId)\r\n        : [...prev, moduleId]\r\n    );\r\n  };\r\n\r\n  const handlePaidEnrollment = async () => {\r\n    setIsPaidEnrolling(true);\r\n\r\n    // Simulate 2-second loading\r\n    await new Promise(resolve => setTimeout(resolve, 2000));\r\n\r\n    // Log the data being passed\r\n    console.log('Passing to OrderDetails:', {\r\n      courseId,\r\n      courseData \r\n    });\r\n\r\n    // Redirect to login page for paid enrollment\r\n    toast.success('Redirecting to login...', {\r\n      position: 'top-right',\r\n      autoClose: 1000,\r\n    });\r\n\r\n    // Wait 1 second after toast before redirecting\r\n    await new Promise(resolve => setTimeout(resolve, 1000));\r\n\r\n    navigate('/login', { \r\n      state: { \r\n        redirectTo: `/public/courseDetails/${encodedId}`,\r\n        message: 'Please login to purchase this course'\r\n      } \r\n    });\r\n\r\n    setIsPaidEnrolling(false);\r\n  };\r\n\r\n  if (isLoading) {\r\n    console.log('🔄 Component in loading state');\r\n    return (\r\n      <div className=\"d-flex justify-content-center align-items-center\" style={{ minHeight: '60vh' }}>\r\n        <div className=\"spinner-border text-primary\" role=\"status\">\r\n          <span className=\"visually-hidden\">Loading...</span>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (!courseData) {\r\n    console.log('❌ No course data available');\r\n    return (\r\n      <div className=\"d-flex justify-content-center align-items-center\" style={{ minHeight: '60vh' }}>\r\n        <div className=\"text-center\">\r\n          <h4>Course Not Found</h4>\r\n          <p className=\"text-muted\">The course you're looking for doesn't exist or is not available.</p>\r\n          <button className=\"btn btn-primary\" onClick={() => navigate('/')}>\r\n            Go Home\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  console.log('🎨 Rendering course details with data:', courseData);\r\n\r\n  const hasModules = courseData.modules &&\r\n    Object.keys(courseData.modules).length > 0;\r\n\r\n  return (\r\n    <>\r\n      <div className=\"row\">\r\n        <div className=\"col-12 p-0\">\r\n          <div className=\"course-details-header\">\r\n            <div className=\"container\">\r\n              <div className=\"d-flex justify-content-between align-items-start mb-3\">\r\n                <h3 className=\"fw-bold mb-0\">{courseData.course_name || 'Untitled Course'}</h3>\r\n                <div className=\"d-flex gap-2\">\r\n                  <button \r\n                    className=\"btn btn-outline-primary btn-sm\"\r\n                    onClick={() => navigate('/login')}\r\n                  >\r\n                    <Icon icon=\"material-symbols:login\" className=\"me-1\" />\r\n                    Login to Enroll\r\n                  </button>\r\n                  <button \r\n                    className=\"btn btn-outline-secondary btn-sm\"\r\n                    onClick={() => navigate('/register')}\r\n                  >\r\n                    <Icon icon=\"material-symbols:person-add\" className=\"me-1\" />\r\n                    Sign Up\r\n                  </button>\r\n                </div>\r\n              </div>\r\n              <p className=\"font-size-8\">\r\n                {courseData.tags && Array.isArray(courseData.tags) ? (\r\n                  courseData.tags.join(' | ')\r\n                ) : (\r\n                  'No tags available'\r\n                )}\r\n              </p>\r\n              <div className=\"d-flex flex-wrap align-items-center gap-4 mb-3\">\r\n                <div className=\"d-flex align-items-center\">\r\n                  <Icon icon=\"material-symbols:star\" className=\"text-warning\" />\r\n                  <span className=\"ms-2\">{courseData?.courseMeta?.averageRating || '0.0'} rating</span>\r\n                </div>\r\n                <div className=\"d-flex align-items-center\">\r\n                  <Icon icon=\"material-symbols:menu-book\" className=\"me-2\" />\r\n                  <span>{courseData?.courseMeta?.modulesCount || 0} Modules</span>\r\n                </div>\r\n                <div className=\"d-flex align-items-center\">\r\n                  <Icon icon=\"material-symbols:update\" className=\"me-2\" />\r\n                  <span>Duration: {courseData?.courseMeta?.totalVideoDuration || '00:00:00'}</span>\r\n                </div>\r\n                <div className=\"d-flex align-items-center\">\r\n                  <Icon icon=\"material-symbols:school\" className=\"me-2\" />\r\n                  <span>Level: {courseData?.levels || 'Not specified'}</span>\r\n                </div>\r\n                <div className=\"d-flex align-items-center\">\r\n                  <Icon icon=\"material-symbols:language\" className=\"me-2\" />\r\n                  <span>Language: {courseData?.course_language || 'Not specified'}</span>\r\n                </div>\r\n              </div>\r\n              <div className=\"instructor d-flex align-items-center gap-2\">\r\n                <div className=\"instructor-image\" style={{ width: '40px', height: '40px', minWidth: '40px' }}>\r\n                  <img\r\n                    src={courseData?.trainer?.profile || DefaultProfile}\r\n                    alt={courseData?.trainer?.name}\r\n                    className=\"rounded-circle\"\r\n                    style={{ width: '100%', height: '100%', objectFit: 'cover' }}\r\n                  />\r\n                </div>\r\n                <div className='d-flex flex-column'>\r\n                  <span className='font-size-1'>By {courseData?.trainer?.name || 'Loading...'}</span>\r\n                  <span className='font-size-1'>{courseData?.trainer?.role || 'Instructor'}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"row justify-content-center\">\r\n            <div className=\"col-12 col-md-11\">\r\n              <div className=\"course-details-container\">\r\n                <div className=\"row\">\r\n                  <div className=\"col-lg-8\">\r\n                    <div className=\"what-youll-learn mb-4\">\r\n                      <h3 className=\"h4 mb-3\">Course Info</h3>\r\n                      <div className=\"learning-points\">\r\n                        {courseData.course_info && Array.isArray(courseData.course_info) ? (\r\n                          courseData.course_info.map((point, index) => (\r\n                            <div key={index} className=\"learning-point mb-3 d-flex align-items-center\">\r\n                              <Icon icon=\"material-symbols:check-circle\" className=\"text-success me-3\" />\r\n                              <span>{point}</span>\r\n                            </div>\r\n                          ))\r\n                        ) : (\r\n                          <div className=\"learning-point mb-3 d-flex align-items-center\">\r\n                            <Icon icon=\"material-symbols:check-circle\" className=\"text-success me-3\" />\r\n                            <span>Course information not available</span>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n\r\n                    {courseData.course_desc ? (\r\n                      <div className=\"about-course mb-4\">\r\n                        <h3 className=\"h4 mb-3\">About This Course</h3>\r\n                        <div className=\"about-content\">\r\n                          <p\r\n                            className=\"text-muted mb-3\"\r\n                            style={{\r\n                              wordBreak: 'break-word',\r\n                              whiteSpace: 'normal',\r\n                            }}\r\n                          >\r\n                            {courseData.course_desc}\r\n                          </p>\r\n                        </div>\r\n                      </div>\r\n                    ) : null}\r\n\r\n                    <div className=\"course-content mb-4 shadow-none\">\r\n                      <h3 className=\"h4 mb-3\">Course Content</h3>\r\n                      {hasModules ? (\r\n                        <div className=\"modules-list\">\r\n                          {Object.entries(courseData.modules).map(([moduleName, contents]) => (\r\n                            <div key={moduleName} className=\"module-item\">\r\n                              <div\r\n                                className=\"module-header d-flex align-items-center justify-content-between p-3\"\r\n                                onClick={() => toggleModule(moduleName)}\r\n                                style={{ cursor: 'pointer' }}\r\n                              >\r\n                                <div className=\"d-flex align-items-center\">\r\n                                  {expandedModules.includes(moduleName) ? <FaChevronDown /> : <FaChevronUp />}\r\n                                  <span className=\"ms-2\">{moduleName}</span>\r\n                                </div>\r\n                                <div className=\"module-meta text-muted\">\r\n                                  <small>{contents.length} items</small>\r\n                                </div>\r\n                              </div>\r\n                              {expandedModules.includes(moduleName) && (\r\n                                <div className=\"module-content\">\r\n                                  {contents.map((content, idx) => (\r\n                                    <div key={idx} className=\"lecture-item d-flex align-items-center justify-content-between p-3\">\r\n                                      <div className=\"d-flex align-items-center\">\r\n                                        {content.type === 'Video' && (\r\n                                          <Icon icon=\"material-symbols:play-circle-outline\" className=\"me-2\" />\r\n                                        )}\r\n                                        {content.type === 'Document' && (\r\n                                          <Icon icon=\"material-symbols:description-outline\" className=\"me-2\" />\r\n                                        )}\r\n                                        {content.type === 'Assessment' && (\r\n                                          <Icon icon=\"material-symbols:quiz-outline\" className=\"me-2\" />\r\n                                        )}\r\n                                        {content.type === 'Survey' && (\r\n                                          <Icon icon=\"material-symbols:analytics-outline\" className=\"me-2\" />\r\n                                        )}\r\n                                        <span>{content.title}</span>\r\n                                      </div>\r\n                                      <div className=\"d-flex align-items-center\">\r\n                                        {content.type === 'Video' && (\r\n                                          <span className=\"ms-3 text-muted\">{content.duration}</span>\r\n                                        )}\r\n                                        {content.type === 'Document' && (\r\n                                          <span className=\"ms-3 text-muted\">{content.fileType}</span>\r\n                                        )}\r\n                                        {content.type === 'Assessment' && content.questions && (\r\n                                          <span className=\"ms-3 text-muted\">{content.questions}</span>\r\n                                        )}\r\n                                      </div>\r\n                                    </div>\r\n                                  ))}\r\n                                </div>\r\n                              )}\r\n                            </div>\r\n                          ))}\r\n                        </div>\r\n                      ) : (\r\n                        <NoData caption=\"No course content available yet\" />\r\n                      )}\r\n                    </div>\r\n\r\n                    {courseData.trainer ? (\r\n                      <div className=\"instructor-profile mb-4\">\r\n                        <h3 className=\"h4 mb-4\">Instructor</h3>\r\n                        <div className=\"d-flex flex-column flex-md-row gap-4\">\r\n                          <div\r\n                            className=\"instructor-image-lg d-none d-md-block\"\r\n                            style={{\r\n                              width: '150px',\r\n                              height: '150px',\r\n                              minWidth: '150px',\r\n                              overflow: 'hidden',\r\n                              borderRadius: '12px',\r\n                              position: 'relative',\r\n                              backgroundColor: '#f8f9fa'\r\n                            }}\r\n                          >\r\n                            <img\r\n                              src={courseData?.trainer?.profile || DefaultProfile}\r\n                              alt={courseData?.trainer?.name}\r\n                              style={{\r\n                                width: '100%',\r\n                                height: '100%',\r\n                                objectFit: 'cover'\r\n                              }}\r\n                            />\r\n                          </div>\r\n                          <div className=\"instructor-info\">\r\n                            <h4 className=\"h5 mb-1\">{courseData?.trainer?.name || 'Loading...'}</h4>\r\n                            <p className=\"text-muted mb-3\">\r\n                              {courseData?.trainer?.role || 'Instructor'}\r\n                            </p>\r\n                            <p className=\"text-muted mb-4\">\r\n                              {courseData?.trainer?.bio || 'No bio available'}\r\n                            </p>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    ) : (\r\n                      <NoData caption=\"Instructor information not available\" />\r\n                    )}\r\n                  </div>\r\n\r\n                  <div className=\"col-lg-4\">\r\n                    <div className=\"course-card card shadow-sm\" style={{ position: 'relative', top: '-5rem' }}>\r\n                      <img\r\n                        src={courseData.banner_image || courseImg}\r\n                        className=\"img-fluid border-2 m-2\"\r\n                        alt={courseData.course_name || \"Course Preview\"}\r\n                        onError={(e) => {\r\n                          e.target.src = courseImg;\r\n                          e.target.onerror = null;\r\n                        }}\r\n                      />\r\n                      <div className=\"card-body\">\r\n                        <div className=\"d-flex justify-content-between align-items-center mb-3\">\r\n                          <span className=\"price-tag\">{getCurrencySymbol(courseData?.currency)} {courseData?.course_type?.toLowerCase() === 'free' ? '0' : courseData?.course_price || '0'}</span>\r\n                          {courseData?.course_type?.toLowerCase() === 'free' ? (\r\n                            <span className=\"original-price\">{getCurrencySymbol(courseData?.currency)}0</span>\r\n                          ) : (\r\n                            <span className=\"original-price\">{getCurrencySymbol(courseData?.currency)}{Number(courseData?.course_price || 0) + 5}</span>\r\n                          )}\r\n                        </div>\r\n\r\n                        {courseData?.course_type?.toLowerCase() === 'paid' ? (\r\n                          <button\r\n                            onClick={handlePaidEnrollment}\r\n                            className=\"paid-btn\"\r\n                            disabled={isPaidEnrolling}\r\n                          >\r\n                            {isPaidEnrolling ? (\r\n                              <>\r\n                                <span className=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\r\n                                Processing...\r\n                              </>\r\n                            ) : (\r\n                              <>\r\n                                <Icon\r\n                                  icon=\"mdi:lock\"\r\n                                  className=\"btn-icon\"\r\n                                />\r\n                                Enroll Now\r\n                              </>\r\n                            )}\r\n                          </button>\r\n                        ) : (\r\n                          <button\r\n                            className=\"watch-now-btn free-btn\"\r\n                            onClick={EnrollFreeCourse}\r\n                            disabled={isEnrolling}\r\n                          >\r\n                            {isEnrolling ? (\r\n                              <>\r\n                                <span className=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\r\n                                Enrolling...\r\n                              </>\r\n                            ) : (\r\n                              <>\r\n                                <Icon\r\n                                  icon=\"mdi:play-circle\"\r\n                                  className=\"btn-icon\"\r\n                                />\r\n                                Watch Now\r\n                              </>\r\n                            )}\r\n                          </button>\r\n                        )}\r\n\r\n                        <div className=\"d-flex justify-content-center align-items-center\">\r\n                          <FaInfinity className=\"text-muted me-2\" />\r\n                          Full lifetime access\r\n                        </div>\r\n\r\n                        <div className=\"course-includes\">\r\n                          <h4 className=\"h6 mb-3\">This course includes:</h4>\r\n                          {hasModules ? (\r\n                            <ul className=\"list-unstyled\">\r\n                              <li className=\"mb-2 d-flex align-items-center\">\r\n                                <Icon icon=\"material-symbols:menu-book\" className=\"text-muted me-2\" />\r\n                                {courseData?.courseMeta?.modulesCount || 0} Modules\r\n                              </li>\r\n                              <li className=\"mb-2 d-flex align-items-center\">\r\n                                <FaVideo className=\"text-muted me-2\" />\r\n                                {courseData?.modules && Object.values(courseData.modules).flat().filter(item => item.type === 'Video').length || 0} Videos • {courseData?.courseMeta?.totalVideoDuration || '00:00:00'} total duration\r\n                              </li>\r\n                              <li className=\"mb-2 d-flex align-items-center\">\r\n                                <Icon icon=\"material-symbols:quiz\" className=\"text-muted me-2\" />\r\n                                {courseData?.modules && Object.values(courseData.modules).flat().filter(item => item.type === 'Assessment').length || 0} Assessments\r\n                              </li>\r\n                              <li className=\"mb-2 d-flex align-items-center\">\r\n                                <Icon icon=\"material-symbols:description-outline\" className=\"text-muted me-2\" />\r\n                                {courseData?.modules && Object.values(courseData.modules).flat().filter(item => item.type === 'Document').length || 0} Documents\r\n                              </li>\r\n                              <li className=\"mb-2 d-flex align-items-center\">\r\n                                <Icon icon=\"material-symbols:analytics-outline\" className=\"text-muted me-2\" />\r\n                                {courseData?.modules && Object.values(courseData.modules).flat().filter(item => item.type === 'Survey').length || 0} Surveys\r\n                              </li>\r\n                              <li className=\"mb-2 d-flex align-items-center\">\r\n                                <FaInfinity className=\"text-muted me-2\" />\r\n                                Full lifetime access\r\n                              </li>\r\n                              <li className=\"mb-2 d-flex align-items-center\">\r\n                                <FaCertificate className=\"text-muted me-2\" />\r\n                                Certificate of completion\r\n                              </li>\r\n                            </ul>\r\n                          ) : (\r\n                            <p className=\"text-muted\">Course content is being prepared</p>\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default PublicCourseDetails; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,OAAO,EAAEC,UAAU,EAAEC,UAAU,EAAEC,aAAa,EAAEC,UAAU,EAC1DC,SAAS,EAAEC,UAAU,EAAEC,aAAa,EAAEC,WAAW,EAAEC,MAAM,QACpD,gBAAgB;AACvB,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,IAAI,QAAQ,gBAAgB;AACrC,OAAOC,SAAS,MAAM,wCAAwC;AAC9D,OAAO,2BAA2B;AAClC,OAAOC,cAAc,MAAM,iDAAiD;AAC5E,SAASC,UAAU,EAAEC,UAAU,QAAQ,6BAA6B;AACpE,SAASC,sBAAsB,QAAQ,4BAA4B;AACnE,OAAOC,MAAM,MAAM,gCAAgC;;AAEnD;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,iBAAiB,GAAIC,QAAQ,IAAK;EACtC,QAAQA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEC,WAAW,CAAC,CAAC;IAC7B,KAAK,KAAK;MACR,OAAO,GAAG;IACZ,KAAK,KAAK;MACR,OAAO,GAAG;IACZ,KAAK,KAAK;MACR,OAAO,IAAI;IACb,KAAK,KAAK;MACR,OAAO,GAAG;IACZ,KAAK,KAAK;MACR,OAAO,GAAG;IACZ;MACE,OAAO,GAAG;IAAE;EAChB;AACF,CAAC;AAED,MAAMC,MAAM,GAAGC,MAAM,CAACC,QAAQ,CAACC,MAAM;AAErC,SAASC,mBAAmBA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,mBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAC7BC,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;EAC/D,MAAMC,QAAQ,GAAG1C,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC2C,eAAe,EAAEC,kBAAkB,CAAC,GAAGxD,QAAQ,CAAC,CAAC,SAAS,CAAC,CAAC;EACnE,MAAM,CAACyD,WAAW,EAAEC,cAAc,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC2D,eAAe,EAAEC,kBAAkB,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EAE7D,MAAM;IAAE6D;EAAU,CAAC,GAAGhD,SAAS,CAAC,CAAC;EACjCuC,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEQ,SAAS,CAAC;EACrD,MAAMC,OAAO,GAAG5C,UAAU,CAAC2C,SAAS,CAAC;EACrCT,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAES,OAAO,CAAC;EACxC,MAAMC,QAAQ,GAAGD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEE,EAAE;EAC5BZ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEU,QAAQ,CAAC;EAE/C9D,SAAS,CAAC,MAAM;IACdmD,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEU,QAAQ,CAAC;IACpEX,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAExB,MAAM,CAAC;EAC5C,CAAC,EAAE,CAACkC,QAAQ,CAAC,CAAC;EAEd,MAAM,CAACE,UAAU,EAAEC,aAAa,CAAC,GAAGlE,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACmE,SAAS,EAAEC,YAAY,CAAC,GAAGpE,QAAQ,CAAC,IAAI,CAAC;;EAEhD;EACA,MAAMqE,iBAAiB,GAAIC,IAAI,IAAK;IAClC,IAAI,CAACA,IAAI,EAAE,OAAOA,IAAI;IAEtB,MAAMC,SAAS,GAAG;MAAE,GAAGD;IAAK,CAAC;;IAE7B;IACA,IAAI,OAAOC,SAAS,CAACC,IAAI,KAAK,QAAQ,EAAE;MACtC,IAAI;QACFD,SAAS,CAACC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,SAAS,CAACC,IAAI,CAAC;QAC3CpB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEkB,SAAS,CAACC,IAAI,CAAC;MAC7C,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdvB,OAAO,CAACuB,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3CJ,SAAS,CAACC,IAAI,GAAG,EAAE;MACrB;IACF;;IAEA;IACA,IAAI,OAAOD,SAAS,CAACK,WAAW,KAAK,QAAQ,EAAE;MAC7C,IAAI;QACFL,SAAS,CAACK,WAAW,GAAGH,IAAI,CAACC,KAAK,CAACH,SAAS,CAACK,WAAW,CAAC;QACzDxB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEkB,SAAS,CAACK,WAAW,CAAC;MAC3D,CAAC,CAAC,OAAOD,KAAK,EAAE;QACdvB,OAAO,CAACuB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClDJ,SAAS,CAACK,WAAW,GAAG,EAAE;MAC5B;IACF;IAEA,OAAOL,SAAS;EAClB,CAAC;EAEDtE,SAAS,CAAC,MAAM;IACd4E,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,CAACd,QAAQ,CAAC,CAAC;EAEd,eAAec,gBAAgBA,CAAA,EAAG;IAChC,IAAI;MACFT,YAAY,CAAC,IAAI,CAAC;MAClBhB,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;MAC1DD,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE;QACpCxB,MAAM,EAAEA,MAAM;QACdiD,SAAS,EAAEf;MACb,CAAC,CAAC;MAEF,MAAMgB,QAAQ,GAAG,MAAM3D,sBAAsB,CAAC;QAC5CS,MAAM,EAAEA,MAAM;QACdiD,SAAS,EAAEf;MACb,CAAC,CAAC;MAEFX,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE0B,QAAQ,CAAC;MAClD3B,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE0B,QAAQ,CAACC,OAAO,CAAC;MACrD5B,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE0B,QAAQ,CAACT,IAAI,CAAC;MAE/C,IAAIS,QAAQ,CAACC,OAAO,EAAE;QACpB5B,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;QACpC;QACA,MAAM4B,aAAa,GAAGZ,iBAAiB,CAACU,QAAQ,CAACT,IAAI,CAAC;QACtDlB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE4B,aAAa,CAAC;QACvD7B,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE4B,aAAa,CAACC,WAAW,CAAC;QACzD9B,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE4B,aAAa,CAACE,OAAO,CAAC;QACxDjB,aAAa,CAACe,aAAa,CAAC;QAC5B7B,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;MAC3C,CAAC,MAAM;QACLD,OAAO,CAACuB,KAAK,CAAC,oBAAoB,EAAEI,QAAQ,CAACK,OAAO,CAAC;QACrDtE,KAAK,CAAC6D,KAAK,CAAC,+BAA+B,CAAC;MAC9C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdvB,OAAO,CAACuB,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDvB,OAAO,CAACuB,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAACS,OAAO,CAAC;MACjDhC,OAAO,CAACuB,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAACU,KAAK,CAAC;MAC7CvE,KAAK,CAAC6D,KAAK,CAAC,8BAA8B,CAAC;IAC7C,CAAC,SAAS;MACRP,YAAY,CAAC,KAAK,CAAC;MACnBhB,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;IACvD;EACF;EAEA,eAAeiC,gBAAgBA,CAAA,EAAG;IAChC,IAAI;MACF5B,cAAc,CAAC,IAAI,CAAC;;MAEpB;MACA,MAAM,IAAI6B,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MAEvDpC,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;;MAE1C;MACAvC,KAAK,CAACkE,OAAO,CAAC,yBAAyB,EAAE;QACvCU,QAAQ,EAAE,WAAW;QACrBC,SAAS,EAAE;MACb,CAAC,CAAC;;MAEF;MACA,MAAM,IAAIJ,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;MAEvD;MACAlC,QAAQ,CAAC,QAAQ,EAAE;QACjBsC,KAAK,EAAE;UACLC,UAAU,EAAE,yBAAyBhC,SAAS,EAAE;UAChDuB,OAAO,EAAE;QACX;MACF,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdvB,OAAO,CAACuB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD7D,KAAK,CAAC6D,KAAK,CAAC,+CAA+C,CAAC;IAC9D,CAAC,SAAS;MACRjB,cAAc,CAAC,KAAK,CAAC;IACvB;EACF;EAEA,MAAMoC,YAAY,GAAIC,QAAQ,IAAK;IACjCvC,kBAAkB,CAACwC,IAAI,IACrBA,IAAI,CAACC,QAAQ,CAACF,QAAQ,CAAC,GACnBC,IAAI,CAACE,MAAM,CAAClC,EAAE,IAAIA,EAAE,KAAK+B,QAAQ,CAAC,GAClC,CAAC,GAAGC,IAAI,EAAED,QAAQ,CACxB,CAAC;EACH,CAAC;EAED,MAAMI,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvCvC,kBAAkB,CAAC,IAAI,CAAC;;IAExB;IACA,MAAM,IAAI2B,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;IAEvD;IACApC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE;MACtCU,QAAQ;MACRE;IACF,CAAC,CAAC;;IAEF;IACAnD,KAAK,CAACkE,OAAO,CAAC,yBAAyB,EAAE;MACvCU,QAAQ,EAAE,WAAW;MACrBC,SAAS,EAAE;IACb,CAAC,CAAC;;IAEF;IACA,MAAM,IAAIJ,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;IAEvDlC,QAAQ,CAAC,QAAQ,EAAE;MACjBsC,KAAK,EAAE;QACLC,UAAU,EAAE,yBAAyBhC,SAAS,EAAE;QAChDuB,OAAO,EAAE;MACX;IACF,CAAC,CAAC;IAEFxB,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC;EAED,IAAIO,SAAS,EAAE;IACbf,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC5C,oBACE9B,OAAA;MAAK6E,SAAS,EAAC,kDAAkD;MAACC,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAO,CAAE;MAAAC,QAAA,eAC7FhF,OAAA;QAAK6E,SAAS,EAAC,6BAA6B;QAACI,IAAI,EAAC,QAAQ;QAAAD,QAAA,eACxDhF,OAAA;UAAM6E,SAAS,EAAC,iBAAiB;UAAAG,QAAA,EAAC;QAAU;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAAC3C,UAAU,EAAE;IACfb,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;IACzC,oBACE9B,OAAA;MAAK6E,SAAS,EAAC,kDAAkD;MAACC,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAO,CAAE;MAAAC,QAAA,eAC7FhF,OAAA;QAAK6E,SAAS,EAAC,aAAa;QAAAG,QAAA,gBAC1BhF,OAAA;UAAAgF,QAAA,EAAI;QAAgB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzBrF,OAAA;UAAG6E,SAAS,EAAC,YAAY;UAAAG,QAAA,EAAC;QAAgE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC9FrF,OAAA;UAAQ6E,SAAS,EAAC,iBAAiB;UAACS,OAAO,EAAEA,CAAA,KAAMvD,QAAQ,CAAC,GAAG,CAAE;UAAAiD,QAAA,EAAC;QAElE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEAxD,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEY,UAAU,CAAC;EAEjE,MAAM6C,UAAU,GAAG7C,UAAU,CAACkB,OAAO,IACnC4B,MAAM,CAACC,IAAI,CAAC/C,UAAU,CAACkB,OAAO,CAAC,CAAC8B,MAAM,GAAG,CAAC;EAE5C,oBACE1F,OAAA,CAAAE,SAAA;IAAA8E,QAAA,eACEhF,OAAA;MAAK6E,SAAS,EAAC,KAAK;MAAAG,QAAA,eAClBhF,OAAA;QAAK6E,SAAS,EAAC,YAAY;QAAAG,QAAA,gBACzBhF,OAAA;UAAK6E,SAAS,EAAC,uBAAuB;UAAAG,QAAA,eACpChF,OAAA;YAAK6E,SAAS,EAAC,WAAW;YAAAG,QAAA,gBACxBhF,OAAA;cAAK6E,SAAS,EAAC,uDAAuD;cAAAG,QAAA,gBACpEhF,OAAA;gBAAI6E,SAAS,EAAC,cAAc;gBAAAG,QAAA,EAAEtC,UAAU,CAACiB,WAAW,IAAI;cAAiB;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/ErF,OAAA;gBAAK6E,SAAS,EAAC,cAAc;gBAAAG,QAAA,gBAC3BhF,OAAA;kBACE6E,SAAS,EAAC,gCAAgC;kBAC1CS,OAAO,EAAEA,CAAA,KAAMvD,QAAQ,CAAC,QAAQ,CAAE;kBAAAiD,QAAA,gBAElChF,OAAA,CAACR,IAAI;oBAACmG,IAAI,EAAC,wBAAwB;oBAACd,SAAS,EAAC;kBAAM;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,mBAEzD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTrF,OAAA;kBACE6E,SAAS,EAAC,kCAAkC;kBAC5CS,OAAO,EAAEA,CAAA,KAAMvD,QAAQ,CAAC,WAAW,CAAE;kBAAAiD,QAAA,gBAErChF,OAAA,CAACR,IAAI;oBAACmG,IAAI,EAAC,6BAA6B;oBAACd,SAAS,EAAC;kBAAM;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,WAE9D;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNrF,OAAA;cAAG6E,SAAS,EAAC,aAAa;cAAAG,QAAA,EACvBtC,UAAU,CAACO,IAAI,IAAI2C,KAAK,CAACC,OAAO,CAACnD,UAAU,CAACO,IAAI,CAAC,GAChDP,UAAU,CAACO,IAAI,CAAC6C,IAAI,CAAC,KAAK,CAAC,GAE3B;YACD;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACJrF,OAAA;cAAK6E,SAAS,EAAC,gDAAgD;cAAAG,QAAA,gBAC7DhF,OAAA;gBAAK6E,SAAS,EAAC,2BAA2B;gBAAAG,QAAA,gBACxChF,OAAA,CAACR,IAAI;kBAACmG,IAAI,EAAC,uBAAuB;kBAACd,SAAS,EAAC;gBAAc;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9DrF,OAAA;kBAAM6E,SAAS,EAAC,MAAM;kBAAAG,QAAA,GAAE,CAAAtC,UAAU,aAAVA,UAAU,wBAAA9B,qBAAA,GAAV8B,UAAU,CAAEqD,UAAU,cAAAnF,qBAAA,uBAAtBA,qBAAA,CAAwBoF,aAAa,KAAI,KAAK,EAAC,SAAO;gBAAA;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClF,CAAC,eACNrF,OAAA;gBAAK6E,SAAS,EAAC,2BAA2B;gBAAAG,QAAA,gBACxChF,OAAA,CAACR,IAAI;kBAACmG,IAAI,EAAC,4BAA4B;kBAACd,SAAS,EAAC;gBAAM;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC3DrF,OAAA;kBAAAgF,QAAA,GAAO,CAAAtC,UAAU,aAAVA,UAAU,wBAAA7B,sBAAA,GAAV6B,UAAU,CAAEqD,UAAU,cAAAlF,sBAAA,uBAAtBA,sBAAA,CAAwBoF,YAAY,KAAI,CAAC,EAAC,UAAQ;gBAAA;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC,eACNrF,OAAA;gBAAK6E,SAAS,EAAC,2BAA2B;gBAAAG,QAAA,gBACxChF,OAAA,CAACR,IAAI;kBAACmG,IAAI,EAAC,yBAAyB;kBAACd,SAAS,EAAC;gBAAM;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACxDrF,OAAA;kBAAAgF,QAAA,GAAM,YAAU,EAAC,CAAAtC,UAAU,aAAVA,UAAU,wBAAA5B,sBAAA,GAAV4B,UAAU,CAAEqD,UAAU,cAAAjF,sBAAA,uBAAtBA,sBAAA,CAAwBoF,kBAAkB,KAAI,UAAU;gBAAA;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC,eACNrF,OAAA;gBAAK6E,SAAS,EAAC,2BAA2B;gBAAAG,QAAA,gBACxChF,OAAA,CAACR,IAAI;kBAACmG,IAAI,EAAC,yBAAyB;kBAACd,SAAS,EAAC;gBAAM;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACxDrF,OAAA;kBAAAgF,QAAA,GAAM,SAAO,EAAC,CAAAtC,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEyD,MAAM,KAAI,eAAe;gBAAA;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC,eACNrF,OAAA;gBAAK6E,SAAS,EAAC,2BAA2B;gBAAAG,QAAA,gBACxChF,OAAA,CAACR,IAAI;kBAACmG,IAAI,EAAC,2BAA2B;kBAACd,SAAS,EAAC;gBAAM;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC1DrF,OAAA;kBAAAgF,QAAA,GAAM,YAAU,EAAC,CAAAtC,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE0D,eAAe,KAAI,eAAe;gBAAA;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNrF,OAAA;cAAK6E,SAAS,EAAC,4CAA4C;cAAAG,QAAA,gBACzDhF,OAAA;gBAAK6E,SAAS,EAAC,kBAAkB;gBAACC,KAAK,EAAE;kBAAEuB,KAAK,EAAE,MAAM;kBAAEC,MAAM,EAAE,MAAM;kBAAEC,QAAQ,EAAE;gBAAO,CAAE;gBAAAvB,QAAA,eAC3FhF,OAAA;kBACEwG,GAAG,EAAE,CAAA9D,UAAU,aAAVA,UAAU,wBAAA3B,mBAAA,GAAV2B,UAAU,CAAE+D,OAAO,cAAA1F,mBAAA,uBAAnBA,mBAAA,CAAqB2F,OAAO,KAAIhH,cAAe;kBACpDiH,GAAG,EAAEjE,UAAU,aAAVA,UAAU,wBAAA1B,oBAAA,GAAV0B,UAAU,CAAE+D,OAAO,cAAAzF,oBAAA,uBAAnBA,oBAAA,CAAqB4F,IAAK;kBAC/B/B,SAAS,EAAC,gBAAgB;kBAC1BC,KAAK,EAAE;oBAAEuB,KAAK,EAAE,MAAM;oBAAEC,MAAM,EAAE,MAAM;oBAAEO,SAAS,EAAE;kBAAQ;gBAAE;kBAAA3B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNrF,OAAA;gBAAK6E,SAAS,EAAC,oBAAoB;gBAAAG,QAAA,gBACjChF,OAAA;kBAAM6E,SAAS,EAAC,aAAa;kBAAAG,QAAA,GAAC,KAAG,EAAC,CAAAtC,UAAU,aAAVA,UAAU,wBAAAzB,oBAAA,GAAVyB,UAAU,CAAE+D,OAAO,cAAAxF,oBAAA,uBAAnBA,oBAAA,CAAqB2F,IAAI,KAAI,YAAY;gBAAA;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnFrF,OAAA;kBAAM6E,SAAS,EAAC,aAAa;kBAAAG,QAAA,EAAE,CAAAtC,UAAU,aAAVA,UAAU,wBAAAxB,oBAAA,GAAVwB,UAAU,CAAE+D,OAAO,cAAAvF,oBAAA,uBAAnBA,oBAAA,CAAqB+D,IAAI,KAAI;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENrF,OAAA;UAAK6E,SAAS,EAAC,4BAA4B;UAAAG,QAAA,eACzChF,OAAA;YAAK6E,SAAS,EAAC,kBAAkB;YAAAG,QAAA,eAC/BhF,OAAA;cAAK6E,SAAS,EAAC,0BAA0B;cAAAG,QAAA,eACvChF,OAAA;gBAAK6E,SAAS,EAAC,KAAK;gBAAAG,QAAA,gBAClBhF,OAAA;kBAAK6E,SAAS,EAAC,UAAU;kBAAAG,QAAA,gBACvBhF,OAAA;oBAAK6E,SAAS,EAAC,uBAAuB;oBAAAG,QAAA,gBACpChF,OAAA;sBAAI6E,SAAS,EAAC,SAAS;sBAAAG,QAAA,EAAC;oBAAW;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACxCrF,OAAA;sBAAK6E,SAAS,EAAC,iBAAiB;sBAAAG,QAAA,EAC7BtC,UAAU,CAACW,WAAW,IAAIuC,KAAK,CAACC,OAAO,CAACnD,UAAU,CAACW,WAAW,CAAC,GAC9DX,UAAU,CAACW,WAAW,CAACyD,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBACtChH,OAAA;wBAAiB6E,SAAS,EAAC,+CAA+C;wBAAAG,QAAA,gBACxEhF,OAAA,CAACR,IAAI;0BAACmG,IAAI,EAAC,+BAA+B;0BAACd,SAAS,EAAC;wBAAmB;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC3ErF,OAAA;0BAAAgF,QAAA,EAAO+B;wBAAK;0BAAA7B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA,GAFZ2B,KAAK;wBAAA9B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAGV,CACN,CAAC,gBAEFrF,OAAA;wBAAK6E,SAAS,EAAC,+CAA+C;wBAAAG,QAAA,gBAC5DhF,OAAA,CAACR,IAAI;0BAACmG,IAAI,EAAC,+BAA+B;0BAACd,SAAS,EAAC;wBAAmB;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC3ErF,OAAA;0BAAAgF,QAAA,EAAM;wBAAgC;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1C;oBACN;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,EAEL3C,UAAU,CAACuE,WAAW,gBACrBjH,OAAA;oBAAK6E,SAAS,EAAC,mBAAmB;oBAAAG,QAAA,gBAChChF,OAAA;sBAAI6E,SAAS,EAAC,SAAS;sBAAAG,QAAA,EAAC;oBAAiB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC9CrF,OAAA;sBAAK6E,SAAS,EAAC,eAAe;sBAAAG,QAAA,eAC5BhF,OAAA;wBACE6E,SAAS,EAAC,iBAAiB;wBAC3BC,KAAK,EAAE;0BACLoC,SAAS,EAAE,YAAY;0BACvBC,UAAU,EAAE;wBACd,CAAE;wBAAAnC,QAAA,EAEDtC,UAAU,CAACuE;sBAAW;wBAAA/B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,GACJ,IAAI,eAERrF,OAAA;oBAAK6E,SAAS,EAAC,iCAAiC;oBAAAG,QAAA,gBAC9ChF,OAAA;sBAAI6E,SAAS,EAAC,SAAS;sBAAAG,QAAA,EAAC;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,EAC1CE,UAAU,gBACTvF,OAAA;sBAAK6E,SAAS,EAAC,cAAc;sBAAAG,QAAA,EAC1BQ,MAAM,CAAC4B,OAAO,CAAC1E,UAAU,CAACkB,OAAO,CAAC,CAACkD,GAAG,CAAC,CAAC,CAACO,UAAU,EAAEC,QAAQ,CAAC,kBAC7DtH,OAAA;wBAAsB6E,SAAS,EAAC,aAAa;wBAAAG,QAAA,gBAC3ChF,OAAA;0BACE6E,SAAS,EAAC,qEAAqE;0BAC/ES,OAAO,EAAEA,CAAA,KAAMf,YAAY,CAAC8C,UAAU,CAAE;0BACxCvC,KAAK,EAAE;4BAAEyC,MAAM,EAAE;0BAAU,CAAE;0BAAAvC,QAAA,gBAE7BhF,OAAA;4BAAK6E,SAAS,EAAC,2BAA2B;4BAAAG,QAAA,GACvChD,eAAe,CAAC0C,QAAQ,CAAC2C,UAAU,CAAC,gBAAGrH,OAAA,CAACd,aAAa;8BAAAgG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,gBAAGrF,OAAA,CAACb,WAAW;8BAAA+F,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,eAC3ErF,OAAA;8BAAM6E,SAAS,EAAC,MAAM;8BAAAG,QAAA,EAAEqC;4BAAU;8BAAAnC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACvC,CAAC,eACNrF,OAAA;4BAAK6E,SAAS,EAAC,wBAAwB;4BAAAG,QAAA,eACrChF,OAAA;8BAAAgF,QAAA,GAAQsC,QAAQ,CAAC5B,MAAM,EAAC,QAAM;4BAAA;8BAAAR,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACnC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,EACLrD,eAAe,CAAC0C,QAAQ,CAAC2C,UAAU,CAAC,iBACnCrH,OAAA;0BAAK6E,SAAS,EAAC,gBAAgB;0BAAAG,QAAA,EAC5BsC,QAAQ,CAACR,GAAG,CAAC,CAACU,OAAO,EAAEC,GAAG,kBACzBzH,OAAA;4BAAe6E,SAAS,EAAC,oEAAoE;4BAAAG,QAAA,gBAC3FhF,OAAA;8BAAK6E,SAAS,EAAC,2BAA2B;8BAAAG,QAAA,GACvCwC,OAAO,CAACE,IAAI,KAAK,OAAO,iBACvB1H,OAAA,CAACR,IAAI;gCAACmG,IAAI,EAAC,sCAAsC;gCAACd,SAAS,EAAC;8BAAM;gCAAAK,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CACrE,EACAmC,OAAO,CAACE,IAAI,KAAK,UAAU,iBAC1B1H,OAAA,CAACR,IAAI;gCAACmG,IAAI,EAAC,sCAAsC;gCAACd,SAAS,EAAC;8BAAM;gCAAAK,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CACrE,EACAmC,OAAO,CAACE,IAAI,KAAK,YAAY,iBAC5B1H,OAAA,CAACR,IAAI;gCAACmG,IAAI,EAAC,+BAA+B;gCAACd,SAAS,EAAC;8BAAM;gCAAAK,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAC9D,EACAmC,OAAO,CAACE,IAAI,KAAK,QAAQ,iBACxB1H,OAAA,CAACR,IAAI;gCAACmG,IAAI,EAAC,oCAAoC;gCAACd,SAAS,EAAC;8BAAM;gCAAAK,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CACnE,eACDrF,OAAA;gCAAAgF,QAAA,EAAOwC,OAAO,CAACG;8BAAK;gCAAAzC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACzB,CAAC,eACNrF,OAAA;8BAAK6E,SAAS,EAAC,2BAA2B;8BAAAG,QAAA,GACvCwC,OAAO,CAACE,IAAI,KAAK,OAAO,iBACvB1H,OAAA;gCAAM6E,SAAS,EAAC,iBAAiB;gCAAAG,QAAA,EAAEwC,OAAO,CAACI;8BAAQ;gCAAA1C,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAC3D,EACAmC,OAAO,CAACE,IAAI,KAAK,UAAU,iBAC1B1H,OAAA;gCAAM6E,SAAS,EAAC,iBAAiB;gCAAAG,QAAA,EAAEwC,OAAO,CAACK;8BAAQ;gCAAA3C,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAC3D,EACAmC,OAAO,CAACE,IAAI,KAAK,YAAY,IAAIF,OAAO,CAACM,SAAS,iBACjD9H,OAAA;gCAAM6E,SAAS,EAAC,iBAAiB;gCAAAG,QAAA,EAAEwC,OAAO,CAACM;8BAAS;gCAAA5C,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAO,CAC5D;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACE,CAAC;0BAAA,GA1BEoC,GAAG;4BAAAvC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OA2BR,CACN;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CACN;sBAAA,GA/COgC,UAAU;wBAAAnC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAgDf,CACN;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,gBAENrF,OAAA,CAACF,MAAM;sBAACiI,OAAO,EAAC;oBAAiC;sBAAA7C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CACpD;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,EAEL3C,UAAU,CAAC+D,OAAO,gBACjBzG,OAAA;oBAAK6E,SAAS,EAAC,yBAAyB;oBAAAG,QAAA,gBACtChF,OAAA;sBAAI6E,SAAS,EAAC,SAAS;sBAAAG,QAAA,EAAC;oBAAU;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACvCrF,OAAA;sBAAK6E,SAAS,EAAC,sCAAsC;sBAAAG,QAAA,gBACnDhF,OAAA;wBACE6E,SAAS,EAAC,uCAAuC;wBACjDC,KAAK,EAAE;0BACLuB,KAAK,EAAE,OAAO;0BACdC,MAAM,EAAE,OAAO;0BACfC,QAAQ,EAAE,OAAO;0BACjByB,QAAQ,EAAE,QAAQ;0BAClBC,YAAY,EAAE,MAAM;0BACpB9D,QAAQ,EAAE,UAAU;0BACpB+D,eAAe,EAAE;wBACnB,CAAE;wBAAAlD,QAAA,eAEFhF,OAAA;0BACEwG,GAAG,EAAE,CAAA9D,UAAU,aAAVA,UAAU,wBAAAvB,oBAAA,GAAVuB,UAAU,CAAE+D,OAAO,cAAAtF,oBAAA,uBAAnBA,oBAAA,CAAqBuF,OAAO,KAAIhH,cAAe;0BACpDiH,GAAG,EAAEjE,UAAU,aAAVA,UAAU,wBAAAtB,oBAAA,GAAVsB,UAAU,CAAE+D,OAAO,cAAArF,oBAAA,uBAAnBA,oBAAA,CAAqBwF,IAAK;0BAC/B9B,KAAK,EAAE;4BACLuB,KAAK,EAAE,MAAM;4BACbC,MAAM,EAAE,MAAM;4BACdO,SAAS,EAAE;0BACb;wBAAE;0BAAA3B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC,eACNrF,OAAA;wBAAK6E,SAAS,EAAC,iBAAiB;wBAAAG,QAAA,gBAC9BhF,OAAA;0BAAI6E,SAAS,EAAC,SAAS;0BAAAG,QAAA,EAAE,CAAAtC,UAAU,aAAVA,UAAU,wBAAArB,oBAAA,GAAVqB,UAAU,CAAE+D,OAAO,cAAApF,oBAAA,uBAAnBA,oBAAA,CAAqBuF,IAAI,KAAI;wBAAY;0BAAA1B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACxErF,OAAA;0BAAG6E,SAAS,EAAC,iBAAiB;0BAAAG,QAAA,EAC3B,CAAAtC,UAAU,aAAVA,UAAU,wBAAApB,oBAAA,GAAVoB,UAAU,CAAE+D,OAAO,cAAAnF,oBAAA,uBAAnBA,oBAAA,CAAqB2D,IAAI,KAAI;wBAAY;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzC,CAAC,eACJrF,OAAA;0BAAG6E,SAAS,EAAC,iBAAiB;0BAAAG,QAAA,EAC3B,CAAAtC,UAAU,aAAVA,UAAU,wBAAAnB,oBAAA,GAAVmB,UAAU,CAAE+D,OAAO,cAAAlF,oBAAA,uBAAnBA,oBAAA,CAAqB4G,GAAG,KAAI;wBAAkB;0BAAAjD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9C,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,gBAENrF,OAAA,CAACF,MAAM;oBAACiI,OAAO,EAAC;kBAAsC;oBAAA7C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CACzD;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAENrF,OAAA;kBAAK6E,SAAS,EAAC,UAAU;kBAAAG,QAAA,eACvBhF,OAAA;oBAAK6E,SAAS,EAAC,4BAA4B;oBAACC,KAAK,EAAE;sBAAEX,QAAQ,EAAE,UAAU;sBAAEiE,GAAG,EAAE;oBAAQ,CAAE;oBAAApD,QAAA,gBACxFhF,OAAA;sBACEwG,GAAG,EAAE9D,UAAU,CAAC2F,YAAY,IAAI5I,SAAU;sBAC1CoF,SAAS,EAAC,wBAAwB;sBAClC8B,GAAG,EAAEjE,UAAU,CAACiB,WAAW,IAAI,gBAAiB;sBAChD2E,OAAO,EAAGC,CAAC,IAAK;wBACdA,CAAC,CAACC,MAAM,CAAChC,GAAG,GAAG/G,SAAS;wBACxB8I,CAAC,CAACC,MAAM,CAACC,OAAO,GAAG,IAAI;sBACzB;oBAAE;sBAAAvD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACFrF,OAAA;sBAAK6E,SAAS,EAAC,WAAW;sBAAAG,QAAA,gBACxBhF,OAAA;wBAAK6E,SAAS,EAAC,wDAAwD;wBAAAG,QAAA,gBACrEhF,OAAA;0BAAM6E,SAAS,EAAC,WAAW;0BAAAG,QAAA,GAAE7E,iBAAiB,CAACuC,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEtC,QAAQ,CAAC,EAAC,GAAC,EAAC,CAAAsC,UAAU,aAAVA,UAAU,wBAAAlB,qBAAA,GAAVkB,UAAU,CAAEgG,WAAW,cAAAlH,qBAAA,uBAAvBA,qBAAA,CAAyBmH,WAAW,CAAC,CAAC,MAAK,MAAM,GAAG,GAAG,GAAG,CAAAjG,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEkG,YAAY,KAAI,GAAG;wBAAA;0BAAA1D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,EACvK,CAAA3C,UAAU,aAAVA,UAAU,wBAAAjB,sBAAA,GAAViB,UAAU,CAAEgG,WAAW,cAAAjH,sBAAA,uBAAvBA,sBAAA,CAAyBkH,WAAW,CAAC,CAAC,MAAK,MAAM,gBAChD3I,OAAA;0BAAM6E,SAAS,EAAC,gBAAgB;0BAAAG,QAAA,GAAE7E,iBAAiB,CAACuC,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEtC,QAAQ,CAAC,EAAC,GAAC;wBAAA;0BAAA8E,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,gBAElFrF,OAAA;0BAAM6E,SAAS,EAAC,gBAAgB;0BAAAG,QAAA,GAAE7E,iBAAiB,CAACuC,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEtC,QAAQ,CAAC,EAAEyI,MAAM,CAAC,CAAAnG,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEkG,YAAY,KAAI,CAAC,CAAC,GAAG,CAAC;wBAAA;0BAAA1D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAC5H;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,EAEL,CAAA3C,UAAU,aAAVA,UAAU,wBAAAhB,sBAAA,GAAVgB,UAAU,CAAEgG,WAAW,cAAAhH,sBAAA,uBAAvBA,sBAAA,CAAyBiH,WAAW,CAAC,CAAC,MAAK,MAAM,gBAChD3I,OAAA;wBACEsF,OAAO,EAAEV,oBAAqB;wBAC9BC,SAAS,EAAC,UAAU;wBACpBiE,QAAQ,EAAE1G,eAAgB;wBAAA4C,QAAA,EAEzB5C,eAAe,gBACdpC,OAAA,CAAAE,SAAA;0BAAA8E,QAAA,gBACEhF,OAAA;4BAAM6E,SAAS,EAAC,uCAAuC;4BAACI,IAAI,EAAC,QAAQ;4BAAC,eAAY;0BAAM;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC,iBAElG;wBAAA,eAAE,CAAC,gBAEHrF,OAAA,CAAAE,SAAA;0BAAA8E,QAAA,gBACEhF,OAAA,CAACR,IAAI;4BACHmG,IAAI,EAAC,UAAU;4BACfd,SAAS,EAAC;0BAAU;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrB,CAAC,cAEJ;wBAAA,eAAE;sBACH;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACK,CAAC,gBAETrF,OAAA;wBACE6E,SAAS,EAAC,wBAAwB;wBAClCS,OAAO,EAAEvB,gBAAiB;wBAC1B+E,QAAQ,EAAE5G,WAAY;wBAAA8C,QAAA,EAErB9C,WAAW,gBACVlC,OAAA,CAAAE,SAAA;0BAAA8E,QAAA,gBACEhF,OAAA;4BAAM6E,SAAS,EAAC,uCAAuC;4BAACI,IAAI,EAAC,QAAQ;4BAAC,eAAY;0BAAM;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC,gBAElG;wBAAA,eAAE,CAAC,gBAEHrF,OAAA,CAAAE,SAAA;0BAAA8E,QAAA,gBACEhF,OAAA,CAACR,IAAI;4BACHmG,IAAI,EAAC,iBAAiB;4BACtBd,SAAS,EAAC;0BAAU;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrB,CAAC,aAEJ;wBAAA,eAAE;sBACH;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACK,CACT,eAEDrF,OAAA;wBAAK6E,SAAS,EAAC,kDAAkD;wBAAAG,QAAA,gBAC/DhF,OAAA,CAACnB,UAAU;0BAACgG,SAAS,EAAC;wBAAiB;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,wBAE5C;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC,eAENrF,OAAA;wBAAK6E,SAAS,EAAC,iBAAiB;wBAAAG,QAAA,gBAC9BhF,OAAA;0BAAI6E,SAAS,EAAC,SAAS;0BAAAG,QAAA,EAAC;wBAAqB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,EACjDE,UAAU,gBACTvF,OAAA;0BAAI6E,SAAS,EAAC,eAAe;0BAAAG,QAAA,gBAC3BhF,OAAA;4BAAI6E,SAAS,EAAC,gCAAgC;4BAAAG,QAAA,gBAC5ChF,OAAA,CAACR,IAAI;8BAACmG,IAAI,EAAC,4BAA4B;8BAACd,SAAS,EAAC;4BAAiB;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,EACrE,CAAA3C,UAAU,aAAVA,UAAU,wBAAAf,sBAAA,GAAVe,UAAU,CAAEqD,UAAU,cAAApE,sBAAA,uBAAtBA,sBAAA,CAAwBsE,YAAY,KAAI,CAAC,EAAC,UAC7C;0BAAA;4BAAAf,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACLrF,OAAA;4BAAI6E,SAAS,EAAC,gCAAgC;4BAAAG,QAAA,gBAC5ChF,OAAA,CAACrB,OAAO;8BAACkG,SAAS,EAAC;4BAAiB;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,EACtC,CAAA3C,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEkB,OAAO,KAAI4B,MAAM,CAACuD,MAAM,CAACrG,UAAU,CAACkB,OAAO,CAAC,CAACoF,IAAI,CAAC,CAAC,CAACrE,MAAM,CAACsE,IAAI,IAAIA,IAAI,CAACvB,IAAI,KAAK,OAAO,CAAC,CAAChC,MAAM,IAAI,CAAC,EAAC,iBAAU,EAAC,CAAAhD,UAAU,aAAVA,UAAU,wBAAAd,sBAAA,GAAVc,UAAU,CAAEqD,UAAU,cAAAnE,sBAAA,uBAAtBA,sBAAA,CAAwBsE,kBAAkB,KAAI,UAAU,EAAC,iBACzL;0BAAA;4BAAAhB,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACLrF,OAAA;4BAAI6E,SAAS,EAAC,gCAAgC;4BAAAG,QAAA,gBAC5ChF,OAAA,CAACR,IAAI;8BAACmG,IAAI,EAAC,uBAAuB;8BAACd,SAAS,EAAC;4BAAiB;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,EAChE,CAAA3C,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEkB,OAAO,KAAI4B,MAAM,CAACuD,MAAM,CAACrG,UAAU,CAACkB,OAAO,CAAC,CAACoF,IAAI,CAAC,CAAC,CAACrE,MAAM,CAACsE,IAAI,IAAIA,IAAI,CAACvB,IAAI,KAAK,YAAY,CAAC,CAAChC,MAAM,IAAI,CAAC,EAAC,cAC1H;0BAAA;4BAAAR,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACLrF,OAAA;4BAAI6E,SAAS,EAAC,gCAAgC;4BAAAG,QAAA,gBAC5ChF,OAAA,CAACR,IAAI;8BAACmG,IAAI,EAAC,sCAAsC;8BAACd,SAAS,EAAC;4BAAiB;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,EAC/E,CAAA3C,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEkB,OAAO,KAAI4B,MAAM,CAACuD,MAAM,CAACrG,UAAU,CAACkB,OAAO,CAAC,CAACoF,IAAI,CAAC,CAAC,CAACrE,MAAM,CAACsE,IAAI,IAAIA,IAAI,CAACvB,IAAI,KAAK,UAAU,CAAC,CAAChC,MAAM,IAAI,CAAC,EAAC,YACxH;0BAAA;4BAAAR,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACLrF,OAAA;4BAAI6E,SAAS,EAAC,gCAAgC;4BAAAG,QAAA,gBAC5ChF,OAAA,CAACR,IAAI;8BAACmG,IAAI,EAAC,oCAAoC;8BAACd,SAAS,EAAC;4BAAiB;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,EAC7E,CAAA3C,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEkB,OAAO,KAAI4B,MAAM,CAACuD,MAAM,CAACrG,UAAU,CAACkB,OAAO,CAAC,CAACoF,IAAI,CAAC,CAAC,CAACrE,MAAM,CAACsE,IAAI,IAAIA,IAAI,CAACvB,IAAI,KAAK,QAAQ,CAAC,CAAChC,MAAM,IAAI,CAAC,EAAC,UACtH;0BAAA;4BAAAR,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACLrF,OAAA;4BAAI6E,SAAS,EAAC,gCAAgC;4BAAAG,QAAA,gBAC5ChF,OAAA,CAACnB,UAAU;8BAACgG,SAAS,EAAC;4BAAiB;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,wBAE5C;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACLrF,OAAA;4BAAI6E,SAAS,EAAC,gCAAgC;4BAAAG,QAAA,gBAC5ChF,OAAA,CAAClB,aAAa;8BAAC+F,SAAS,EAAC;4BAAiB;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,6BAE/C;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,gBAELrF,OAAA;0BAAG6E,SAAS,EAAC,YAAY;0BAAAG,QAAA,EAAC;wBAAgC;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAC9D;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC,gBACN,CAAC;AAEP;AAAC1E,EAAA,CAxhBQD,mBAAmB;EAAA,QAETrB,WAAW,EAKNC,SAAS;AAAA;AAAA4J,EAAA,GAPxBxI,mBAAmB;AA0hB5B,eAAeA,mBAAmB;AAAC,IAAAwI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}