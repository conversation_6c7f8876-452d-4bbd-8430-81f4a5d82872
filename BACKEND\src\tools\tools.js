
const bcrypt = require('bcrypt');
const { validationResult } = require('express-validator');
const jwt = require('jsonwebtoken');
const { mysqlServerConnection } = require('../db/db');
const multer = require('multer');
require('dotenv').config();
const nodemailer = require('nodemailer');
const path = require('path');
const maindb = process.env.MAIN_DB;
const PDFDocument = require('pdfkit');



async function hashPassword(password) {
  if (password === "") {
    throw new Error('Password is required');
  }
  return await bcrypt.hash(password, 10);
}

const Validate = (req, res) => {
  const errors = validationResult(req);
  console.log(errors)
  if (!errors.isEmpty()) {
    return res.status(401).json({
      success: false,
      data: {
        error_msg: 'Validation errors',
        response: errors.array().reduce((acc, error) => {
          acc[error.path] = error.msg
          return acc
        }, {})
      }
    });
  }
  return null
}

const generateAccessToken = async (user) => {
  try {
    const [permissionsResult] = await mysqlServerConnection.query(
      `SELECT p.name 
            FROM ${user.db_name}.roles r 
            JOIN ${user.db_name}.role_permissions rp ON r.id = rp.role_id 
            JOIN ${maindb}.permissions p ON rp.permission_id = p.id 
            WHERE r.id = ?`,
      [user.role_id]
    );

    // Extract permission names
    const permissions = permissionsResult.map(permission => permission.name);

    // Include user information and permissions in the token payload
    const payload = {
      userId: user.id,
      name: user.name,
      db_name: user.db_name,
      username: user.username,
      permissions: permissions
    };
    return jwt.sign(payload, process.env.JWT_ACCESS_KEY, { expiresIn: '3d' });
  } catch (error) {
    console.error('Error generating access token:', error);
    throw new Error('Could not generate access token');
  }
};

const generateRefreshToken = async (user) => {
  const [permissionsResult] = await mysqlServerConnection.query(
    `SELECT p.name 
        FROM ${user.db_name}.roles r 
        JOIN ${user.db_name}.role_permissions rp ON r.id = rp.role_id 
        JOIN ${maindb}.permissions p ON rp.permission_id = p.id 
        WHERE r.id = ?`,
    [user.role_id]
  );

  const permissions = permissionsResult.map(permission => permission.name);
  const payload = {
    userId: user.id,
    name: user.name,
    username: user.username,
    permissions: permissions,
    db_name: user.db_name
  };
  return jwt.sign(payload, process.env.JWT_REFRESH_KEY, { expiresIn: '3d' });
}


const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'src/app/uploads/');
  },
  filename: (req, file, cb) => {
    cb(null, `${Date.now()}-${file.originalname}`);
  },
});

const upload = multer({ storage });

// Ensure the uploads directory exists
const fs = require('fs');
const uploadDir = 'src/app/uploads';
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir);
}


const deleteFileIfExists = (filePath) => {
  const resolvedPath = path.resolve(filePath);
  if (fs.existsSync(resolvedPath)) {
    try {
      fs.unlinkSync(resolvedPath);
      console.log(`File at ${resolvedPath} has been deleted.`);
    } catch (err) {
      console.error(`Error deleting file at ${resolvedPath}:`, err.message);
    }
  } else {
    console.log(`File at ${resolvedPath} does not exist.`);
  } 
};

let transporter;
// Create transporter with Gmail SMTP
if (process.env.IS_PROD === 'false') {
transporter = nodemailer.createTransport({
  service: 'gmail',
  auth: {
    user: process.env.EMAIL,
    pass: process.env.PASSWORD
  },
  debug: true, // Enable debugging
  logger: true  // Log to console
});
} else {
  // smtp for aws
  transporter = nodemailer.createTransport({
    service: 'gmail',
    auth: {
      user: process.env.PROD_EMAIL,
      pass: process.env.PROD_PASSWORD
    },
    debug: true, // Enable debugging
    logger: true  // Log to console
  })
}

// Verify transporter connection configuration
transporter.verify(function(error, success) {
  if (error) {
    console.error('SMTP Connection Error:', error);
  } else {
    console.log('SMTP Server is ready to take our messages');
  }
});

// Function to send an email
const sendEmail = (to, subject, text) => {
  console.log('Attempting to send email...');
  console.log('From:', process.env.EMAIL);
  console.log('To:', to);
  console.log('Subject:', subject);
  
  const mailOptions = {
    from: `"LMS System" <${process.env.EMAIL}>`,
    to: to,
    subject: subject,
    html: text,
    // Add headers to help with debugging
    headers: {
      'X-LAZY-MANAGER-MICROSOFT': 'outlook' // Helps with some email clients
    }
  };

  console.log('Mail options prepared, sending...');
  
  return new Promise((resolve, reject) => {
    transporter.sendMail(mailOptions, (error, info) => {
      console.log('--- Email Send Attempt ---');
      console.log('Time:', new Date().toISOString());
      
      if (error) {
        console.error('❌ Email Error:', error.message);
        console.error('Error Code:', error.code);
        console.error('Error Response:', error.response);
        console.error('Full Error:', JSON.stringify(error, null, 2));
        reject(error);
      } else {
        console.log('✅ Email sent successfully!');
        console.log('Message ID:', info.messageId);
        console.log('Preview URL:', nodemailer.getTestMessageUrl(info));
        console.log('Response:', info.response);
        resolve(info);
      }
      console.log('--------------------------');
    });
  });
};


const LogsHandler = async (db_name, user_id, logs_name, logs_decription) => {
  await mysqlServerConnection.query(`
        INSERT INTO ${db_name}.user_logs (user_id, log_name, log_description)
        VALUES (?, ?, ?) 
    `, [user_id, logs_name, logs_decription]);

}


const validateEmail = (email) => {

  let validationMessage = null



  switch (true) {

    case !email:

      validationMessage = "Email is required.";

      break;

    case !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email):

      validationMessage = "Must be a valid email address.";

      break;

    default:

      validationMessage = null;

  }



  return validationMessage;

}

const validatePassword = (password) => {

  let validationMessage = null



  switch (true) {

    case password.length < 8:

      validationMessage = "Password must be at least 8 characters long.";

      break;

    case !/\d/.test(password):

      validationMessage = "Password must contain at least one number.";

      break;

    case !/[A-Z]/.test(password):

      validationMessage = "Password must contain at least one uppercase letter.";

      break;

    case !/[!@#$%^&*(),.?":{}|<>]/.test(password):

      validationMessage = "Password must contain at least one special character.";

      break;

    default:

      validationMessage = null

  }



  return validationMessage;

}

/**
 * Generate a professional invoice PDF
 * @param {Object} invoiceData - Invoice data object
 * @returns {Promise<Buffer>} - PDF buffer
 */
const generateInvoicePDF = async (invoiceData) => {
  return new Promise((resolve, reject) => {
    try {
      console.log('=== Starting PDF Generation ===');
      console.log('Invoice Data Received:', JSON.stringify(invoiceData, null, 2));

      const doc = new PDFDocument({
        size: 'A4',
        margin: 50
      });

      const chunks = [];
      doc.on('data', chunk => chunks.push(chunk));
      doc.on('end', () => {
        console.log('PDF generation completed, buffer size:', Buffer.concat(chunks).length);
        resolve(Buffer.concat(chunks));
      });
      doc.on('error', (error) => {
        console.error('PDF generation error:', error);
        reject(error);
      });

      // Set white background
      doc.rect(0, 0, 595, 842).fill('#ffffff');

      // Set default text color to black
      doc.fillColor('#000000');

      // Helper function to draw horizontal line
      const drawLine = (y) => {
        doc.strokeColor('#000000').lineWidth(1);
        doc.moveTo(50, y).lineTo(540, y).stroke();
      };

      // Helper function to draw table row with borders
      const drawTableRow = (y, sl, courseName, quantity, price, isHeader = false) => {
        const colWidths = [60, 200, 80, 140]; // Adjusted column widths: SL, Course Name, Qty, Price
        const startX = 50;
        const rowHeight = isHeader ? 30 : 40; // More height for content rows to handle text wrapping

        // Set stroke color and line width
        doc.strokeColor('#000000').lineWidth(1);

        // Draw horizontal lines (top and bottom of row)
        const totalWidth = colWidths.reduce((sum, width) => sum + width, 0);
        doc.moveTo(startX, y).lineTo(startX + totalWidth, y).stroke();
        doc.moveTo(startX, y + rowHeight).lineTo(startX + totalWidth, y + rowHeight).stroke();

        // Draw vertical lines
        let currentX = startX;
        for (let i = 0; i <= colWidths.length; i++) {
          doc.moveTo(currentX, y).lineTo(currentX, y + rowHeight).stroke();
          if (i < colWidths.length) {
            currentX += colWidths[i];
          }
        }

        // Add text with proper formatting
        const fontSize = isHeader ? 11 : 10;
        const fontType = isHeader ? 'Helvetica-Bold' : 'Helvetica';
        doc.fontSize(fontSize).font(fontType).fillColor('#000000');

        // Add text content with proper positioning and wrapping
        let textX = startX;

        // SL column
        doc.text(sl.toString(), textX + 5, y + 12, {
          width: colWidths[0] - 10,
          align: 'center',
          lineBreak: false
        });
        textX += colWidths[0];

        // Course Name column (with text wrapping)
        doc.text(courseName, textX + 5, y + 12, {
          width: colWidths[1] - 10,
          align: 'left',
          lineBreak: true
        });
        textX += colWidths[1];

        // Quantity column
        doc.text(quantity.toString(), textX + 5, y + 12, {
          width: colWidths[2] - 10,
          align: 'center',
          lineBreak: false
        });
        textX += colWidths[2];

        // Price column
        doc.text(price.toString(), textX + 5, y + 15, {
          width: colWidths[3] - 10,
          align: 'right',
          lineBreak: false,
          continued: false
        });

        return rowHeight; // Return the actual row height used
      };

      // Draw main border around content
      doc.strokeColor('#000000').lineWidth(2);
      doc.rect(30, 30, 535, 782).stroke();

      let currentY = 60;

      // Invoice Title
      doc.fontSize(24).font('Helvetica-Bold').fillColor('#000000');
      doc.text('INVOICE', 50, currentY);
      currentY += 40;
      drawLine(currentY);
      currentY += 20;

      // Invoice ID and Date
      doc.fontSize(12).font('Helvetica').fillColor('#000000');
      doc.text(`Invoice ID: ${invoiceData.transactionId || 'N/A'}`, 50, currentY);
      doc.text(`Date: ${new Date(invoiceData.paymentDate).toLocaleDateString()}`, 450, currentY);
      currentY += 30;
      drawLine(currentY);
      currentY += 20;

      // Company Information
      doc.fontSize(14).font('Helvetica-Bold').fillColor('#000000');
      doc.text('COMPANY INFORMATION', 50, currentY);
      currentY += 25;

      // Use default company info if environment variables are not set
      const companyName = process.env.COMPANY_NAME || 'Not Specified';
      const companyAddress = process.env.COMPANY_ADDRESS || 'Not Specified';
      const companyPhone = process.env.COMPANY_PHONE || 'Not Specified';
      const companyEmail = process.env.COMPANY_EMAIL || 'Not Specified';

      doc.fontSize(10).font('Helvetica-Bold').fillColor('#000000');
      doc.text(companyName, 50, currentY);
      currentY += 20;
      doc.fontSize(10).font('Helvetica').fillColor('#000000');

      doc.text(`Email: ${companyEmail}`, 50, currentY);
      currentY += 15;
      doc.text(`Phone: ${companyPhone}`, 50, currentY);
      currentY += 15;
      
      // Handle long address by wrapping text properly
      const addressOptions = {
        width: 490, // Available width (540 - 50)
        align: 'left'
      };
      doc.text(`Address: ${companyAddress}`, 50, currentY, addressOptions);

      // Calculate how many lines the address took
      const addressHeight = doc.heightOfString(`Address: ${companyAddress}`, addressOptions);
      currentY += Math.max(15, addressHeight + 5);
      currentY += 5;
      drawLine(currentY);
      currentY += 20;

      // Customer Information
      doc.fontSize(14).font('Helvetica-Bold').fillColor('#000000');
      doc.text('CUSTOMER INFORMATION', 50, currentY);
      currentY += 25;

      doc.fontSize(10).font('Helvetica').fillColor('#000000');
      doc.text(`Name: ${invoiceData.userName || 'N/A'}`, 50, currentY);
      currentY += 15;
      doc.text(`Email: ${invoiceData.userEmail || 'N/A'}`, 50, currentY);
      currentY += 15;
      doc.text(`Phone: ${invoiceData.userPhone || 'N/A'}`, 50, currentY);
      currentY += 20;
      drawLine(currentY);
      currentY += 20;

      // Course Details
      doc.fontSize(14).font('Helvetica-Bold').fillColor('#000000');
      doc.text('COURSE DETAILS', 50, currentY);
      currentY += 25;

      // Table Header
      const headerHeight = drawTableRow(currentY, 'SL', 'Course Name', 'Qty', 'Price (INR)', true);
      currentY += headerHeight;

      // Table Content
      const coursePrice = `${invoiceData.amount || 0}`;
      const contentHeight = drawTableRow(currentY, '1', invoiceData.courseName || 'N/A', '1', coursePrice, false);
      currentY += contentHeight;
      currentY += 20;

      // Total Amount Section
      drawLine(currentY);
      currentY += 15;
      doc.fontSize(12).font('Helvetica-Bold').fillColor('#000000');
      const totalAmountText = `TOTAL AMOUNT: ${invoiceData.amount || 0}`;
      doc.text(totalAmountText, 400, currentY);
      currentY += 30;
      drawLine(currentY);
      currentY += 30;

      // Thank You Message
      doc.fontSize(16).font('Helvetica-Bold').fillColor('#000000');
      doc.text('THANK YOU FOR YOUR PURCHASE!', 50, currentY);
      currentY += 25;
      doc.fontSize(11).font('Helvetica').fillColor('#000000');
      doc.text('This is a computer-generated invoice. No signature is required.', 50, currentY);
      currentY += 15;
      doc.text('For any queries, please contact our support team.', 50, currentY);
      currentY += 25;

      // Terms and Conditions
      doc.fontSize(12).font('Helvetica-Bold').fillColor('#000000');
      doc.text('TERMS & CONDITIONS:', 50, currentY);
      currentY += 20;
      doc.fontSize(10).font('Helvetica').fillColor('#000000');
      doc.text('• All payments are non-refundable unless otherwise specified.', 50, currentY);
      currentY += 15;

      console.log('PDF content generation completed, finalizing document...');
      doc.end();

    } catch (error) {
      console.error('Error in PDF generation:', error);
      reject(error);
    }
  });
};

module.exports = { 
  hashPassword, 
  Validate, 
  generateAccessToken, 
  generateRefreshToken, 
  upload, 
  sendEmail, 
  storage, 
  deleteFileIfExists, 
  LogsHandler, 
  validateEmail, 
  validatePassword,
  generateInvoicePDF
};