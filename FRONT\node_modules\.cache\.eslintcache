[{"C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\App.js": "3", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\routes\\AdminRoutes.jsx": "4", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\routes\\UserRoutes.jsx": "5", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\routes\\AuthRoutes.jsx": "6", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\layouts\\AdminLayout.jsx": "7", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\layouts\\UserLayout.jsx": "8", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\Login.jsx": "9", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\ForgotPasswordOTP.jsx": "10", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\Register.jsx": "11", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\RegisterOTP.jsx": "12", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\ForgotPassword.jsx": "13", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\ResetPassword.jsx": "14", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\error\\Error401.jsx": "15", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\error\\PageNotFound.jsx": "16", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomDashboard.jsx": "17", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\AllClassroom.jsx": "18", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssignments.jsx": "19", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssessments.jsx": "20", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssessmentDetails.jsx": "21", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssignmentDetails.jsx": "22", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomTrainees.jsx": "23", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\AllCourses.jsx": "24", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomLiveClasses.jsx": "25", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalytics.jsx": "26", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomCommunity.jsx": "27", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsAssessmentDetails.jsx": "28", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseQuiz.jsx": "29", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseDocument.jsx": "30", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseModule.jsx": "31", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\Courses.jsx": "32", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseSurvey.jsx": "33", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CreateCourse.jsx": "34", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\ModuleContent.jsx": "35", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsSurveyDetails.jsx": "36", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo.jsx": "37", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\questionBank\\QuestionBank.jsx": "38", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\certificates\\AllCertificates.jsx": "39", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\approvalRequest\\ApprovalRequest.jsx": "40", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\announcement\\announcement.jsx": "41", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\Dashboard.jsx": "42", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\profile\\Profile.jsx": "43", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\Notifications\\NotificationDetails.jsx": "44", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\Notifications\\Notifications.jsx": "45", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\Settings.jsx": "46", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\trainees.jsx": "47", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalytics.jsx": "48", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo\\VideoPlayer.jsx": "49", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\dashboard\\Dashboard.jsx": "50", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\profile\\Profile.jsx": "51", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\Courses.jsx": "52", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\Result.jsx": "53", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseWatch.jsx": "54", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseDetails.jsx": "55", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\Resultvideo.jsx": "56", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\Settings.jsx": "57", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\SuccessPayment.jsx": "58", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssessmentQuestions.jsx": "59", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssessmentResult.jsx": "60", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssignmentResources.jsx": "61", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssignmentResponse.jsx": "62", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsAssessment.jsx": "63", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsSurvey.jsx": "64", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsTraineeProgress.jsx": "65", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\Classroom.jsx": "66", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AllClassroom.jsx": "67", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AssessmentQuestions.jsx": "68", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AssessmentQuiz.jsx": "69", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\help\\Help.jsx": "70", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AssignmentQuestions.jsx": "71", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\help\\Faq.jsx": "72", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AssignmentQuizResult.jsx": "73", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\notification\\NotificationDetails.jsx": "74", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\certificates\\Certificates.jsx": "75", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\help\\Ticket.jsx": "76", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\notification\\Notifications.jsx": "77", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\utils\\validation.js": "78", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\utils\\encodeAndEncode.js": "79", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\authService.js": "80", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\Cummunity.jsx": "81", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\adminService.js": "82", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\Header.jsx": "83", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\Footer.jsx": "84", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\Breadcrumbs.jsx": "85", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\user\\Footer.jsx": "86", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\user\\SideBar.jsx": "87", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\user\\Header.jsx": "88", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\SideBar.jsx": "89", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\user\\Breadcrumbs.jsx": "90", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\common\\Loader.jsx": "91", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\common\\NoData.jsx": "92", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\Loader.jsx": "93", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\WaitingForApproval.jsx": "94", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\userService.js": "95", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\Pending.jsx": "96", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\approvalRequest\\Rejected.jsx": "97", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\Approved.jsx": "98", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\Rejected.jsx": "99", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\approvalRequest\\Approved.jsx": "100", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\approvalRequest\\PendingApproval.jsx": "101", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\Ticket.jsx": "102", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\RecentPushedAnnouncement.jsx": "103", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\CourseGraph.jsx": "104", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\profile\\PersonalInformation.jsx": "105", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\TraineeGraph.jsx": "106", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\AboutOrganization.jsx": "107", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\traineeService.js": "108", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\profile\\ChangePassword.jsx": "109", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\PaymentHistory.jsx": "110", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\Query.jsx": "111", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\FAQ.jsx": "112", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsClassroomTab.jsx": "113", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsCourseTab.jsx": "114", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\hooks\\useDebounce.js": "115", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsPaymentsTab.jsx": "116", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsTicketsTab.jsx": "117", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo\\CourseOverview.jsx": "118", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsCertificatesTab.jsx": "119", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo\\CourseRating.jsx": "120", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo\\CourseComment.jsx": "121", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\dashboard\\Statistics.jsx": "122", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\common\\Activity.jsx": "123", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\dashboard\\TodoList.jsx": "124", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\profile\\PersonalInformation.jsx": "125", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\profile\\ChangePassword.jsx": "126", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseTab.jsx": "127", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseQuiz.jsx": "128", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseModule.jsx": "129", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\MyCourseTab.jsx": "130", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\VideoPlayer.jsx": "131", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\DeleteAccount.jsx": "132", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseDocument.jsx": "133", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseSurvey.jsx": "134", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\Notification.jsx": "135", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\apiController.js": "136", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\AccountInfo.jsx": "137", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\Payment.jsx": "138", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\AboutUs.jsx": "139", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\Assessments.jsx": "140", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\Assignments.jsx": "141", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\LiveClass.jsx": "142", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\RecordedVideo.jsx": "143", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseRating.jsx": "144", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseNotes.jsx": "145", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseOverview.jsx": "146", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseComment.jsx": "147", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\context\\PermissionsContext.js": "148", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\roles_and_access\\RolesAndAccess.jsx": "149", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\PromptAssistant\\PromptAssistant.jsx": "150", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\roles_and_access\\Users.jsx": "151", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\roles_and_access\\permission.jsx": "152", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\dashboard\\TaskList.jsx": "153", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\context\\NotificationContext.js": "154", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomRecorded.jsx": "155", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\NewClassroomLiveCLasses.jsx": "156", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\ActiveAccountPage.jsx": "157", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\zoom\\ZoomMeeting.jsx": "158", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\utils\\logoUtils.js": "159", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\SuccessPayUPayment.jsx": "160", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\CancelPayUPayment.jsx": "161", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\OrderDetailsWithPayu.jsx": "162", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\OrderDetails.jsx": "163", "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\public\\PublicCourseDetails.jsx": "164"}, {"size": 552, "mtime": *************, "results": "165", "hashOfConfig": "166"}, {"size": 375, "mtime": *************, "results": "167", "hashOfConfig": "166"}, {"size": 1901, "mtime": *************, "results": "168", "hashOfConfig": "166"}, {"size": 7498, "mtime": 1753125925988, "results": "169", "hashOfConfig": "166"}, {"size": 4546, "mtime": 1753890072773, "results": "170", "hashOfConfig": "166"}, {"size": 1522, "mtime": 1752921584997, "results": "171", "hashOfConfig": "166"}, {"size": 2212, "mtime": 1751054548000, "results": "172", "hashOfConfig": "166"}, {"size": 2266, "mtime": 1751054540000, "results": "173", "hashOfConfig": "166"}, {"size": 8314, "mtime": 1753384191084, "results": "174", "hashOfConfig": "166"}, {"size": 5573, "mtime": 1753383910530, "results": "175", "hashOfConfig": "166"}, {"size": 14993, "mtime": 1753383833576, "results": "176", "hashOfConfig": "166"}, {"size": 5376, "mtime": 1753383939567, "results": "177", "hashOfConfig": "166"}, {"size": 4637, "mtime": 1753383886944, "results": "178", "hashOfConfig": "166"}, {"size": 9424, "mtime": 1753383999422, "results": "179", "hashOfConfig": "166"}, {"size": 882, "mtime": 1750498842000, "results": "180", "hashOfConfig": "166"}, {"size": 887, "mtime": 1750500968000, "results": "181", "hashOfConfig": "166"}, {"size": 23107, "mtime": 1753190819653, "results": "182", "hashOfConfig": "166"}, {"size": 64676, "mtime": 1753170429987, "results": "183", "hashOfConfig": "166"}, {"size": 53481, "mtime": 1752484548147, "results": "184", "hashOfConfig": "166"}, {"size": 65216, "mtime": 1752490834024, "results": "185", "hashOfConfig": "166"}, {"size": 4643, "mtime": 1752409801600, "results": "186", "hashOfConfig": "166"}, {"size": 4216, "mtime": 1752409801605, "results": "187", "hashOfConfig": "166"}, {"size": 40073, "mtime": 1752409801612, "results": "188", "hashOfConfig": "166"}, {"size": 36784, "mtime": 1752409801613, "results": "189", "hashOfConfig": "166"}, {"size": 51452, "mtime": 1753276336843, "results": "190", "hashOfConfig": "166"}, {"size": 12687, "mtime": 1751710504000, "results": "191", "hashOfConfig": "166"}, {"size": 169, "mtime": 1752677915041, "results": "192", "hashOfConfig": "166"}, {"size": 9308, "mtime": 1751767716000, "results": "193", "hashOfConfig": "166"}, {"size": 77456, "mtime": 1752409801619, "results": "194", "hashOfConfig": "166"}, {"size": 22490, "mtime": 1752409801616, "results": "195", "hashOfConfig": "166"}, {"size": 26511, "mtime": 1752409801617, "results": "196", "hashOfConfig": "166"}, {"size": 5517, "mtime": 1752409801623, "results": "197", "hashOfConfig": "166"}, {"size": 32410, "mtime": 1752409801620, "results": "198", "hashOfConfig": "166"}, {"size": 44282, "mtime": 1753903422856, "results": "199", "hashOfConfig": "166"}, {"size": 100330, "mtime": 1752409801626, "results": "200", "hashOfConfig": "166"}, {"size": 7690, "mtime": 1751768538000, "results": "201", "hashOfConfig": "166"}, {"size": 15453, "mtime": 1751722792000, "results": "202", "hashOfConfig": "166"}, {"size": 54929, "mtime": 1752409801636, "results": "203", "hashOfConfig": "166"}, {"size": 36510, "mtime": 1752920830735, "results": "204", "hashOfConfig": "166"}, {"size": 3661, "mtime": 1752338196000, "results": "205", "hashOfConfig": "166"}, {"size": 54017, "mtime": 1752829580976, "results": "206", "hashOfConfig": "166"}, {"size": 11659, "mtime": 1752411851791, "results": "207", "hashOfConfig": "166"}, {"size": 1670, "mtime": 1752409801634, "results": "208", "hashOfConfig": "166"}, {"size": 5099, "mtime": 1752836652921, "results": "209", "hashOfConfig": "166"}, {"size": 10347, "mtime": 1753280697739, "results": "210", "hashOfConfig": "166"}, {"size": 5709, "mtime": 1752409801648, "results": "211", "hashOfConfig": "166"}, {"size": 48841, "mtime": 1752409801651, "results": "212", "hashOfConfig": "166"}, {"size": 11615, "mtime": 1752097860000, "results": "213", "hashOfConfig": "166"}, {"size": 27087, "mtime": 1752409801621, "results": "214", "hashOfConfig": "166"}, {"size": 9781, "mtime": 1753216567180, "results": "215", "hashOfConfig": "166"}, {"size": 1734, "mtime": 1751059658000, "results": "216", "hashOfConfig": "166"}, {"size": 2266, "mtime": 1753216737273, "results": "217", "hashOfConfig": "166"}, {"size": 8117, "mtime": 1752409801668, "results": "218", "hashOfConfig": "166"}, {"size": 10168, "mtime": 1751782620000, "results": "219", "hashOfConfig": "166"}, {"size": 27198, "mtime": 1753879087755, "results": "220", "hashOfConfig": "166"}, {"size": 4076, "mtime": 1751035930000, "results": "221", "hashOfConfig": "166"}, {"size": 4662, "mtime": 1752409801676, "results": "222", "hashOfConfig": "166"}, {"size": 4567, "mtime": 1753879568970, "results": "223", "hashOfConfig": "166"}, {"size": 40890, "mtime": 1752409801601, "results": "224", "hashOfConfig": "166"}, {"size": 49748, "mtime": 1752409801602, "results": "225", "hashOfConfig": "166"}, {"size": 29513, "mtime": 1752409801607, "results": "226", "hashOfConfig": "166"}, {"size": 20882, "mtime": 1752409801608, "results": "227", "hashOfConfig": "166"}, {"size": 7771, "mtime": 1751767656000, "results": "228", "hashOfConfig": "166"}, {"size": 7751, "mtime": 1751767830000, "results": "229", "hashOfConfig": "166"}, {"size": 8583, "mtime": 1751710504000, "results": "230", "hashOfConfig": "166"}, {"size": 9357, "mtime": 1753007074236, "results": "231", "hashOfConfig": "166"}, {"size": 9025, "mtime": 1750975280000, "results": "232", "hashOfConfig": "166"}, {"size": 7922, "mtime": *************, "results": "233", "hashOfConfig": "166"}, {"size": 23471, "mtime": 1752409801655, "results": "234", "hashOfConfig": "166"}, {"size": 5224, "mtime": 1750548386000, "results": "235", "hashOfConfig": "166"}, {"size": 22425, "mtime": 1752677199172, "results": "236", "hashOfConfig": "166"}, {"size": 4066, "mtime": 1750548608000, "results": "237", "hashOfConfig": "166"}, {"size": 3862, "mtime": *************, "results": "238", "hashOfConfig": "166"}, {"size": 5099, "mtime": 1752836952067, "results": "239", "hashOfConfig": "166"}, {"size": 10555, "mtime": 1753260541912, "results": "240", "hashOfConfig": "166"}, {"size": 14135, "mtime": 1753359213497, "results": "241", "hashOfConfig": "166"}, {"size": 10347, "mtime": 1753280688735, "results": "242", "hashOfConfig": "166"}, {"size": 13237, "mtime": 1750509658000, "results": "243", "hashOfConfig": "166"}, {"size": 760, "mtime": 1750582154000, "results": "244", "hashOfConfig": "166"}, {"size": 1464, "mtime": 1753081064033, "results": "245", "hashOfConfig": "166"}, {"size": 61762, "mtime": 1752931709150, "results": "246", "hashOfConfig": "166"}, {"size": 27929, "mtime": 1753099878281, "results": "247", "hashOfConfig": "166"}, {"size": 8756, "mtime": 1753280085442, "results": "248", "hashOfConfig": "166"}, {"size": 0, "mtime": 1751054256000, "results": "249", "hashOfConfig": "166"}, {"size": 21900, "mtime": 1752409801578, "results": "250", "hashOfConfig": "166"}, {"size": 409, "mtime": *************, "results": "251", "hashOfConfig": "166"}, {"size": 10020, "mtime": 1753861868097, "results": "252", "hashOfConfig": "166"}, {"size": 8024, "mtime": 1753280076928, "results": "253", "hashOfConfig": "166"}, {"size": 11878, "mtime": 1753384103074, "results": "254", "hashOfConfig": "166"}, {"size": 13068, "mtime": 1753884508765, "results": "255", "hashOfConfig": "166"}, {"size": 488, "mtime": 1751579418000, "results": "256", "hashOfConfig": "166"}, {"size": 506, "mtime": 1751579400000, "results": "257", "hashOfConfig": "166"}, {"size": 1452, "mtime": 1751437768000, "results": "258", "hashOfConfig": "166"}, {"size": 23613, "mtime": 1752409801630, "results": "259", "hashOfConfig": "166"}, {"size": 10859, "mtime": 1753908526977, "results": "260", "hashOfConfig": "166"}, {"size": 25665, "mtime": 1752409801627, "results": "261", "hashOfConfig": "166"}, {"size": 19281, "mtime": 1752409801594, "results": "262", "hashOfConfig": "166"}, {"size": 16387, "mtime": 1752409801614, "results": "263", "hashOfConfig": "166"}, {"size": 30692, "mtime": 1752409801629, "results": "264", "hashOfConfig": "166"}, {"size": 20468, "mtime": 1752409801591, "results": "265", "hashOfConfig": "166"}, {"size": 23434, "mtime": 1752409801593, "results": "266", "hashOfConfig": "166"}, {"size": 3508, "mtime": 1752049504000, "results": "267", "hashOfConfig": "166"}, {"size": 2614, "mtime": 1751826792000, "results": "268", "hashOfConfig": "166"}, {"size": 1923, "mtime": 1751826502000, "results": "269", "hashOfConfig": "166"}, {"size": 50190, "mtime": 1752409801633, "results": "270", "hashOfConfig": "166"}, {"size": 1927, "mtime": 1751826456000, "results": "271", "hashOfConfig": "166"}, {"size": 23878, "mtime": 1752409801643, "results": "272", "hashOfConfig": "166"}, {"size": 1708, "mtime": 1752408694807, "results": "273", "hashOfConfig": "166"}, {"size": 7643, "mtime": 1750763974000, "results": "274", "hashOfConfig": "166"}, {"size": 23256, "mtime": 1752409801645, "results": "275", "hashOfConfig": "166"}, {"size": 21217, "mtime": 1752409801647, "results": "276", "hashOfConfig": "166"}, {"size": 22643, "mtime": 1752409801644, "results": "277", "hashOfConfig": "166"}, {"size": 2997, "mtime": 1751882506000, "results": "278", "hashOfConfig": "166"}, {"size": 16290, "mtime": 1752409801649, "results": "279", "hashOfConfig": "166"}, {"size": 388, "mtime": 1751774832000, "results": "280", "hashOfConfig": "166"}, {"size": 9439, "mtime": 1751882686000, "results": "281", "hashOfConfig": "166"}, {"size": 8837, "mtime": 1751887002000, "results": "282", "hashOfConfig": "166"}, {"size": 1896, "mtime": 1751609914000, "results": "283", "hashOfConfig": "166"}, {"size": 4484, "mtime": 1751882686000, "results": "284", "hashOfConfig": "166"}, {"size": 4450, "mtime": 1751482138000, "results": "285", "hashOfConfig": "166"}, {"size": 16839, "mtime": 1752683212096, "results": "286", "hashOfConfig": "166"}, {"size": 5743, "mtime": 1750625032000, "results": "287", "hashOfConfig": "166"}, {"size": 5240, "mtime": 1752054098000, "results": "288", "hashOfConfig": "166"}, {"size": 12656, "mtime": 1750906260000, "results": "289", "hashOfConfig": "166"}, {"size": 49712, "mtime": 1751053448000, "results": "290", "hashOfConfig": "166"}, {"size": 7643, "mtime": 1750763974000, "results": "291", "hashOfConfig": "166"}, {"size": 9421, "mtime": 1753900289098, "results": "292", "hashOfConfig": "166"}, {"size": 18218, "mtime": 1752409801667, "results": "293", "hashOfConfig": "166"}, {"size": 9495, "mtime": 1752435437340, "results": "294", "hashOfConfig": "166"}, {"size": 6988, "mtime": 1753900289099, "results": "295", "hashOfConfig": "166"}, {"size": 13304, "mtime": 1752435292539, "results": "296", "hashOfConfig": "166"}, {"size": 5931, "mtime": 1750543146000, "results": "297", "hashOfConfig": "166"}, {"size": 430, "mtime": 1750591742000, "results": "298", "hashOfConfig": "166"}, {"size": 11350, "mtime": 1751037418000, "results": "299", "hashOfConfig": "166"}, {"size": 5807, "mtime": 1750533816000, "results": "300", "hashOfConfig": "166"}, {"size": 7407, "mtime": 1752767163918, "results": "301", "hashOfConfig": "166"}, {"size": 3831, "mtime": *************, "results": "302", "hashOfConfig": "166"}, {"size": 8425, "mtime": 1751813544000, "results": "303", "hashOfConfig": "166"}, {"size": 1406, "mtime": 1750548574000, "results": "304", "hashOfConfig": "166"}, {"size": 18082, "mtime": 1752474860957, "results": "305", "hashOfConfig": "166"}, {"size": 10944, "mtime": 1752484370321, "results": "306", "hashOfConfig": "166"}, {"size": 19638, "mtime": 1753276143523, "results": "307", "hashOfConfig": "166"}, {"size": 17989, "mtime": 1752697222607, "results": "308", "hashOfConfig": "166"}, {"size": 8503, "mtime": 1752680006516, "results": "309", "hashOfConfig": "166"}, {"size": 7673, "mtime": 1752680102177, "results": "310", "hashOfConfig": "166"}, {"size": 626, "mtime": 1751536590000, "results": "311", "hashOfConfig": "166"}, {"size": 18515, "mtime": 1752683186304, "results": "312", "hashOfConfig": "166"}, {"size": 1686, "mtime": 1752409801587, "results": "313", "hashOfConfig": "166"}, {"size": 2272, "mtime": 1752409801638, "results": "314", "hashOfConfig": "166"}, {"size": 6037, "mtime": 1752409801588, "results": "315", "hashOfConfig": "166"}, {"size": 52979, "mtime": 1752685608016, "results": "316", "hashOfConfig": "166"}, {"size": 26870, "mtime": 1752827191316, "results": "317", "hashOfConfig": "166"}, {"size": 16901, "mtime": 1752409801670, "results": "318", "hashOfConfig": "166"}, {"size": 925, "mtime": 1752409801586, "results": "319", "hashOfConfig": "166"}, {"size": 52070, "mtime": 1752697034820, "results": "320", "hashOfConfig": "166"}, {"size": 184, "mtime": 1752749250332, "results": "321", "hashOfConfig": "166"}, {"size": 6226, "mtime": 1753383969771, "results": "322", "hashOfConfig": "166"}, {"size": 8705, "mtime": 1753278652965, "results": "323", "hashOfConfig": "166"}, {"size": 1258, "mtime": 1753386028029, "results": "324", "hashOfConfig": "166"}, {"size": 7621, "mtime": 1753885536281, "results": "325", "hashOfConfig": "166"}, {"size": 5672, "mtime": 1753885538738, "results": "326", "hashOfConfig": "166"}, {"size": 11695, "mtime": 1753890036950, "results": "327", "hashOfConfig": "166"}, {"size": 11352, "mtime": 1753890023205, "results": "328", "hashOfConfig": "166"}, {"size": 25312, "mtime": 1753908766129, "results": "329", "hashOfConfig": "166"}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1h3wgbg", {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "612", "messages": "613", "suppressedMessages": "614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "615", "messages": "616", "suppressedMessages": "617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "618", "messages": "619", "suppressedMessages": "620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "621", "messages": "622", "suppressedMessages": "623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "624", "messages": "625", "suppressedMessages": "626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "627", "messages": "628", "suppressedMessages": "629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "630", "messages": "631", "suppressedMessages": "632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "633", "messages": "634", "suppressedMessages": "635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "636", "messages": "637", "suppressedMessages": "638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "639", "messages": "640", "suppressedMessages": "641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "642", "messages": "643", "suppressedMessages": "644", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "645", "messages": "646", "suppressedMessages": "647", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "648", "messages": "649", "suppressedMessages": "650", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "651", "messages": "652", "suppressedMessages": "653", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "654", "messages": "655", "suppressedMessages": "656", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "657", "messages": "658", "suppressedMessages": "659", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "660", "messages": "661", "suppressedMessages": "662", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "663", "messages": "664", "suppressedMessages": "665", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "666", "messages": "667", "suppressedMessages": "668", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "669", "messages": "670", "suppressedMessages": "671", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "672", "messages": "673", "suppressedMessages": "674", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "675", "messages": "676", "suppressedMessages": "677", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "678", "messages": "679", "suppressedMessages": "680", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "681", "messages": "682", "suppressedMessages": "683", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "684", "messages": "685", "suppressedMessages": "686", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "687", "messages": "688", "suppressedMessages": "689", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "690", "messages": "691", "suppressedMessages": "692", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "693", "messages": "694", "suppressedMessages": "695", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "696", "messages": "697", "suppressedMessages": "698", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "699", "messages": "700", "suppressedMessages": "701", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "702", "messages": "703", "suppressedMessages": "704", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "705", "messages": "706", "suppressedMessages": "707", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "708", "messages": "709", "suppressedMessages": "710", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "711", "messages": "712", "suppressedMessages": "713", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "714", "messages": "715", "suppressedMessages": "716", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "717", "messages": "718", "suppressedMessages": "719", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "720", "messages": "721", "suppressedMessages": "722", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "723", "messages": "724", "suppressedMessages": "725", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "726", "messages": "727", "suppressedMessages": "728", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "729", "messages": "730", "suppressedMessages": "731", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "732", "messages": "733", "suppressedMessages": "734", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "735", "messages": "736", "suppressedMessages": "737", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "738", "messages": "739", "suppressedMessages": "740", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "741", "messages": "742", "suppressedMessages": "743", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "744", "messages": "745", "suppressedMessages": "746", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "747", "messages": "748", "suppressedMessages": "749", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "750", "messages": "751", "suppressedMessages": "752", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "753", "messages": "754", "suppressedMessages": "755", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "756", "messages": "757", "suppressedMessages": "758", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "759", "messages": "760", "suppressedMessages": "761", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "762", "messages": "763", "suppressedMessages": "764", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "765", "messages": "766", "suppressedMessages": "767", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "768", "messages": "769", "suppressedMessages": "770", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "771", "messages": "772", "suppressedMessages": "773", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "774", "messages": "775", "suppressedMessages": "776", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "777", "messages": "778", "suppressedMessages": "779", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "780", "messages": "781", "suppressedMessages": "782", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "783", "messages": "784", "suppressedMessages": "785", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "786", "messages": "787", "suppressedMessages": "788", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "789", "messages": "790", "suppressedMessages": "791", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "792", "messages": "793", "suppressedMessages": "794", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "795", "messages": "796", "suppressedMessages": "797", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "798", "messages": "799", "suppressedMessages": "800", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "801", "messages": "802", "suppressedMessages": "803", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "804", "messages": "805", "suppressedMessages": "806", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "807", "messages": "808", "suppressedMessages": "809", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "810", "messages": "811", "suppressedMessages": "812", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "813", "messages": "814", "suppressedMessages": "815", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "816", "messages": "817", "suppressedMessages": "818", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "819", "messages": "820", "suppressedMessages": "821", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 22, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\App.js", ["822"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\routes\\AdminRoutes.jsx", ["823"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\routes\\UserRoutes.jsx", ["824"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\routes\\AuthRoutes.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\layouts\\AdminLayout.jsx", ["825", "826"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\layouts\\UserLayout.jsx", ["827", "828"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\Login.jsx", ["829"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\ForgotPasswordOTP.jsx", ["830"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\Register.jsx", ["831", "832"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\RegisterOTP.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\ForgotPassword.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\ResetPassword.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\error\\Error401.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\error\\PageNotFound.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomDashboard.jsx", ["833", "834", "835", "836"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\AllClassroom.jsx", ["837", "838", "839", "840"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssignments.jsx", ["841", "842", "843"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssessments.jsx", ["844", "845"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssessmentDetails.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssignmentDetails.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomTrainees.jsx", ["846", "847", "848"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\AllCourses.jsx", ["849", "850"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomLiveClasses.jsx", ["851", "852", "853", "854"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalytics.jsx", ["855"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomCommunity.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsAssessmentDetails.jsx", ["856", "857"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseQuiz.jsx", ["858", "859", "860", "861", "862", "863"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseDocument.jsx", ["864", "865", "866"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseModule.jsx", ["867", "868", "869", "870"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\Courses.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseSurvey.jsx", ["871"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CreateCourse.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\ModuleContent.jsx", ["872", "873", "874", "875"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsSurveyDetails.jsx", ["876"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo.jsx", ["877", "878"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\questionBank\\QuestionBank.jsx", ["879", "880"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\certificates\\AllCertificates.jsx", ["881", "882", "883", "884", "885", "886"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\approvalRequest\\ApprovalRequest.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\announcement\\announcement.jsx", ["887"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\Dashboard.jsx", ["888", "889"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\profile\\Profile.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\Notifications\\NotificationDetails.jsx", ["890"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\Notifications\\Notifications.jsx", ["891", "892", "893"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\Settings.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\trainees.jsx", ["894", "895", "896", "897", "898"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalytics.jsx", ["899", "900"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo\\VideoPlayer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\dashboard\\Dashboard.jsx", ["901", "902", "903"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\profile\\Profile.jsx", ["904"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\Courses.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\Result.jsx", ["905"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseWatch.jsx", ["906", "907", "908", "909", "910"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseDetails.jsx", ["911", "912", "913", "914", "915", "916", "917", "918", "919", "920", "921", "922", "923", "924", "925", "926", "927"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\Resultvideo.jsx", ["928", "929", "930"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\Settings.jsx", ["931", "932"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\SuccessPayment.jsx", ["933"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssessmentQuestions.jsx", ["934", "935", "936"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssessmentResult.jsx", ["937", "938", "939"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssignmentResources.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomAssignmentResponse.jsx", ["940", "941", "942"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsAssessment.jsx", ["943"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsSurvey.jsx", ["944"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseAnalyticsTraineeProgress.jsx", ["945", "946"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\Classroom.jsx", ["947", "948"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AllClassroom.jsx", ["949", "950"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AssessmentQuestions.jsx", ["951"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AssessmentQuiz.jsx", ["952", "953"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\help\\Help.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AssignmentQuestions.jsx", ["954"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\help\\Faq.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\AssignmentQuizResult.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\notification\\NotificationDetails.jsx", ["955"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\certificates\\Certificates.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\help\\Ticket.jsx", ["956", "957", "958"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\notification\\Notifications.jsx", ["959", "960", "961"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\utils\\validation.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\utils\\encodeAndEncode.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\authService.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\Cummunity.jsx", ["962", "963", "964", "965", "966", "967"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\adminService.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\Header.jsx", ["968", "969"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\Footer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\Breadcrumbs.jsx", ["970"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\user\\Footer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\user\\SideBar.jsx", ["971", "972"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\user\\Header.jsx", ["973", "974"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\SideBar.jsx", ["975", "976"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\user\\Breadcrumbs.jsx", ["977"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\common\\Loader.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\common\\NoData.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\admin\\Loader.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\WaitingForApproval.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\userService.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\Pending.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\approvalRequest\\Rejected.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\Approved.jsx", ["978", "979"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\Rejected.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\approvalRequest\\Approved.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\approvalRequest\\PendingApproval.jsx", ["980"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\Ticket.jsx", ["981"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\RecentPushedAnnouncement.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\CourseGraph.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\profile\\PersonalInformation.jsx", ["982", "983", "984", "985", "986", "987", "988"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\dashboard\\TraineeGraph.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\AboutOrganization.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\traineeService.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\profile\\ChangePassword.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\PaymentHistory.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\Query.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\settings\\FAQ.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsClassroomTab.jsx", ["989"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsCourseTab.jsx", ["990"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\hooks\\useDebounce.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsPaymentsTab.jsx", ["991"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsTicketsTab.jsx", ["992"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo\\CourseOverview.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\trainees\\TraineeAnalyticsCertificatesTab.jsx", ["993"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo\\CourseRating.jsx", ["994"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\courses\\CourseVideo\\CourseComment.jsx", ["995", "996", "997", "998"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\dashboard\\Statistics.jsx", ["999"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\common\\Activity.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\dashboard\\TodoList.jsx", ["1000"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\profile\\PersonalInformation.jsx", ["1001", "1002", "1003", "1004", "1005", "1006", "1007"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\profile\\ChangePassword.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseTab.jsx", ["1008", "1009"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseQuiz.jsx", ["1010", "1011"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseModule.jsx", ["1012"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\MyCourseTab.jsx", ["1013", "1014"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\VideoPlayer.jsx", ["1015", "1016", "1017", "1018"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\DeleteAccount.jsx", ["1019"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseDocument.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseSurvey.jsx", ["1020", "1021"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\Notification.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\services\\apiController.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\AccountInfo.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\Payment.jsx", ["1022", "1023"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\AboutUs.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\Assessments.jsx", ["1024"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\Assignments.jsx", ["1025", "1026"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\LiveClass.jsx", ["1027", "1028", "1029", "1030"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\classroom\\RecordedVideo.jsx", ["1031", "1032"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseRating.jsx", ["1033"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseNotes.jsx", ["1034", "1035"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseOverview.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\CourseWatch\\CourseComment.jsx", ["1036", "1037", "1038", "1039"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\context\\PermissionsContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\roles_and_access\\RolesAndAccess.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\PromptAssistant\\PromptAssistant.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\roles_and_access\\Users.jsx", ["1040", "1041", "1042", "1043", "1044"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\roles_and_access\\permission.jsx", ["1045"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\dashboard\\TaskList.jsx", ["1046", "1047"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\context\\NotificationContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\ClassroomRecorded.jsx", ["1048", "1049", "1050", "1051"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\admin\\classroom\\NewClassroomLiveCLasses.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\auth\\ActiveAccountPage.jsx", ["1052"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\components\\zoom\\ZoomMeeting.jsx", ["1053"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\utils\\logoUtils.js", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\SuccessPayUPayment.jsx", ["1054"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\settings\\CancelPayUPayment.jsx", ["1055", "1056"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\OrderDetailsWithPayu.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\user\\course\\OrderDetails.jsx", ["1057", "1058"], [], "C:\\Users\\<USER>\\Desktop\\NEW_LMS_FIXING\\FRONT\\src\\pages\\public\\PublicCourseDetails.jsx", ["1059", "1060", "1061", "1062", "1063", "1064", "1065", "1066", "1067", "1068", "1069", "1070", "1071", "1072", "1073", "1074", "1075", "1076", "1077", "1078", "1079", "1080"], [], {"ruleId": "1081", "severity": 1, "message": "1082", "line": 1, "column": 40, "nodeType": "1083", "messageId": "1084", "endLine": 1, "endColumn": 48}, {"ruleId": "1081", "severity": 1, "message": "1085", "line": 3, "column": 17, "nodeType": "1083", "messageId": "1084", "endLine": 3, "endColumn": 21}, {"ruleId": "1081", "severity": 1, "message": "1085", "line": 1, "column": 17, "nodeType": "1083", "messageId": "1084", "endLine": 1, "endColumn": 21}, {"ruleId": "1081", "severity": 1, "message": "1086", "line": 2, "column": 18, "nodeType": "1083", "messageId": "1084", "endLine": 2, "endColumn": 22}, {"ruleId": "1081", "severity": 1, "message": "1087", "line": 5, "column": 8, "nodeType": "1083", "messageId": "1084", "endLine": 5, "endColumn": 14}, {"ruleId": "1081", "severity": 1, "message": "1086", "line": 2, "column": 18, "nodeType": "1083", "messageId": "1084", "endLine": 2, "endColumn": 22}, {"ruleId": "1081", "severity": 1, "message": "1087", "line": 5, "column": 8, "nodeType": "1083", "messageId": "1084", "endLine": 5, "endColumn": 14}, {"ruleId": "1081", "severity": 1, "message": "1088", "line": 7, "column": 20, "nodeType": "1083", "messageId": "1084", "endLine": 7, "endColumn": 30}, {"ruleId": "1081", "severity": 1, "message": "1089", "line": 14, "column": 9, "nodeType": "1083", "messageId": "1084", "endLine": 14, "endColumn": 24}, {"ruleId": "1081", "severity": 1, "message": "1090", "line": 34, "column": 10, "nodeType": "1083", "messageId": "1084", "endLine": 34, "endColumn": 21}, {"ruleId": "1081", "severity": 1, "message": "1091", "line": 34, "column": 23, "nodeType": "1083", "messageId": "1084", "endLine": 34, "endColumn": 37}, {"ruleId": "1081", "severity": 1, "message": "1092", "line": 24, "column": 25, "nodeType": "1083", "messageId": "1084", "endLine": 24, "endColumn": 41}, {"ruleId": "1081", "severity": 1, "message": "1093", "line": 26, "column": 10, "nodeType": "1083", "messageId": "1084", "endLine": 26, "endColumn": 23}, {"ruleId": "1094", "severity": 1, "message": "1095", "line": 221, "column": 6, "nodeType": "1096", "endLine": 221, "endColumn": 8, "suggestions": "1097"}, {"ruleId": "1098", "severity": 1, "message": "1099", "line": 229, "column": 5, "nodeType": "1100", "messageId": "1101", "endLine": 260, "endColumn": 6}, {"ruleId": "1081", "severity": 1, "message": "1102", "line": 10, "column": 22, "nodeType": "1083", "messageId": "1084", "endLine": 10, "endColumn": 32}, {"ruleId": "1081", "severity": 1, "message": "1103", "line": 49, "column": 12, "nodeType": "1083", "messageId": "1084", "endLine": 49, "endColumn": 22}, {"ruleId": "1081", "severity": 1, "message": "1104", "line": 50, "column": 12, "nodeType": "1083", "messageId": "1084", "endLine": 50, "endColumn": 24}, {"ruleId": "1094", "severity": 1, "message": "1105", "line": 110, "column": 8, "nodeType": "1096", "endLine": 110, "endColumn": 48, "suggestions": "1106"}, {"ruleId": "1094", "severity": 1, "message": "1107", "line": 94, "column": 6, "nodeType": "1096", "endLine": 94, "endColumn": 26, "suggestions": "1108"}, {"ruleId": "1081", "severity": 1, "message": "1109", "line": 237, "column": 9, "nodeType": "1083", "messageId": "1084", "endLine": 237, "endColumn": 31}, {"ruleId": "1081", "severity": 1, "message": "1110", "line": 474, "column": 17, "nodeType": "1083", "messageId": "1084", "endLine": 474, "endColumn": 26}, {"ruleId": "1081", "severity": 1, "message": "1111", "line": 212, "column": 9, "nodeType": "1083", "messageId": "1084", "endLine": 212, "endColumn": 27}, {"ruleId": "1081", "severity": 1, "message": "1112", "line": 229, "column": 9, "nodeType": "1083", "messageId": "1084", "endLine": 229, "endColumn": 28}, {"ruleId": "1081", "severity": 1, "message": "1113", "line": 13, "column": 11, "nodeType": "1083", "messageId": "1084", "endLine": 13, "endColumn": 19}, {"ruleId": "1094", "severity": 1, "message": "1114", "line": 131, "column": 8, "nodeType": "1096", "endLine": 131, "endColumn": 67, "suggestions": "1115"}, {"ruleId": "1094", "severity": 1, "message": "1116", "line": 142, "column": 8, "nodeType": "1096", "endLine": 142, "endColumn": 39, "suggestions": "1117"}, {"ruleId": "1081", "severity": 1, "message": "1118", "line": 17, "column": 12, "nodeType": "1083", "messageId": "1084", "endLine": 17, "endColumn": 17}, {"ruleId": "1094", "severity": 1, "message": "1119", "line": 60, "column": 8, "nodeType": "1096", "endLine": 60, "endColumn": 10, "suggestions": "1120"}, {"ruleId": "1094", "severity": 1, "message": "1121", "line": 79, "column": 6, "nodeType": "1096", "endLine": 79, "endColumn": 82, "suggestions": "1122"}, {"ruleId": "1081", "severity": 1, "message": "1123", "line": 256, "column": 13, "nodeType": "1083", "messageId": "1084", "endLine": 256, "endColumn": 21}, {"ruleId": "1081", "severity": 1, "message": "1123", "line": 278, "column": 13, "nodeType": "1083", "messageId": "1084", "endLine": 278, "endColumn": 21}, {"ruleId": "1081", "severity": 1, "message": "1124", "line": 467, "column": 9, "nodeType": "1083", "messageId": "1084", "endLine": 467, "endColumn": 23}, {"ruleId": "1094", "severity": 1, "message": "1125", "line": 23, "column": 8, "nodeType": "1096", "endLine": 23, "endColumn": 25, "suggestions": "1126"}, {"ruleId": "1081", "severity": 1, "message": "1127", "line": 4, "column": 10, "nodeType": "1083", "messageId": "1084", "endLine": 4, "endColumn": 14}, {"ruleId": "1094", "severity": 1, "message": "1128", "line": 20, "column": 6, "nodeType": "1096", "endLine": 20, "endColumn": 44, "suggestions": "1129"}, {"ruleId": "1081", "severity": 1, "message": "1130", "line": 1, "column": 38, "nodeType": "1083", "messageId": "1084", "endLine": 1, "endColumn": 49}, {"ruleId": "1081", "severity": 1, "message": "1131", "line": 10, "column": 11, "nodeType": "1083", "messageId": "1084", "endLine": 10, "endColumn": 19}, {"ruleId": "1081", "severity": 1, "message": "1132", "line": 52, "column": 12, "nodeType": "1083", "messageId": "1084", "endLine": 52, "endColumn": 26}, {"ruleId": "1081", "severity": 1, "message": "1133", "line": 267, "column": 11, "nodeType": "1083", "messageId": "1084", "endLine": 267, "endColumn": 40}, {"ruleId": "1081", "severity": 1, "message": "1134", "line": 704, "column": 11, "nodeType": "1083", "messageId": "1084", "endLine": 704, "endColumn": 32}, {"ruleId": "1081", "severity": 1, "message": "1135", "line": 740, "column": 11, "nodeType": "1083", "messageId": "1084", "endLine": 740, "endColumn": 30}, {"ruleId": "1081", "severity": 1, "message": "1113", "line": 19, "column": 11, "nodeType": "1083", "messageId": "1084", "endLine": 19, "endColumn": 19}, {"ruleId": "1094", "severity": 1, "message": "1136", "line": 163, "column": 8, "nodeType": "1096", "endLine": 163, "endColumn": 26, "suggestions": "1137"}, {"ruleId": "1081", "severity": 1, "message": "1138", "line": 183, "column": 11, "nodeType": "1083", "messageId": "1084", "endLine": 183, "endColumn": 31}, {"ruleId": "1081", "severity": 1, "message": "1139", "line": 6, "column": 8, "nodeType": "1083", "messageId": "1084", "endLine": 6, "endColumn": 14}, {"ruleId": "1081", "severity": 1, "message": "1140", "line": 15, "column": 10, "nodeType": "1083", "messageId": "1084", "endLine": 15, "endColumn": 17}, {"ruleId": "1094", "severity": 1, "message": "1141", "line": 80, "column": 6, "nodeType": "1096", "endLine": 80, "endColumn": 8, "suggestions": "1142"}, {"ruleId": "1094", "severity": 1, "message": "1141", "line": 88, "column": 6, "nodeType": "1096", "endLine": 88, "endColumn": 14, "suggestions": "1143"}, {"ruleId": "1081", "severity": 1, "message": "1144", "line": 13, "column": 11, "nodeType": "1083", "messageId": "1084", "endLine": 13, "endColumn": 26}, {"ruleId": "1081", "severity": 1, "message": "1145", "line": 12, "column": 7, "nodeType": "1083", "messageId": "1084", "endLine": 12, "endColumn": 36}, {"ruleId": "1094", "severity": 1, "message": "1146", "line": 109, "column": 8, "nodeType": "1096", "endLine": 109, "endColumn": 10, "suggestions": "1147"}, {"ruleId": "1098", "severity": 1, "message": "1099", "line": 170, "column": 21, "nodeType": "1100", "messageId": "1101", "endLine": 183, "endColumn": 22}, {"ruleId": "1098", "severity": 1, "message": "1099", "line": 477, "column": 9, "nodeType": "1100", "messageId": "1101", "endLine": 556, "endColumn": 10}, {"ruleId": "1094", "severity": 1, "message": "1148", "line": 20, "column": 6, "nodeType": "1096", "endLine": 20, "endColumn": 40, "suggestions": "1149"}, {"ruleId": "1081", "severity": 1, "message": "1150", "line": 5, "column": 22, "nodeType": "1083", "messageId": "1084", "endLine": 5, "endColumn": 32}, {"ruleId": "1081", "severity": 1, "message": "1151", "line": 45, "column": 12, "nodeType": "1083", "messageId": "1084", "endLine": 45, "endColumn": 20}, {"ruleId": "1081", "severity": 1, "message": "1152", "line": 5, "column": 98, "nodeType": "1083", "messageId": "1084", "endLine": 5, "endColumn": 117}, {"ruleId": "1094", "severity": 1, "message": "1153", "line": 351, "column": 6, "nodeType": "1096", "endLine": 351, "endColumn": 20, "suggestions": "1154"}, {"ruleId": "1081", "severity": 1, "message": "1155", "line": 26, "column": 18, "nodeType": "1083", "messageId": "1084", "endLine": 26, "endColumn": 25}, {"ruleId": "1081", "severity": 1, "message": "1156", "line": 27, "column": 19, "nodeType": "1083", "messageId": "1084", "endLine": 27, "endColumn": 27}, {"ruleId": "1081", "severity": 1, "message": "1157", "line": 42, "column": 12, "nodeType": "1083", "messageId": "1084", "endLine": 42, "endColumn": 27}, {"ruleId": "1094", "severity": 1, "message": "1158", "line": 66, "column": 8, "nodeType": "1096", "endLine": 66, "endColumn": 21, "suggestions": "1159"}, {"ruleId": "1081", "severity": 1, "message": "1160", "line": 268, "column": 11, "nodeType": "1083", "messageId": "1084", "endLine": 268, "endColumn": 33}, {"ruleId": "1081", "severity": 1, "message": "1161", "line": 282, "column": 11, "nodeType": "1083", "messageId": "1084", "endLine": 282, "endColumn": 24}, {"ruleId": "1081", "severity": 1, "message": "1162", "line": 14, "column": 5, "nodeType": "1083", "messageId": "1084", "endLine": 14, "endColumn": 22}, {"ruleId": "1081", "severity": 1, "message": "1163", "line": 9, "column": 8, "nodeType": "1083", "messageId": "1084", "endLine": 9, "endColumn": 14}, {"ruleId": "1094", "severity": 1, "message": "1095", "line": 90, "column": 6, "nodeType": "1096", "endLine": 90, "endColumn": 8, "suggestions": "1164"}, {"ruleId": "1081", "severity": 1, "message": "1165", "line": 43, "column": 9, "nodeType": "1083", "messageId": "1084", "endLine": 43, "endColumn": 19}, {"ruleId": "1081", "severity": 1, "message": "1166", "line": 22, "column": 10, "nodeType": "1083", "messageId": "1084", "endLine": 22, "endColumn": 20}, {"ruleId": "1094", "severity": 1, "message": "1167", "line": 94, "column": 6, "nodeType": "1096", "endLine": 94, "endColumn": 27, "suggestions": "1168"}, {"ruleId": "1094", "severity": 1, "message": "1169", "line": 102, "column": 6, "nodeType": "1096", "endLine": 102, "endColumn": 27, "suggestions": "1170"}, {"ruleId": "1081", "severity": 1, "message": "1171", "line": 25, "column": 10, "nodeType": "1083", "messageId": "1084", "endLine": 25, "endColumn": 25}, {"ruleId": "1081", "severity": 1, "message": "1172", "line": 25, "column": 27, "nodeType": "1083", "messageId": "1084", "endLine": 25, "endColumn": 45}, {"ruleId": "1081", "severity": 1, "message": "1173", "line": 426, "column": 9, "nodeType": "1083", "messageId": "1084", "endLine": 426, "endColumn": 25}, {"ruleId": "1081", "severity": 1, "message": "1174", "line": 456, "column": 9, "nodeType": "1083", "messageId": "1084", "endLine": 456, "endColumn": 29}, {"ruleId": "1094", "severity": 1, "message": "1175", "line": 483, "column": 6, "nodeType": "1096", "endLine": 483, "endColumn": 76, "suggestions": "1176"}, {"ruleId": "1081", "severity": 1, "message": "1177", "line": 40, "column": 10, "nodeType": "1083", "messageId": "1084", "endLine": 40, "endColumn": 17}, {"ruleId": "1094", "severity": 1, "message": "1178", "line": 80, "column": 6, "nodeType": "1096", "endLine": 80, "endColumn": 17, "suggestions": "1179"}, {"ruleId": "1081", "severity": 1, "message": "1180", "line": 5, "column": 8, "nodeType": "1083", "messageId": "1084", "endLine": 5, "endColumn": 16}, {"ruleId": "1081", "severity": 1, "message": "1181", "line": 11, "column": 10, "nodeType": "1083", "messageId": "1084", "endLine": 11, "endColumn": 28}, {"ruleId": "1081", "severity": 1, "message": "1182", "line": 11, "column": 30, "nodeType": "1083", "messageId": "1084", "endLine": 11, "endColumn": 51}, {"ruleId": "1081", "severity": 1, "message": "1183", "line": 5, "column": 10, "nodeType": "1083", "messageId": "1084", "endLine": 5, "endColumn": 24}, {"ruleId": "1094", "severity": 1, "message": "1184", "line": 15, "column": 6, "nodeType": "1096", "endLine": 15, "endColumn": 20, "suggestions": "1185"}, {"ruleId": "1081", "severity": 1, "message": "1186", "line": 20, "column": 10, "nodeType": "1083", "messageId": "1084", "endLine": 20, "endColumn": 24}, {"ruleId": "1081", "severity": 1, "message": "1187", "line": 20, "column": 26, "nodeType": "1083", "messageId": "1084", "endLine": 20, "endColumn": 43}, {"ruleId": "1094", "severity": 1, "message": "1188", "line": 33, "column": 6, "nodeType": "1096", "endLine": 33, "endColumn": 16, "suggestions": "1189"}, {"ruleId": "1094", "severity": 1, "message": "1190", "line": 44, "column": 6, "nodeType": "1096", "endLine": 44, "endColumn": 18, "suggestions": "1191"}, {"ruleId": "1081", "severity": 1, "message": "1192", "line": 185, "column": 9, "nodeType": "1083", "messageId": "1084", "endLine": 185, "endColumn": 35}, {"ruleId": "1081", "severity": 1, "message": "1193", "line": 3, "column": 12, "nodeType": "1083", "messageId": "1084", "endLine": 3, "endColumn": 22}, {"ruleId": "1081", "severity": 1, "message": "1194", "line": 3, "column": 51, "nodeType": "1083", "messageId": "1084", "endLine": 3, "endColumn": 61}, {"ruleId": "1081", "severity": 1, "message": "1195", "line": 4, "column": 3, "nodeType": "1083", "messageId": "1084", "endLine": 4, "endColumn": 12}, {"ruleId": "1081", "severity": 1, "message": "1196", "line": 4, "column": 14, "nodeType": "1083", "messageId": "1084", "endLine": 4, "endColumn": 24}, {"ruleId": "1081", "severity": 1, "message": "1197", "line": 4, "column": 54, "nodeType": "1083", "messageId": "1084", "endLine": 4, "endColumn": 60}, {"ruleId": "1094", "severity": 1, "message": "1198", "line": 89, "column": 6, "nodeType": "1096", "endLine": 89, "endColumn": 16, "suggestions": "1199"}, {"ruleId": "1081", "severity": 1, "message": "1200", "line": 151, "column": 9, "nodeType": "1083", "messageId": "1084", "endLine": 151, "endColumn": 22}, {"ruleId": "1081", "severity": 1, "message": "1201", "line": 184, "column": 9, "nodeType": "1083", "messageId": "1084", "endLine": 184, "endColumn": 26}, {"ruleId": "1081", "severity": 1, "message": "1202", "line": 240, "column": 9, "nodeType": "1083", "messageId": "1084", "endLine": 240, "endColumn": 25}, {"ruleId": "1203", "severity": 1, "message": "1204", "line": 573, "column": 54, "nodeType": "1205", "messageId": "1206", "endLine": 573, "endColumn": 56}, {"ruleId": "1203", "severity": 1, "message": "1204", "line": 573, "column": 143, "nodeType": "1205", "messageId": "1206", "endLine": 573, "endColumn": 145}, {"ruleId": "1203", "severity": 1, "message": "1204", "line": 577, "column": 54, "nodeType": "1205", "messageId": "1206", "endLine": 577, "endColumn": 56}, {"ruleId": "1203", "severity": 1, "message": "1204", "line": 577, "column": 148, "nodeType": "1205", "messageId": "1206", "endLine": 577, "endColumn": 150}, {"ruleId": "1203", "severity": 1, "message": "1204", "line": 581, "column": 54, "nodeType": "1205", "messageId": "1206", "endLine": 581, "endColumn": 56}, {"ruleId": "1203", "severity": 1, "message": "1204", "line": 581, "column": 146, "nodeType": "1205", "messageId": "1206", "endLine": 581, "endColumn": 148}, {"ruleId": "1203", "severity": 1, "message": "1204", "line": 585, "column": 54, "nodeType": "1205", "messageId": "1206", "endLine": 585, "endColumn": 56}, {"ruleId": "1203", "severity": 1, "message": "1204", "line": 585, "column": 144, "nodeType": "1205", "messageId": "1206", "endLine": 585, "endColumn": 146}, {"ruleId": "1081", "severity": 1, "message": "1207", "line": 5, "column": 10, "nodeType": "1083", "messageId": "1084", "endLine": 5, "endColumn": 25}, {"ruleId": "1081", "severity": 1, "message": "1208", "line": 12, "column": 11, "nodeType": "1083", "messageId": "1084", "endLine": 12, "endColumn": 18}, {"ruleId": "1094", "severity": 1, "message": "1209", "line": 55, "column": 6, "nodeType": "1096", "endLine": 55, "endColumn": 8, "suggestions": "1210"}, {"ruleId": "1081", "severity": 1, "message": "1211", "line": 1, "column": 27, "nodeType": "1083", "messageId": "1084", "endLine": 1, "endColumn": 36}, {"ruleId": "1081", "severity": 1, "message": "1212", "line": 3, "column": 8, "nodeType": "1083", "messageId": "1084", "endLine": 3, "endColumn": 19}, {"ruleId": "1094", "severity": 1, "message": "1213", "line": 60, "column": 6, "nodeType": "1096", "endLine": 60, "endColumn": 29, "suggestions": "1214"}, {"ruleId": "1081", "severity": 1, "message": "1215", "line": 27, "column": 12, "nodeType": "1083", "messageId": "1084", "endLine": 27, "endColumn": 28}, {"ruleId": "1081", "severity": 1, "message": "1216", "line": 28, "column": 12, "nodeType": "1083", "messageId": "1084", "endLine": 28, "endColumn": 32}, {"ruleId": "1094", "severity": 1, "message": "1217", "line": 48, "column": 8, "nodeType": "1096", "endLine": 48, "endColumn": 63, "suggestions": "1218"}, {"ruleId": "1081", "severity": 1, "message": "1219", "line": 14, "column": 12, "nodeType": "1083", "messageId": "1084", "endLine": 14, "endColumn": 22}, {"ruleId": "1081", "severity": 1, "message": "1220", "line": 15, "column": 12, "nodeType": "1083", "messageId": "1084", "endLine": 15, "endColumn": 26}, {"ruleId": "1094", "severity": 1, "message": "1221", "line": 47, "column": 8, "nodeType": "1096", "endLine": 47, "endColumn": 88, "suggestions": "1222"}, {"ruleId": "1081", "severity": 1, "message": "1223", "line": 8, "column": 3, "nodeType": "1083", "messageId": "1084", "endLine": 8, "endColumn": 37}, {"ruleId": "1081", "severity": 1, "message": "1224", "line": 21, "column": 10, "nodeType": "1083", "messageId": "1084", "endLine": 21, "endColumn": 27}, {"ruleId": "1081", "severity": 1, "message": "1165", "line": 217, "column": 9, "nodeType": "1083", "messageId": "1084", "endLine": 217, "endColumn": 19}, {"ruleId": "1094", "severity": 1, "message": "1225", "line": 26, "column": 6, "nodeType": "1096", "endLine": 26, "endColumn": 48, "suggestions": "1226"}, {"ruleId": "1094", "severity": 1, "message": "1227", "line": 26, "column": 6, "nodeType": "1096", "endLine": 26, "endColumn": 48, "suggestions": "1228"}, {"ruleId": "1081", "severity": 1, "message": "1127", "line": 3, "column": 10, "nodeType": "1083", "messageId": "1084", "endLine": 3, "endColumn": 14}, {"ruleId": "1094", "severity": 1, "message": "1229", "line": 27, "column": 6, "nodeType": "1096", "endLine": 27, "endColumn": 62, "suggestions": "1230"}, {"ruleId": "1081", "severity": 1, "message": "1231", "line": 21, "column": 10, "nodeType": "1083", "messageId": "1084", "endLine": 21, "endColumn": 23}, {"ruleId": "1094", "severity": 1, "message": "1232", "line": 88, "column": 6, "nodeType": "1096", "endLine": 88, "endColumn": 40, "suggestions": "1233"}, {"ruleId": "1094", "severity": 1, "message": "1234", "line": 54, "column": 27, "nodeType": "1083", "endLine": 54, "endColumn": 38}, {"ruleId": "1094", "severity": 1, "message": "1235", "line": 86, "column": 6, "nodeType": "1096", "endLine": 86, "endColumn": 12, "suggestions": "1236"}, {"ruleId": "1081", "severity": 1, "message": "1237", "line": 5, "column": 10, "nodeType": "1083", "messageId": "1084", "endLine": 5, "endColumn": 22}, {"ruleId": "1081", "severity": 1, "message": "1163", "line": 7, "column": 8, "nodeType": "1083", "messageId": "1084", "endLine": 7, "endColumn": 14}, {"ruleId": "1094", "severity": 1, "message": "1238", "line": 107, "column": 8, "nodeType": "1096", "endLine": 107, "endColumn": 39, "suggestions": "1239"}, {"ruleId": "1094", "severity": 1, "message": "1153", "line": 74, "column": 8, "nodeType": "1096", "endLine": 74, "endColumn": 46, "suggestions": "1240"}, {"ruleId": "1081", "severity": 1, "message": "1165", "line": 43, "column": 9, "nodeType": "1083", "messageId": "1084", "endLine": 43, "endColumn": 19}, {"ruleId": "1081", "severity": 1, "message": "1241", "line": 5, "column": 57, "nodeType": "1083", "messageId": "1084", "endLine": 5, "endColumn": 77}, {"ruleId": "1081", "severity": 1, "message": "1163", "line": 6, "column": 8, "nodeType": "1083", "messageId": "1084", "endLine": 6, "endColumn": 14}, {"ruleId": "1094", "severity": 1, "message": "1242", "line": 30, "column": 6, "nodeType": "1096", "endLine": 30, "endColumn": 31, "suggestions": "1243"}, {"ruleId": "1081", "severity": 1, "message": "1166", "line": 22, "column": 10, "nodeType": "1083", "messageId": "1084", "endLine": 22, "endColumn": 20}, {"ruleId": "1094", "severity": 1, "message": "1167", "line": 94, "column": 6, "nodeType": "1096", "endLine": 94, "endColumn": 27, "suggestions": "1244"}, {"ruleId": "1094", "severity": 1, "message": "1169", "line": 102, "column": 6, "nodeType": "1096", "endLine": 102, "endColumn": 27, "suggestions": "1245"}, {"ruleId": "1094", "severity": 1, "message": "1246", "line": 239, "column": 6, "nodeType": "1096", "endLine": 239, "endColumn": 49, "suggestions": "1247"}, {"ruleId": "1081", "severity": 1, "message": "1248", "line": 658, "column": 9, "nodeType": "1083", "messageId": "1084", "endLine": 658, "endColumn": 26}, {"ruleId": "1081", "severity": 1, "message": "1249", "line": 709, "column": 9, "nodeType": "1083", "messageId": "1084", "endLine": 709, "endColumn": 23}, {"ruleId": "1081", "severity": 1, "message": "1250", "line": 835, "column": 19, "nodeType": "1083", "messageId": "1084", "endLine": 835, "endColumn": 29}, {"ruleId": "1081", "severity": 1, "message": "1251", "line": 838, "column": 19, "nodeType": "1083", "messageId": "1084", "endLine": 838, "endColumn": 32}, {"ruleId": "1252", "severity": 1, "message": "1253", "line": 917, "column": 27, "nodeType": "1254", "endLine": 922, "endColumn": 29}, {"ruleId": "1094", "severity": 1, "message": "1169", "line": 39, "column": 6, "nodeType": "1096", "endLine": 39, "endColumn": 8, "suggestions": "1255"}, {"ruleId": "1094", "severity": 1, "message": "1169", "line": 48, "column": 6, "nodeType": "1096", "endLine": 48, "endColumn": 8, "suggestions": "1256"}, {"ruleId": "1081", "severity": 1, "message": "1257", "line": 584, "column": 13, "nodeType": "1083", "messageId": "1084", "endLine": 584, "endColumn": 24}, {"ruleId": "1081", "severity": 1, "message": "1258", "line": 12, "column": 10, "nodeType": "1083", "messageId": "1084", "endLine": 12, "endColumn": 19}, {"ruleId": "1081", "severity": 1, "message": "1259", "line": 12, "column": 21, "nodeType": "1083", "messageId": "1084", "endLine": 12, "endColumn": 33}, {"ruleId": "1094", "severity": 1, "message": "1169", "line": 35, "column": 6, "nodeType": "1096", "endLine": 35, "endColumn": 8, "suggestions": "1260"}, {"ruleId": "1094", "severity": 1, "message": "1169", "line": 44, "column": 6, "nodeType": "1096", "endLine": 44, "endColumn": 8, "suggestions": "1261"}, {"ruleId": "1081", "severity": 1, "message": "1258", "line": 15, "column": 10, "nodeType": "1083", "messageId": "1084", "endLine": 15, "endColumn": 19}, {"ruleId": "1081", "severity": 1, "message": "1259", "line": 15, "column": 21, "nodeType": "1083", "messageId": "1084", "endLine": 15, "endColumn": 33}, {"ruleId": "1081", "severity": 1, "message": "1262", "line": 149, "column": 11, "nodeType": "1083", "messageId": "1084", "endLine": 149, "endColumn": 22}, {"ruleId": "1081", "severity": 1, "message": "1263", "line": 3, "column": 10, "nodeType": "1083", "messageId": "1084", "endLine": 3, "endColumn": 15}, {"ruleId": "1081", "severity": 1, "message": "1118", "line": 15, "column": 12, "nodeType": "1083", "messageId": "1084", "endLine": 15, "endColumn": 17}, {"ruleId": "1081", "severity": 1, "message": "1102", "line": 7, "column": 22, "nodeType": "1083", "messageId": "1084", "endLine": 7, "endColumn": 32}, {"ruleId": "1081", "severity": 1, "message": "1127", "line": 2, "column": 10, "nodeType": "1083", "messageId": "1084", "endLine": 2, "endColumn": 14}, {"ruleId": "1081", "severity": 1, "message": "1264", "line": 6, "column": 39, "nodeType": "1083", "messageId": "1084", "endLine": 6, "endColumn": 51}, {"ruleId": "1081", "severity": 1, "message": "1265", "line": 13, "column": 12, "nodeType": "1083", "messageId": "1084", "endLine": 13, "endColumn": 23}, {"ruleId": "1081", "severity": 1, "message": "1266", "line": 13, "column": 25, "nodeType": "1083", "messageId": "1084", "endLine": 13, "endColumn": 39}, {"ruleId": "1081", "severity": 1, "message": "1267", "line": 124, "column": 11, "nodeType": "1083", "messageId": "1084", "endLine": 124, "endColumn": 30}, {"ruleId": "1081", "severity": 1, "message": "1268", "line": 235, "column": 11, "nodeType": "1083", "messageId": "1084", "endLine": 235, "endColumn": 28}, {"ruleId": "1081", "severity": 1, "message": "1269", "line": 247, "column": 11, "nodeType": "1083", "messageId": "1084", "endLine": 247, "endColumn": 23}, {"ruleId": "1081", "severity": 1, "message": "1270", "line": 255, "column": 11, "nodeType": "1083", "messageId": "1084", "endLine": 255, "endColumn": 23}, {"ruleId": "1094", "severity": 1, "message": "1271", "line": 35, "column": 6, "nodeType": "1096", "endLine": 35, "endColumn": 17, "suggestions": "1272"}, {"ruleId": "1094", "severity": 1, "message": "1273", "line": 45, "column": 6, "nodeType": "1096", "endLine": 45, "endColumn": 17, "suggestions": "1274"}, {"ruleId": "1094", "severity": 1, "message": "1275", "line": 38, "column": 6, "nodeType": "1096", "endLine": 38, "endColumn": 17, "suggestions": "1276"}, {"ruleId": "1094", "severity": 1, "message": "1277", "line": 38, "column": 6, "nodeType": "1096", "endLine": 38, "endColumn": 17, "suggestions": "1278"}, {"ruleId": "1094", "severity": 1, "message": "1279", "line": 35, "column": 6, "nodeType": "1096", "endLine": 35, "endColumn": 17, "suggestions": "1280"}, {"ruleId": "1081", "severity": 1, "message": "1263", "line": 6, "column": 10, "nodeType": "1083", "messageId": "1084", "endLine": 6, "endColumn": 15}, {"ruleId": "1081", "severity": 1, "message": "1281", "line": 7, "column": 10, "nodeType": "1083", "messageId": "1084", "endLine": 7, "endColumn": 20}, {"ruleId": "1094", "severity": 1, "message": "1282", "line": 32, "column": 6, "nodeType": "1096", "endLine": 32, "endColumn": 15, "suggestions": "1283"}, {"ruleId": "1081", "severity": 1, "message": "1284", "line": 87, "column": 9, "nodeType": "1083", "messageId": "1084", "endLine": 87, "endColumn": 21}, {"ruleId": "1081", "severity": 1, "message": "1285", "line": 217, "column": 9, "nodeType": "1083", "messageId": "1084", "endLine": 217, "endColumn": 26}, {"ruleId": "1094", "severity": 1, "message": "1286", "line": 10, "column": 6, "nodeType": "1096", "endLine": 10, "endColumn": 8, "suggestions": "1287"}, {"ruleId": "1081", "severity": 1, "message": "1163", "line": 4, "column": 8, "nodeType": "1083", "messageId": "1084", "endLine": 4, "endColumn": 14}, {"ruleId": "1081", "severity": 1, "message": "1264", "line": 6, "column": 39, "nodeType": "1083", "messageId": "1084", "endLine": 6, "endColumn": 51}, {"ruleId": "1081", "severity": 1, "message": "1265", "line": 13, "column": 12, "nodeType": "1083", "messageId": "1084", "endLine": 13, "endColumn": 23}, {"ruleId": "1081", "severity": 1, "message": "1266", "line": 13, "column": 25, "nodeType": "1083", "messageId": "1084", "endLine": 13, "endColumn": 39}, {"ruleId": "1081", "severity": 1, "message": "1267", "line": 124, "column": 11, "nodeType": "1083", "messageId": "1084", "endLine": 124, "endColumn": 30}, {"ruleId": "1081", "severity": 1, "message": "1268", "line": 235, "column": 11, "nodeType": "1083", "messageId": "1084", "endLine": 235, "endColumn": 28}, {"ruleId": "1081", "severity": 1, "message": "1269", "line": 247, "column": 11, "nodeType": "1083", "messageId": "1084", "endLine": 247, "endColumn": 23}, {"ruleId": "1081", "severity": 1, "message": "1270", "line": 255, "column": 11, "nodeType": "1083", "messageId": "1084", "endLine": 255, "endColumn": 23}, {"ruleId": "1094", "severity": 1, "message": "1288", "line": 86, "column": 8, "nodeType": "1096", "endLine": 86, "endColumn": 10, "suggestions": "1289"}, {"ruleId": "1094", "severity": 1, "message": "1288", "line": 90, "column": 8, "nodeType": "1096", "endLine": 90, "endColumn": 14, "suggestions": "1290"}, {"ruleId": "1094", "severity": 1, "message": "1291", "line": 33, "column": 6, "nodeType": "1096", "endLine": 33, "endColumn": 21, "suggestions": "1292"}, {"ruleId": "1081", "severity": 1, "message": "1293", "line": 173, "column": 9, "nodeType": "1083", "messageId": "1084", "endLine": 173, "endColumn": 27}, {"ruleId": "1081", "severity": 1, "message": "1294", "line": 93, "column": 18, "nodeType": "1083", "messageId": "1084", "endLine": 93, "endColumn": 37}, {"ruleId": "1094", "severity": 1, "message": "1288", "line": 21, "column": 6, "nodeType": "1096", "endLine": 21, "endColumn": 8, "suggestions": "1295"}, {"ruleId": "1094", "severity": 1, "message": "1288", "line": 35, "column": 6, "nodeType": "1096", "endLine": 35, "endColumn": 32, "suggestions": "1296"}, {"ruleId": "1094", "severity": 1, "message": "1297", "line": 42, "column": 6, "nodeType": "1096", "endLine": 42, "endColumn": 23, "suggestions": "1298"}, {"ruleId": "1081", "severity": 1, "message": "1299", "line": 75, "column": 9, "nodeType": "1083", "messageId": "1084", "endLine": 75, "endColumn": 26}, {"ruleId": "1094", "severity": 1, "message": "1300", "line": 163, "column": 6, "nodeType": "1096", "endLine": 163, "endColumn": 33, "suggestions": "1301"}, {"ruleId": "1094", "severity": 1, "message": "1209", "line": 187, "column": 6, "nodeType": "1096", "endLine": 187, "endColumn": 8, "suggestions": "1302"}, {"ruleId": "1303", "severity": 1, "message": "1304", "line": 77, "column": 13, "nodeType": "1254", "endLine": 77, "endColumn": 42}, {"ruleId": "1081", "severity": 1, "message": "1305", "line": 15, "column": 10, "nodeType": "1083", "messageId": "1084", "endLine": 15, "endColumn": 24}, {"ruleId": "1094", "severity": 1, "message": "1306", "line": 50, "column": 6, "nodeType": "1096", "endLine": 50, "endColumn": 39, "suggestions": "1307"}, {"ruleId": "1081", "severity": 1, "message": "1163", "line": 4, "column": 8, "nodeType": "1083", "messageId": "1084", "endLine": 4, "endColumn": 14}, {"ruleId": "1081", "severity": 1, "message": "1308", "line": 30, "column": 11, "nodeType": "1083", "messageId": "1084", "endLine": 30, "endColumn": 23}, {"ruleId": "1081", "severity": 1, "message": "1165", "line": 21, "column": 9, "nodeType": "1083", "messageId": "1084", "endLine": 21, "endColumn": 19}, {"ruleId": "1081", "severity": 1, "message": "1309", "line": 67, "column": 9, "nodeType": "1083", "messageId": "1084", "endLine": 67, "endColumn": 23}, {"ruleId": "1081", "severity": 1, "message": "1165", "line": 80, "column": 9, "nodeType": "1083", "messageId": "1084", "endLine": 80, "endColumn": 19}, {"ruleId": "1081", "severity": 1, "message": "1163", "line": 6, "column": 8, "nodeType": "1083", "messageId": "1084", "endLine": 6, "endColumn": 14}, {"ruleId": "1081", "severity": 1, "message": "1103", "line": 21, "column": 12, "nodeType": "1083", "messageId": "1084", "endLine": 21, "endColumn": 22}, {"ruleId": "1094", "severity": 1, "message": "1121", "line": 39, "column": 8, "nodeType": "1096", "endLine": 39, "endColumn": 85, "suggestions": "1310"}, {"ruleId": "1081", "severity": 1, "message": "1311", "line": 155, "column": 11, "nodeType": "1083", "messageId": "1084", "endLine": 155, "endColumn": 23}, {"ruleId": "1094", "severity": 1, "message": "1312", "line": 63, "column": 6, "nodeType": "1096", "endLine": 63, "endColumn": 73, "suggestions": "1313"}, {"ruleId": "1303", "severity": 1, "message": "1314", "line": 184, "column": 13, "nodeType": "1254", "endLine": 188, "endColumn": 15}, {"ruleId": "1094", "severity": 1, "message": "1315", "line": 38, "column": 6, "nodeType": "1096", "endLine": 38, "endColumn": 25, "suggestions": "1316"}, {"ruleId": "1081", "severity": 1, "message": "1317", "line": 5, "column": 55, "nodeType": "1083", "messageId": "1084", "endLine": 5, "endColumn": 68}, {"ruleId": "1094", "severity": 1, "message": "1318", "line": 20, "column": 6, "nodeType": "1096", "endLine": 20, "endColumn": 16, "suggestions": "1319"}, {"ruleId": "1081", "severity": 1, "message": "1281", "line": 7, "column": 14, "nodeType": "1083", "messageId": "1084", "endLine": 7, "endColumn": 24}, {"ruleId": "1094", "severity": 1, "message": "1282", "line": 32, "column": 10, "nodeType": "1096", "endLine": 32, "endColumn": 19, "suggestions": "1320"}, {"ruleId": "1081", "severity": 1, "message": "1284", "line": 87, "column": 13, "nodeType": "1083", "messageId": "1084", "endLine": 87, "endColumn": 25}, {"ruleId": "1081", "severity": 1, "message": "1285", "line": 217, "column": 13, "nodeType": "1083", "messageId": "1084", "endLine": 217, "endColumn": 30}, {"ruleId": "1081", "severity": 1, "message": "1321", "line": 56, "column": 10, "nodeType": "1083", "messageId": "1084", "endLine": 56, "endColumn": 20}, {"ruleId": "1081", "severity": 1, "message": "1322", "line": 89, "column": 25, "nodeType": "1083", "messageId": "1084", "endLine": 89, "endColumn": 41}, {"ruleId": "1081", "severity": 1, "message": "1323", "line": 93, "column": 9, "nodeType": "1083", "messageId": "1084", "endLine": 93, "endColumn": 26}, {"ruleId": "1094", "severity": 1, "message": "1324", "line": 124, "column": 6, "nodeType": "1096", "endLine": 124, "endColumn": 60, "suggestions": "1325"}, {"ruleId": "1081", "severity": 1, "message": "1326", "line": 278, "column": 9, "nodeType": "1083", "messageId": "1084", "endLine": 278, "endColumn": 33}, {"ruleId": "1081", "severity": 1, "message": "1327", "line": 52, "column": 13, "nodeType": "1083", "messageId": "1084", "endLine": 52, "endColumn": 21}, {"ruleId": "1094", "severity": 1, "message": "1328", "line": 23, "column": 8, "nodeType": "1096", "endLine": 23, "endColumn": 10, "suggestions": "1329"}, {"ruleId": "1081", "severity": 1, "message": "1330", "line": 94, "column": 11, "nodeType": "1083", "messageId": "1084", "endLine": 94, "endColumn": 23}, {"ruleId": "1081", "severity": 1, "message": "1166", "line": 18, "column": 10, "nodeType": "1083", "messageId": "1084", "endLine": 18, "endColumn": 20}, {"ruleId": "1094", "severity": 1, "message": "1312", "line": 43, "column": 6, "nodeType": "1096", "endLine": 43, "endColumn": 61, "suggestions": "1331"}, {"ruleId": "1081", "severity": 1, "message": "1332", "line": 628, "column": 39, "nodeType": "1083", "messageId": "1084", "endLine": 628, "endColumn": 52}, {"ruleId": "1081", "severity": 1, "message": "1333", "line": 629, "column": 11, "nodeType": "1083", "messageId": "1084", "endLine": 629, "endColumn": 19}, {"ruleId": "1081", "severity": 1, "message": "1088", "line": 7, "column": 32, "nodeType": "1083", "messageId": "1084", "endLine": 7, "endColumn": 42}, {"ruleId": "1094", "severity": 1, "message": "1334", "line": 71, "column": 6, "nodeType": "1096", "endLine": 71, "endColumn": 45, "suggestions": "1335"}, {"ruleId": "1094", "severity": 1, "message": "1336", "line": 127, "column": 6, "nodeType": "1096", "endLine": 127, "endColumn": 8, "suggestions": "1337"}, {"ruleId": "1081", "severity": 1, "message": "1338", "line": 13, "column": 10, "nodeType": "1083", "messageId": "1084", "endLine": 13, "endColumn": 14}, {"ruleId": "1094", "severity": 1, "message": "1339", "line": 70, "column": 6, "nodeType": "1096", "endLine": 70, "endColumn": 24, "suggestions": "1340"}, {"ruleId": "1081", "severity": 1, "message": "1163", "line": 7, "column": 8, "nodeType": "1083", "messageId": "1084", "endLine": 7, "endColumn": 14}, {"ruleId": "1094", "severity": 1, "message": "1341", "line": 61, "column": 6, "nodeType": "1096", "endLine": 61, "endColumn": 22, "suggestions": "1342"}, {"ruleId": "1081", "severity": 1, "message": "1193", "line": 3, "column": 12, "nodeType": "1083", "messageId": "1084", "endLine": 3, "endColumn": 22}, {"ruleId": "1081", "severity": 1, "message": "1194", "line": 3, "column": 51, "nodeType": "1083", "messageId": "1084", "endLine": 3, "endColumn": 61}, {"ruleId": "1081", "severity": 1, "message": "1195", "line": 4, "column": 3, "nodeType": "1083", "messageId": "1084", "endLine": 4, "endColumn": 12}, {"ruleId": "1081", "severity": 1, "message": "1196", "line": 4, "column": 14, "nodeType": "1083", "messageId": "1084", "endLine": 4, "endColumn": 24}, {"ruleId": "1081", "severity": 1, "message": "1197", "line": 4, "column": 54, "nodeType": "1083", "messageId": "1084", "endLine": 4, "endColumn": 60}, {"ruleId": "1081", "severity": 1, "message": "1263", "line": 7, "column": 10, "nodeType": "1083", "messageId": "1084", "endLine": 7, "endColumn": 15}, {"ruleId": "1081", "severity": 1, "message": "1343", "line": 13, "column": 34, "nodeType": "1083", "messageId": "1084", "endLine": 13, "endColumn": 46}, {"ruleId": "1081", "severity": 1, "message": "1344", "line": 34, "column": 7, "nodeType": "1083", "messageId": "1084", "endLine": 34, "endColumn": 13}, {"ruleId": "1081", "severity": 1, "message": "1345", "line": 41, "column": 23, "nodeType": "1083", "messageId": "1084", "endLine": 41, "endColumn": 37}, {"ruleId": "1081", "severity": 1, "message": "1346", "line": 42, "column": 27, "nodeType": "1083", "messageId": "1084", "endLine": 42, "endColumn": 45}, {"ruleId": "1094", "severity": 1, "message": "1198", "line": 89, "column": 6, "nodeType": "1096", "endLine": 89, "endColumn": 16, "suggestions": "1347"}, {"ruleId": "1081", "severity": 1, "message": "1200", "line": 123, "column": 9, "nodeType": "1083", "messageId": "1084", "endLine": 123, "endColumn": 22}, {"ruleId": "1081", "severity": 1, "message": "1201", "line": 156, "column": 9, "nodeType": "1083", "messageId": "1084", "endLine": 156, "endColumn": 26}, {"ruleId": "1081", "severity": 1, "message": "1202", "line": 174, "column": 9, "nodeType": "1083", "messageId": "1084", "endLine": 174, "endColumn": 25}, {"ruleId": "1203", "severity": 1, "message": "1204", "line": 507, "column": 54, "nodeType": "1205", "messageId": "1206", "endLine": 507, "endColumn": 56}, {"ruleId": "1203", "severity": 1, "message": "1204", "line": 507, "column": 143, "nodeType": "1205", "messageId": "1206", "endLine": 507, "endColumn": 145}, {"ruleId": "1203", "severity": 1, "message": "1204", "line": 511, "column": 54, "nodeType": "1205", "messageId": "1206", "endLine": 511, "endColumn": 56}, {"ruleId": "1203", "severity": 1, "message": "1204", "line": 511, "column": 148, "nodeType": "1205", "messageId": "1206", "endLine": 511, "endColumn": 150}, {"ruleId": "1203", "severity": 1, "message": "1204", "line": 515, "column": 54, "nodeType": "1205", "messageId": "1206", "endLine": 515, "endColumn": 56}, {"ruleId": "1203", "severity": 1, "message": "1204", "line": 515, "column": 146, "nodeType": "1205", "messageId": "1206", "endLine": 515, "endColumn": 148}, {"ruleId": "1203", "severity": 1, "message": "1204", "line": 519, "column": 54, "nodeType": "1205", "messageId": "1206", "endLine": 519, "endColumn": 56}, {"ruleId": "1203", "severity": 1, "message": "1204", "line": 519, "column": 144, "nodeType": "1205", "messageId": "1206", "endLine": 519, "endColumn": 146}, "no-unused-vars", "'Navigate' is defined but never used.", "Identifier", "unusedVar", "'lazy' is defined but never used.", "'Link' is defined but never used.", "'Footer' is defined but never used.", "'sendOTPApi' is defined but never used.", "'organization_id' is assigned a value but never used.", "'isFormValid' is assigned a value but never used.", "'setIsFormValid' is assigned a value but never used.", "'setClassroomData' is assigned a value but never used.", "'dashboardData' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchDashboardData'. Either include it or remove the dependency array.", "ArrayExpression", ["1348"], "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "'decodeData' is defined but never used.", "'totalPages' is assigned a value but never used.", "'totalRecords' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getAllClassroomData'. Either include it or remove the dependency array.", ["1349"], "React Hook useEffect has a missing dependency: 'fetchAssignments'. Either include it or remove the dependency array.", ["1350"], "'handleUpdateAssignment' is assigned a value but never used.", "'dueStatus' is assigned a value but never used.", "'formatDateForInput' is assigned a value but never used.", "'formatToLocalString' is assigned a value but never used.", "'navigate' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchTrainees'. Either include it or remove the dependency array.", ["1351"], "React Hook useEffect has a missing dependency: 'fetchAvailableTrainees'. Either include it or remove the dependency array.", ["1352"], "'error' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchCourseList'. Either include it or remove the dependency array.", ["1353"], "React Hook useEffect has a missing dependency: 'fetchLiveClasses'. Either include it or remove the dependency array.", ["1354"], "'response' is assigned a value but never used.", "'formatDateTime' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchCourseStats'. Either include it or remove the dependency array.", ["1355"], "'Icon' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchAssessmentDetails'. Either include it or remove the dependency array.", ["1356"], "'useCallback' is defined but never used.", "'location' is assigned a value but never used.", "'correctAnswers' is assigned a value but never used.", "'handleOptionContentTypeChange' is assigned a value but never used.", "'renderQuestionContent' is assigned a value but never used.", "'renderOptionContent' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'getDocumentDetailsById'. Either include it or remove the dependency array.", ["1357"], "'handleDocumentRemove' is assigned a value but never used.", "'Loader' is defined but never used.", "'loading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchCourseModule'. Either include it or remove the dependency array.", ["1358"], ["1359"], "'decodedCourseId' is assigned a value but never used.", "'REACT_APP_BITMOVIN_PLAYER_KEY' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchModuleContent'. Either include it or remove the dependency array.", ["1360"], "React Hook useEffect has a missing dependency: 'fetchSurveyDetails'. Either include it or remove the dependency array.", ["1361"], "'encodeData' is defined but never used.", "'newVideo' is assigned a value but never used.", "'getQuestionBankById' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchQuestions'. Either include it or remove the dependency array.", ["1362"], "'setPage' is assigned a value but never used.", "'setLimit' is assigned a value but never used.", "'documentPreview' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchCertificates'. Either include it or remove the dependency array.", ["1363"], "'handleCreateModalClose' is assigned a value but never used.", "'togglePreview' is assigned a value but never used.", "'sendAnnouncements' is defined but never used.", "'NoData' is defined but never used.", ["1364"], "'formatDate' is assigned a value but never used.", "'totalCount' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'notifications.length'. Either include it or remove the dependency array.", ["1365"], "React Hook useEffect has a missing dependency: 'fetchNotifications'. Either include it or remove the dependency array.", ["1366"], "'showDeleteModal' is assigned a value but never used.", "'setShowDeleteModal' is assigned a value but never used.", "'handleBulkUpload' is assigned a value but never used.", "'handleSampleDownload' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchTraineesData'. Either include it or remove the dependency array.", ["1367"], "'tabData' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAnalyticsData'. Either include it or remove the dependency array.", ["1368"], "'TodoList' is defined but never used.", "'monthlyPerformance' is assigned a value but never used.", "'setMonthlyPerformance' is assigned a value but never used.", "'changePassword' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchResults'. Either include it or remove the dependency array.", ["1369"], "'courseProgress' is assigned a value but never used.", "'setCourseProgress' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchModuleList'. Either include it or remove the dependency array.", ["1370"], "React Hook useEffect has a missing dependency: 'selectedContent'. Either include it or remove the dependency array.", ["1371"], "'handleCourseProgressUpdate' is assigned a value but never used.", "'FaDownload' is defined but never used.", "'FaShareAlt' is defined but never used.", "'FaTwitter' is defined but never used.", "'FaLinkedin' is defined but never used.", "'FaLock' is defined but never used.", "React Hook useEffect has a missing dependency: 'getCourseDetails'. Either include it or remove the dependency array.", ["1372"], "'courseModules' is assigned a value but never used.", "'handleWatchCourse' is assigned a value but never used.", "'hasSubCategories' is assigned a value but never used.", "no-mixed-operators", "Unexpected mix of '&&' and '||'. Use parentheses to clarify the intended order of operations.", "LogicalExpression", "unexpectedMixedOperator", "'saveRecentVideo' is defined but never used.", "'videoId' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'initPlayer'. Either include it or remove the dependency array.", ["1373"], "'useEffect' is defined but never used.", "'AccountInfo' is defined but never used.", "React Hook useEffect has a missing dependency: 'sendtovalidation'. Either include it or remove the dependency array.", ["1374"], "'hasMoreQuestions' is assigned a value but never used.", "'availableCurrentPage' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAssessmentQuestions'. Either include it or remove the dependency array.", ["1375"], "'allResults' is assigned a value but never used.", "'groupedResults' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchAnalyticsData' and 'fetchResults'. Either include them or remove the dependency array.", ["1376"], "'gradeClassroomAssignmentSubmission' is defined but never used.", "'assignmentDetails' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAssessmentData'. Either include it or remove the dependency array.", ["1377"], "React Hook useEffect has a missing dependency: 'fetchSurveyData'. Either include it or remove the dependency array.", ["1378"], "React Hook useEffect has a missing dependency: 'fetchTraineeProgress'. Either include it or remove the dependency array.", ["1379"], "'classroomName' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'classroom_id'. Either include it or remove the dependency array.", ["1380"], "React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead.", "React Hook useEffect has missing dependencies: 'fetchClassrooms' and 'search'. Either include them or remove the dependency array.", ["1381"], "'selectedFile' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'handleSubmit' and 'timeLeft'. Either include them or remove the dependency array.", ["1382"], ["1383"], "'editOrganisationUser' is defined but never used.", "React Hook useEffect has a missing dependency: 'getTicketData'. Either include it or remove the dependency array.", ["1384"], ["1385"], ["1386"], "React Hook useEffect has a missing dependency: 'currentUserId'. Either include it or remove the dependency array.", ["1387"], "'toggleEmojiPicker' is assigned a value but never used.", "'dislikeMessage' is assigned a value but never used.", "'likesArray' is assigned a value but never used.", "'dislikesArray' is assigned a value but never used.", "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", ["1388"], ["1389"], "'moduleIndex' is assigned a value but never used.", "'collapsed' is assigned a value but never used.", "'setCollapsed' is assigned a value but never used.", ["1390"], ["1391"], "'classroomId' is assigned a value but never used.", "'toast' is defined but never used.", "'validateDate' is defined but never used.", "'profileData' is assigned a value but never used.", "'setProfileData' is assigned a value but never used.", "'handleCountrySelect' is assigned a value but never used.", "'handleImageChange' is assigned a value but never used.", "'handleChange' is assigned a value but never used.", "'handleSubmit' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchClassroomData'. Either include it or remove the dependency array.", ["1392"], "React Hook useEffect has a missing dependency: 'fetchCourseData'. Either include it or remove the dependency array.", ["1393"], "React Hook useEffect has a missing dependency: 'fetchPaymentData'. Either include it or remove the dependency array.", ["1394"], "React Hook useEffect has a missing dependency: 'fetchTicketsData'. Either include it or remove the dependency array.", ["1395"], "React Hook useEffect has a missing dependency: 'fetchCertificatesData'. Either include it or remove the dependency array.", ["1396"], "'updateLike' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchComments'. Either include it or remove the dependency array.", ["1397"], "'fetchReplies' is assigned a value but never used.", "'handleDeleteReply' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchMonthlyPerformance'. Either include it or remove the dependency array.", ["1398"], "React Hook useEffect has a missing dependency: 'searchTerm'. Either include it or remove the dependency array.", ["1399"], ["1400"], "React Hook useEffect has a missing dependency: 'getAssessmentQuestions'. Either include it or remove the dependency array.", ["1401"], "'getScorePercentage' is assigned a value but never used.", "'fetchCourseProgress' is defined but never used.", ["1402"], ["1403"], "React Hook useEffect has a missing dependency: 'contentData'. Either include it or remove the dependency array.", ["1404"], "'saveVideoProgress' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'contentData?.title'. Either include it or remove the dependency array.", ["1405"], ["1406"], "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "'totalQuestions' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchSurveyQuestions'. Either include it or remove the dependency array.", ["1407"], "'filteredData' is assigned a value but never used.", "'getStatusClass' is assigned a value but never used.", ["1408"], "'clearFilters' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchResources'. Either include it or remove the dependency array.", ["1409"], "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "React Hook useEffect has a missing dependency: 'fetchReviews'. Either include it or remove the dependency array.", ["1410"], "'updateComment' is defined but never used.", "React Hook useEffect has missing dependencies: 'getNotesData' and 'videoId'. Either include them or remove the dependency array.", ["1411"], ["1412"], "'actionType' is assigned a value but never used.", "'setSearchCountry' is assigned a value but never used.", "'filteredCountries' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["1413"], "'handleStatusFilterChange' is assigned a value but never used.", "'newState' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchTasks'. Either include it or remove the dependency array.", ["1414"], "'getTaskBadge' is assigned a value but never used.", ["1415"], "'resource_name' is assigned a value but never used.", "'fileName' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'initializeZoomMeeting'. Either include it or remove the dependency array.", ["1416"], "React Hook useEffect has missing dependencies: 'courseId', 'sendToValidation', and 'txnId'. Either include them or remove the dependency array.", ["1417"], "'data' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleCancelValidation'. Either include it or remove the dependency array.", ["1418"], "React Hook useEffect has missing dependencies: 'courseData?.banner_image', 'courseData?.course_desc', 'courseData?.course_id', 'courseData?.course_name', 'courseData?.course_price', 'courseData?.course_type', 'courseData?.currency', 'courseData?.discountCode', 'courseData?.discountValue', and 'courseData?.points'. Either include them or remove the dependency array.", ["1419"], "'AddMyCourses' is defined but never used.", "'domain' is assigned a value but never used.", "'setIsEnrolling' is assigned a value but never used.", "'setIsPaidEnrolling' is assigned a value but never used.", ["1420"], {"desc": "1421", "fix": "1422"}, {"desc": "1423", "fix": "1424"}, {"desc": "1425", "fix": "1426"}, {"desc": "1427", "fix": "1428"}, {"desc": "1429", "fix": "1430"}, {"desc": "1431", "fix": "1432"}, {"desc": "1433", "fix": "1434"}, {"desc": "1435", "fix": "1436"}, {"desc": "1437", "fix": "1438"}, {"desc": "1439", "fix": "1440"}, {"desc": "1441", "fix": "1442"}, {"desc": "1443", "fix": "1444"}, {"desc": "1445", "fix": "1446"}, {"desc": "1447", "fix": "1448"}, {"desc": "1449", "fix": "1450"}, {"desc": "1451", "fix": "1452"}, {"desc": "1421", "fix": "1453"}, {"desc": "1454", "fix": "1455"}, {"desc": "1456", "fix": "1457"}, {"desc": "1458", "fix": "1459"}, {"desc": "1460", "fix": "1461"}, {"desc": "1462", "fix": "1463"}, {"desc": "1464", "fix": "1465"}, {"desc": "1466", "fix": "1467"}, {"desc": "1468", "fix": "1469"}, {"desc": "1470", "fix": "1471"}, {"desc": "1472", "fix": "1473"}, {"desc": "1474", "fix": "1475"}, {"desc": "1476", "fix": "1477"}, {"desc": "1478", "fix": "1479"}, {"desc": "1480", "fix": "1481"}, {"desc": "1482", "fix": "1483"}, {"desc": "1484", "fix": "1485"}, {"desc": "1486", "fix": "1487"}, {"desc": "1488", "fix": "1489"}, {"desc": "1490", "fix": "1491"}, {"desc": "1492", "fix": "1493"}, {"desc": "1454", "fix": "1494"}, {"desc": "1456", "fix": "1495"}, {"desc": "1496", "fix": "1497"}, {"desc": "1498", "fix": "1499"}, {"desc": "1498", "fix": "1500"}, {"desc": "1498", "fix": "1501"}, {"desc": "1498", "fix": "1502"}, {"desc": "1503", "fix": "1504"}, {"desc": "1505", "fix": "1506"}, {"desc": "1507", "fix": "1508"}, {"desc": "1509", "fix": "1510"}, {"desc": "1511", "fix": "1512"}, {"desc": "1513", "fix": "1514"}, {"desc": "1515", "fix": "1516"}, {"desc": "1517", "fix": "1518"}, {"desc": "1519", "fix": "1520"}, {"desc": "1521", "fix": "1522"}, {"desc": "1517", "fix": "1523"}, {"desc": "1524", "fix": "1525"}, {"desc": "1526", "fix": "1527"}, {"desc": "1528", "fix": "1529"}, {"desc": "1470", "fix": "1530"}, {"desc": "1531", "fix": "1532"}, {"desc": "1533", "fix": "1534"}, {"desc": "1535", "fix": "1536"}, {"desc": "1537", "fix": "1538"}, {"desc": "1539", "fix": "1540"}, {"desc": "1513", "fix": "1541"}, {"desc": "1542", "fix": "1543"}, {"desc": "1544", "fix": "1545"}, {"desc": "1546", "fix": "1547"}, {"desc": "1548", "fix": "1549"}, {"desc": "1550", "fix": "1551"}, {"desc": "1552", "fix": "1553"}, {"desc": "1554", "fix": "1555"}, {"desc": "1468", "fix": "1556"}, "Update the dependencies array to be: [fetchDashboardData]", {"range": "1557", "text": "1558"}, "Update the dependencies array to be: [currentPage, getAllClassroomData, itemsPerPage, searchQuery]", {"range": "1559", "text": "1560"}, "Update the dependencies array to be: [decodedClassroomId, fetchAssignments]", {"range": "1561", "text": "1562"}, "Update the dependencies array to be: [decodedClassroomId, currentPage, itemsPerPage, searchTerm, fetchTrainees]", {"range": "1563", "text": "1564"}, "Update the dependencies array to be: [fetchAvailableTrainees, modalSearchTerm, showAddModal]", {"range": "1565", "text": "1566"}, "Update the dependencies array to be: [fetchCourseList]", {"range": "1567", "text": "1568"}, "Update the dependencies array to be: [classroomId, currentPage, itemsPerPage, debouncedSearchQuery, statusFilter, fetchLiveClasses]", {"range": "1569", "text": "1570"}, "Update the dependencies array to be: [decodedCourseId, fetchCourseStats]", {"range": "1571", "text": "1572"}, "Update the dependencies array to be: [decodedAssessmentId, decodedCourseId, fetchAssessmentDetails]", {"range": "1573", "text": "1574"}, "Update the dependencies array to be: [decodedContentId, getDocumentDetailsById]", {"range": "1575", "text": "1576"}, "Update the dependencies array to be: [fetchCourseModule]", {"range": "1577", "text": "1578"}, "Update the dependencies array to be: [fetchCourseModule, search]", {"range": "1579", "text": "1580"}, "Update the dependencies array to be: [fetchModuleContent]", {"range": "1581", "text": "1582"}, "Update the dependencies array to be: [decodedSurveyId, decodedCourseId, fetchSurveyDetails]", {"range": "1583", "text": "1584"}, "Update the dependencies array to be: [fetchQuestions, itemsPerPage]", {"range": "1585", "text": "1586"}, "Update the dependencies array to be: [page, limit, fetchCertificates]", {"range": "1587", "text": "1588"}, {"range": "1589", "text": "1558"}, "Update the dependencies array to be: [debouncedSearchTerm, notifications.length]", {"range": "1590", "text": "1591"}, "Update the dependencies array to be: [debouncedSearchTerm, fetchNotifications]", {"range": "1592", "text": "1593"}, "Update the dependencies array to be: [pagination.page, pagination.limit, activeFilter, debouncedSearchTerm, fetchTraineesData]", {"range": "1594", "text": "1595"}, "Update the dependencies array to be: [decodedId, fetchAnalyticsData]", {"range": "1596", "text": "1597"}, "Update the dependencies array to be: [assessmentId, fetchResults]", {"range": "1598", "text": "1599"}, "Update the dependencies array to be: [courseId, fetchModuleList]", {"range": "1600", "text": "1601"}, "Update the dependencies array to be: [moduleList, selectedContent]", {"range": "1602", "text": "1603"}, "Update the dependencies array to be: [courseId, getCourseDetails]", {"range": "1604", "text": "1605"}, "Update the dependencies array to be: [initPlayer]", {"range": "1606", "text": "1607"}, "Update the dependencies array to be: [course_id, sendtovalidation, session_id]", {"range": "1608", "text": "1609"}, "Update the dependencies array to be: [decodedClassroomId, decodedAssessmentId, itemsPerPage, fetchAssessmentQuestions]", {"range": "1610", "text": "1611"}, "Update the dependencies array to be: [decodedClassroomId, decodedAssessmentId, currentPage, itemsPerPage, searchTerm, fetchResults, fetchAnalyticsData]", {"range": "1612", "text": "1613"}, "Update the dependencies array to be: [decodedCourseId, search, pagination.page, fetchAssessmentData]", {"range": "1614", "text": "1615"}, "Update the dependencies array to be: [decodedCourseId, search, pagination.page, fetchSurveyData]", {"range": "1616", "text": "1617"}, "Update the dependencies array to be: [decodedCourseId, search, statusFilter, pagination.page, fetchTraineeProgress]", {"range": "1618", "text": "1619"}, "Update the dependencies array to be: [classroom_id, encodedClassroomID, urlActiveTab]", {"range": "1620", "text": "1621"}, "Update the dependencies array to be: [fetchClassrooms, page, search]", {"range": "1622", "text": "1623"}, "Update the dependencies array to be: [handleSubmit, quizStarted, submissionResult, timeLeft]", {"range": "1624", "text": "1625"}, "Update the dependencies array to be: [assignment_id, classroom_id, fetchQuestions, user_id]", {"range": "1626", "text": "1627"}, "Update the dependencies array to be: [page, limit, searchTerm, getTicketData]", {"range": "1628", "text": "1629"}, {"range": "1630", "text": "1591"}, {"range": "1631", "text": "1593"}, "Update the dependencies array to be: [classroom_id, currentUserId, group_id, group_name, token]", {"range": "1632", "text": "1633"}, "Update the dependencies array to be: [fetchNotifications]", {"range": "1634", "text": "1635"}, {"range": "1636", "text": "1635"}, {"range": "1637", "text": "1635"}, {"range": "1638", "text": "1635"}, "Update the dependencies array to be: [fetchClassroomData, traineeId]", {"range": "1639", "text": "1640"}, "Update the dependencies array to be: [fetchCourseData, traineeId]", {"range": "1641", "text": "1642"}, "Update the dependencies array to be: [fetchPaymentData, traineeId]", {"range": "1643", "text": "1644"}, "Update the dependencies array to be: [fetchTicketsData, traineeId]", {"range": "1645", "text": "1646"}, "Update the dependencies array to be: [fetchCertificatesData, traineeId]", {"range": "1647", "text": "1648"}, "Update the dependencies array to be: [fetchComments, videoId]", {"range": "1649", "text": "1650"}, "Update the dependencies array to be: [fetchMonthlyPerformance]", {"range": "1651", "text": "1652"}, "Update the dependencies array to be: [searchTerm]", {"range": "1653", "text": "1654"}, "Update the dependencies array to be: [page, searchTerm]", {"range": "1655", "text": "1656"}, "Update the dependencies array to be: [getAssessmentQuestions, moduleData.id]", {"range": "1657", "text": "1658"}, {"range": "1659", "text": "1654"}, "Update the dependencies array to be: [page, hasMore, isLoading, searchTerm]", {"range": "1660", "text": "1661"}, "Update the dependencies array to be: [contentData, contentData?.id]", {"range": "1662", "text": "1663"}, "Update the dependencies array to be: [videoUrl, contentData?.id, contentData?.title]", {"range": "1664", "text": "1665"}, {"range": "1666", "text": "1607"}, "Update the dependencies array to be: [surveyId, moduleData?.completed, fetchSurveyQuestions]", {"range": "1667", "text": "1668"}, "Update the dependencies array to be: [classroom_id, currentPage, itemsPerPage, debouncedSearchQuery, statusFilter, fetchLiveClasses]", {"range": "1669", "text": "1670"}, "Update the dependencies array to be: [decodedClassroomId, currentPage, searchTerm, selectedResourceType, fetchResources]", {"range": "1671", "text": "1672"}, "Update the dependencies array to be: [videoId, courseId, fetchReviews]", {"range": "1673", "text": "1674"}, "Update the dependencies array to be: [getNotesData, moduleId, videoId]", {"range": "1675", "text": "1676"}, {"range": "1677", "text": "1650"}, "Update the dependencies array to be: [currentPage, fetchUsers, itemsPerPage, searchQuery, statusFilter]", {"range": "1678", "text": "1679"}, "Update the dependencies array to be: [fetchTasks]", {"range": "1680", "text": "1681"}, "Update the dependencies array to be: [decodedClassroomId, currentPage, selectedResourceType, fetchResources]", {"range": "1682", "text": "1683"}, "Update the dependencies array to be: [initializationStarted, loading, error, initializeZoomMeeting]", {"range": "1684", "text": "1685"}, "Update the dependencies array to be: [courseId, sendToValidation, txnId]", {"range": "1686", "text": "1687"}, "Update the dependencies array to be: [course_id, handleCancelValidation, txnid]", {"range": "1688", "text": "1689"}, "Update the dependencies array to be: [courseData?.banner_image, courseData?.course_desc, courseData?.course_id, courseData?.course_name, courseData?.course_price, courseData?.course_type, courseData?.currency, courseData?.discountCode, courseData?.discountValue, courseData?.points, location.state]", {"range": "1690", "text": "1691"}, {"range": "1692", "text": "1605"}, [8379, 8381], "[fetchDashboardData]", [4416, 4456], "[currentPage, getAllClassroomData, itemsPerPage, searchQuery]", [3270, 3290], "[decodedClassroomId, fetchAssignments]", [5315, 5374], "[decodedClassroomId, currentPage, itemsPerPage, searchTerm, fetchTrainees]", [5697, 5728], "[fetchAvailableTrainees, modalSearchTerm, showAddModal]", [2474, 2476], "[fetchCourseList]", [2868, 2944], "[classroomId, currentPage, itemsPerPage, debouncedSearchQuery, statusFilter, fetchLiveClasses]", [935, 952], "[decodedCourseId, fetchCourseStats]", [810, 848], "[decodedAssessmentId, decodedCourseId, fetchAssessmentDetails]", [5972, 5990], "[decodedContentId, getDocumentDetailsById]", [3092, 3094], "[fetchCourseModule]", [3269, 3277], "[fetchCourseModule, search]", [4264, 4266], "[fetchModuleContent]", [757, 791], "[decodedSurveyId, decodedCourseId, fetchSurveyDetails]", [10267, 10281], "[fetchQuestions, itemsPerPage]", [2486, 2499], "[page, limit, fetchCertificates]", [3277, 3279], [3318, 3339], "[debouncedSearchTerm, notifications.length]", [3597, 3618], "[debouncedSearchTerm, fetchNotifications]", [17596, 17666], "[pagination.page, pagination.limit, activeFilter, debouncedSearchTerm, fetchTraineesData]", [2624, 2635], "[decodedId, fetchAnalyticsData]", [592, 606], "[assessmentId, fetchResults]", [1218, 1228], "[courseId, fetchModuleList]", [1678, 1690], "[module<PERSON>ist, <PERSON><PERSON><PERSON><PERSON>]", [2615, 2625], "[courseId, getCourseDetails]", [1876, 1878], "[initPlayer]", [1573, 1596], "[course_id, sendtovalidation, session_id]", [2503, 2558], "[decodedClassroomId, decodedAssessmentId, itemsPerPage, fetchAssessmentQuestions]", [2091, 2171], "[decodedClassroomId, decodedAssessmentId, currentPage, itemsPerPage, searchTerm, fetchResults, fetchAnalyticsData]", [831, 873], "[decodedCourseId, search, pagination.page, fetchAssessmentData]", [806, 848], "[decodedCourseId, search, pagination.page, fetchSurveyData]", [920, 976], "[decodedCourseId, search, statusFilter, pagination.page, fetchTraineeProgress]", [3577, 3611], "[classroom_id, encodedClassroomID, urlActiveTab]", [2869, 2875], "[fetchClassrooms, page, search]", [4500, 4531], "[handleSubmit, quizStarted, submissionResult, timeLeft]", [2728, 2766], "[assignment_id, classroom_id, fetchQuestions, user_id]", [1330, 1355], "[page, limit, searchTerm, getTicketData]", [3318, 3339], [3597, 3618], [8665, 8708], "[classroom_id, currentUserId, group_id, group_name, token]", [1557, 1559], "[fetchNotifications]", [1815, 1817], [1440, 1442], [1698, 1700], [1209, 1220], "[fetchClassroom<PERSON><PERSON>, traineeId]", [1666, 1677], "[fetchCourseData, traineeId]", [1330, 1341], "[fetchPayment<PERSON><PERSON>, traineeId]", [1325, 1336], "[fetchTickets<PERSON>ata, traineeId]", [1229, 1240], "[fetchCertificates<PERSON><PERSON>, traineeId]", [1367, 1376], "[fetchComments, videoId]", [393, 395], "[fetchMonthlyPerformance]", [3059, 3061], "[searchTerm]", [3153, 3159], "[page, searchTerm]", [1278, 1293], "[getAssessmentQuestions, moduleData.id]", [816, 818], [1229, 1255], "[page, hasMore, isLoading, searchTerm]", [1761, 1778], "[contentData, contentData?.id]", [6036, 6063], "[videoUrl, contentData?.id, contentData?.title]", [6796, 6798], [1804, 1837], "[surveyId, moduleData?.completed, fetchSurveyQuestions]", [1525, 1602], "[classroom_id, currentPage, itemsPerPage, debouncedSearchQuery, statusFilter, fetchLiveClasses]", [2287, 2354], "[decodedClassroomId, currentPage, searchTerm, selectedResourceType, fetchResources]", [1270, 1289], "[videoId, courseId, fetchReviews]", [673, 683], "[getNotesData, moduleId, videoId]", [1475, 1484], [4256, 4310], "[currentPage, fetchUsers, itemsPerPage, searchQuery, statusFilter]", [824, 826], "[fetchTasks]", [1904, 1959], "[decodedClassroomId, currentPage, selectedResourceType, fetchResources]", [2406, 2445], "[initializationStarted, loading, error, initializeZoomMeeting]", [3808, 3810], "[courseId, sendToValidation, txnId]", [2264, 2282], "[course_id, handleCancelValidation, txnid]", [1850, 1866], "[courseData?.banner_image, courseData?.course_desc, courseData?.course_id, courseData?.course_name, courseData?.course_price, courseData?.course_type, courseData?.currency, courseData?.discountCode, courseData?.discountValue, courseData?.points, location.state]", [2611, 2621]]