const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const { mysqlServerConnection } = require('../../../db/db');
const axios = require('axios');
const {uploadStripeReceiptToS3} = require('../../../tools/aws'); // Assuming this function is defined in another file

const processPayment = async (req, res) => {
    try {
        console.log(req.body, 'req.body');
        const { amount, currency, origin, courseName, points, course_id } = req.body;
        console.log(currency, 'currency');

        // Validate required fields
        if (!amount || !currency || !origin || !courseName || !course_id) {
            return res.status(400).json({
                success: false,
                data: { error_msg: 'Missing required fields' }
            });
        }

        // Check if the course is already purchased
        const [isCourseExistRows] = await mysqlServerConnection.query(
            `SELECT * FROM ${req.user.db_name}.mycourses WHERE course_id = ? AND user_id = ? LIMIT 1`,
            [course_id, req.user.userId]
        );

        if (isCourseExistRows.length > 0) {
            return res.status(409).json({  
                success: false,
                data: { error_msg: 'Course already purchased' }
            });
        }

        // Create Stripe checkout session and include session.id dynamically
        const session = await stripe.checkout.sessions.create({
            payment_method_types: ['card'],
            line_items: [
                {
                    price_data: {
                        currency: currency,
                        product_data: {
                            name: courseName, 
                        },
                        unit_amount: amount, 
                    },
                    quantity: 1,
                },
            ],
            mode: "payment",
            success_url: `${origin}/user/courses/successPayment?course_id=${course_id}&session_id={CHECKOUT_SESSION_ID}`,
            cancel_url: `${origin}/payment-cancel?session_id={CHECKOUT_SESSION_ID}`,
        });

        console.log(session.cancel_url, 'payment');

        const amt = amount / 100; // Ensure correct amount conversion

        // Insert payment record into the database
        await mysqlServerConnection.query(
            `INSERT INTO ${req.user.db_name}.payments (user_id, amount, currency, payment_method, payment_uid, course_name, discount_point) 
            VALUES (?, ?, ?, ?, ?, ?, ?)`,
            [req.user.userId, amt, currency, 'Stripe', session.id, courseName, points]
        );

        // Return session URL to frontend
        return res.status(200).json({
            success: true,
            sessionUrl: session.url, // Correct usage of session.url
        });

    } catch (error) {
        console.error(error, 'error');
        return res.status(500).json({
            success: false,
            error_msg: "Payment processing failed",
            details: error.message,
        });
    }
};

const successPayment = async (req, res) => {
    try {
        const db_name = req.user.db_name;
        const user_id = req.user.userId;
        const course_id = req.query.course_id;
        const session_id = req.query.session_id;

        const [payment] = await mysqlServerConnection.query(`SELECT * FROM ${db_name}.payments WHERE user_id = ? AND payment_uid=?`, [user_id, session_id])

        if (payment.length === 0) {
            await mysqlServerConnection.query(`UPDATE ${db_name}.payments SET status = 'failed' WHERE user_id = ? AND payment_uid=?`, [user_id,session_id])
            return res.status(404).json({
                success: false,
                data: { error_msg: 'Payment failed' }
            });
        }

        const [courseRows] = await mysqlServerConnection.query(
            `SELECT * FROM ${req.user.db_name}.courses WHERE id = ? LIMIT 1`,
            [course_id]
        );

        if (courseRows.length === 0) {
            return res.status(404).json({
                success: false,
                data: { error_msg: 'The referenced course ID does not exist in the courses table.' }
            });
        }

        // Retrieve the session from Stripe to get payment intent
        const session = await stripe.checkout.sessions.retrieve(session_id);
        const paymentIntentId = session.payment_intent;
        
        // Get the receipt URL from the payment intent
        const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
        const chargeId = paymentIntent.latest_charge;
        
        let receiptUrl = null;
        let receiptPdfPath = null;
        
        if (chargeId) {
            // Get the charge to access the receipt URL
            const charge = await stripe.charges.retrieve(chargeId);
            receiptUrl = charge.receipt_url;
            
            // If we have a receipt URL, store it and upload to S3
            if (receiptUrl) {
                try {
                    // Store just the receipt URL without trying to download it as PDF
                    // The frontend will open this URL directly in a new tab
                    await mysqlServerConnection.query(
                        `UPDATE ${db_name}.payments SET receipt_url = ? WHERE payment_uid = ?`,
                        [receiptUrl, session_id]
                    );
                    
                    // Set the receipt_pdf_path to the same URL for now
                    receiptPdfPath = receiptUrl;
                } catch (error) {
                    console.error('Error updating receipt URL:', error);
                    // Continue with the payment process even if receipt handling fails
                }
            }
        }

        // Check if the course is already in mycourses
        const [existingCourse] = await mysqlServerConnection.query(
            `SELECT * FROM ${db_name}.mycourses WHERE course_id = ? AND user_id = ?`,
            [course_id, user_id]
        );

        if (existingCourse.length === 0) {
            // Add the course to mycourses
            await mysqlServerConnection.query(
                `INSERT INTO ${db_name}.mycourses (course_id, user_id) VALUES (?, ?)`,
                [course_id, user_id]
            );

            // Update course subscribers count
            await mysqlServerConnection.query(
                `UPDATE ${db_name}.courses SET subscribers = subscribers + 1 WHERE id = ?`,
                [course_id]
            );
        }

        // Update payment status to success
        await mysqlServerConnection.query(
            `UPDATE ${db_name}.payments SET status = 'completed' WHERE user_id = ? AND payment_uid = ?`,
            [user_id, session_id]
        );

        // Get course details for response
        const course = courseRows[0];

        let payment_id = null;
        if (payment.length > 0) {
            payment_id = payment[0].payment_uid.split("_")[2];
        }
        
        return res.status(200).json({
            success: true,
            data: {
                payment_id: payment_id,
                currency: course.currency,
                price: course.course_price,
                receipt_url: receiptUrl,
                receipt_pdf_path: receiptPdfPath
            }
        });

    } catch (error) {
        console.log(error);
        return res.status(500).json({
            success: false,
            error_msg: "Payment processing failed",
            details: error.message,
        });
    }
};

const cancelPayment = async (req, res) => {
    try {
        const db_name = req.user.db_name;
        const user_id = req.user.userId;
        const session_id=req.query.session_id

        const [payment] = await mysqlServerConnection.query(`SELECT * FROM ${db_name}.payments WHERE user_id = ? AND payment_uid=?`, [user_id,session_id])

        if (payment.length > 0) {
            await mysqlServerConnection.query(`UPDATE ${db_name}.payments SET status = 'failed' WHERE user_id = ? AND payment_uid=?`, [user_id,session_id])
        }
        else{
            return res.status(404).json({
                success: false,
                data: { error_msg: 'Payment failed' }
            })
        }
        let payment_id = null
        if (payment) {
            payment_id = payment[0].payment_uid.split("_")[2];

        }
        return res.status(200).json({
            success: true,
            data: {
                payment_id: payment_id,
                price: payment[0].amount

            }
        })

    } catch (error) {
        console.log(error)
        return res.status(500).json({
            success: false,
            error_msg: "Payment processing failed",
            details: error.message,
        });
    }
};


const getPaymentHistory = async (req, res) => {
    try {
    const { page = 1, limit = 10 } = req.query; // Extract pagination & search params
    const {userId} = req.user;      

  

    const search = req.query.search===""?`%`:`%${req.query.search}%`;
    console.log('search',search)
        // Validate pagination params
        const pageNum = parseInt(page);
        const limitNum = parseInt(limit);
        if (isNaN(pageNum) || isNaN(limitNum) || pageNum < 1 || limitNum < 1) {
            return res.status(400).json({ 
                success: false, 
                error_msg: "Invalid pagination parameters" 
            });
        }

        // Calculate offset for pagination
        const offset = (pageNum - 1) * limitNum;

        // Construct the WHERE clause based on search input
        let searchQuery = "";
        let searchParams = [req.user.userId];

        if (search) {
            searchQuery = `AND (course_name LIKE ? OR payment_method LIKE ? OR payment_uid LIKE ?)`;
            const searchPattern = `%${search}%`;
            searchParams.push(searchPattern, searchPattern, searchPattern);
        }

        // Fetch payment history with pagination & search
        const [payments] = await mysqlServerConnection.query(
            `SELECT id, amount, currency, payment_method, payment_uid, course_name, discount_point, status, created_at, receipt_url, receipt_pdf_path 
             FROM ${req.user.db_name}.payments 
             WHERE user_id = ? ${searchQuery} 
             ORDER BY created_at DESC 
             LIMIT ? OFFSET ?`,
            [...searchParams, limitNum, offset]
        );

        // Fetch total payment count for pagination
        const [[{ totalCount }]] = await mysqlServerConnection.query(
            `SELECT COUNT(*) AS totalCount FROM ${req.user.db_name}.payments 
             WHERE user_id = ? ${searchQuery}`,
            searchParams
        );

        // Calculate total pages
        const totalPages = Math.ceil(totalCount / limitNum);

        // Response with pagination data
        return res.status(200).json({
            success: true,
            pagination: {
                page: pageNum,
                limit: limitNum,
                totalPages,
                totalCount
            },
            payments
        });

    } catch (error) {
        console.error(error, "Error fetching payment history");
        return res.status(500).json({
            success: false,
            error_msg: "Failed to retrieve payment history",
            details: error.message,
        });
    }
};

const getAllUserTransactions = async (req, res) => {
  try {
    const user_id = req.user.userId;
    const db_name = req.user.db_name;

    const [transactions] = await mysqlServerConnection.query(
      `SELECT 
        id,
        user_id,
        amount,
        currency,
        status,
        payment_method,
        created_at,
        updated_at,
        payment_uid,
        course_name,
        discount_point,
        receipt_url,
        receipt_pdf_path
      FROM ${db_name}.payments
      WHERE user_id = ?
      ORDER BY created_at DESC`,
      [user_id]
    );

    return res.status(200).json({
      success: true,
      message: transactions.length > 0 ? "Transactions fetched successfully" : "No transactions found",
      data: transactions.length > 0 ? transactions : []
    });

  } catch (error) {
    console.error('Error fetching user transactions:', error);
    return res.status(500).json({
      success: false,
      error: "Failed to retrieve user payment transactions",
      details: error.message,
    });
  }
};



module.exports = {
  processPayment,
  successPayment,
  cancelPayment,
  getPaymentHistory,
  getAllUserTransactions
};
