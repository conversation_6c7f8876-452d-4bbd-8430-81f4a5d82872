{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NEW_LMS_FIXING\\\\FRONT\\\\src\\\\components\\\\user\\\\SideBar.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\nimport { Icon } from '@iconify/react';\nimport { getLogoByDomainAndAlt } from '../../utils/logoUtils';\nimport { logoutApi } from '../../services/authService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction SideBar() {\n  _s();\n  const location = useLocation();\n  const navigate = useNavigate();\n\n  // State for toggle collapse in menu items\n  const [collapsed, setCollapsed] = useState(false);\n  // State for mobile sidebar visibility\n  const [showMobileSidebar, setShowMobileSidebar] = useState(false);\n  // Track if we're on mobile or not\n  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);\n\n  // Check screen size on mount and resize\n  useEffect(() => {\n    const handleResize = () => {\n      setIsMobile(window.innerWidth < 768);\n      // Auto-hide sidebar when resizing to desktop\n      if (window.innerWidth >= 768) {\n        setShowMobileSidebar(false);\n      }\n    };\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n\n  // Toggle mobile sidebar\n  const toggleMobileSidebar = () => {\n    setShowMobileSidebar(!showMobileSidebar);\n  };\n  const isActive = path => {\n    if (path === '/user/courses' && (location.pathname.startsWith('/user/courses/courseDetails') || location.pathname.startsWith('/user/courses/OrderDetailsWithStripe') || location.pathname.startsWith('/user/courses/successPayment') || location.pathname.startsWith('/user/courses/Result') || location.pathname.startsWith('/user/courses/WatchCourse') || location.pathname.startsWith('/user/resultvideo'))) {\n      return 'active';\n    }\n    if (path === '/user/AllClassroom' && (location.pathname.startsWith('/user/AllClassroom/classroomDetails') || location.pathname.startsWith('/user/assignmentsQuestions'))) {\n      return 'active';\n    }\n    return location.pathname === path ? 'active' : '';\n  };\n  const handleLinkClick = () => {\n    // Close sidebar on mobile when clicking a link\n    if (isMobile) {\n      setShowMobileSidebar(false);\n    }\n  };\n  const handleLogout = async () => {\n    try {\n      // Optionally call logout API\n      await logoutApi();\n\n      // Clear all local storage items\n      localStorage.clear();\n\n      // Redirect to login page\n      navigate('/');\n    } catch (error) {\n      console.error(\"Logout error:\", error);\n      localStorage.clear();\n      navigate('/');\n    }\n  };\n\n  // Mobile toggle button to be rendered in the layout\n  const MobileToggleButton = () => /*#__PURE__*/_jsxDEV(\"button\", {\n    className: \"btn p-0\",\n    onClick: toggleMobileSidebar,\n    \"aria-label\": \"Toggle sidebar\",\n    style: {\n      border: 'none',\n      background: 'none'\n    },\n    children: /*#__PURE__*/_jsxDEV(Icon, {\n      icon: \"mdi:menu\",\n      width: \"32\",\n      height: \"32\",\n      color: \"#212529\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 84,\n    columnNumber: 5\n  }, this);\n\n  // Standard sidebar content\n  const SidebarContent = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"sidebar-nav py-2\",\n    children: [/*#__PURE__*/_jsxDEV(Link, {\n      to: \"/user/dashboard\",\n      className: `nav-item d-flex align-items-center py-2 px-3 ${isActive('/user/dashboard') ? 'active' : ''}`,\n      onClick: handleLinkClick,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-icon-wrapper me-3 d-flex align-items-center justify-content-center\",\n        children: /*#__PURE__*/_jsxDEV(Icon, {\n          icon: \"fluent:grid-24-regular\",\n          width: \"22\",\n          height: \"22\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"nav-text\",\n        children: \"Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Link, {\n      to: \"/user/profile\",\n      className: `nav-item d-flex align-items-center py-2 px-3 ${isActive('/user/profile') ? 'active' : ''}`,\n      onClick: handleLinkClick,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-icon-wrapper me-3 d-flex align-items-center justify-content-center\",\n        children: /*#__PURE__*/_jsxDEV(Icon, {\n          icon: \"fluent:person-24-regular\",\n          width: \"22\",\n          height: \"22\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"nav-text\",\n        children: \"Profile\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Link, {\n      to: \"/user/courses\",\n      className: `nav-item d-flex align-items-center py-2 px-3 ${isActive('/user/courses') ? 'active' : ''}`,\n      onClick: handleLinkClick,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-icon-wrapper me-3 d-flex align-items-center justify-content-center\",\n        children: /*#__PURE__*/_jsxDEV(Icon, {\n          icon: \"fluent:book-24-regular\",\n          width: \"22\",\n          height: \"22\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"nav-text\",\n        children: \"Courses\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Link, {\n      to: \"/user/AllClassroom\",\n      className: `nav-item d-flex align-items-center py-2 px-3 ${isActive('/user/AllClassroom') ? 'active' : ''}`,\n      onClick: handleLinkClick,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-icon-wrapper me-3 d-flex align-items-center justify-content-center\",\n        children: /*#__PURE__*/_jsxDEV(Icon, {\n          icon: \"fluent:desktop-24-regular\",\n          width: \"22\",\n          height: \"22\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"nav-text\",\n        children: \"Classrooms\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Link, {\n      to: \"/user/certificates\",\n      className: `nav-item d-flex align-items-center py-2 px-3 ${isActive('/user/certificates') ? 'active' : ''}`,\n      onClick: handleLinkClick,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-icon-wrapper me-3 d-flex align-items-center justify-content-center\",\n        children: /*#__PURE__*/_jsxDEV(Icon, {\n          icon: \"fluent:document-ribbon-24-regular\",\n          width: \"22\",\n          height: \"22\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"nav-text\",\n        children: \"Certificates\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Link, {\n      to: \"/user/notifications\",\n      className: `nav-item d-flex align-items-center py-2 px-3 ${isActive('/user/notifications') ? 'active' : ''}`,\n      onClick: handleLinkClick,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-icon-wrapper me-3 d-flex align-items-center justify-content-center\",\n        children: /*#__PURE__*/_jsxDEV(Icon, {\n          icon: \"fluent:alert-24-regular\",\n          width: \"22\",\n          height: \"22\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"nav-text\",\n        children: \"Notifications\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 161,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"nav-item d-flex align-items-center py-2 px-3 text-danger\",\n      onClick: handleLogout,\n      role: \"button\",\n      style: {\n        cursor: 'pointer'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-icon-wrapper me-3 d-flex align-items-center justify-content-center\",\n        children: /*#__PURE__*/_jsxDEV(Icon, {\n          icon: \"fluent:sign-out-24-regular\",\n          width: \"22\",\n          height: \"22\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"nav-text\",\n        children: \"Logout\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mx-auto mt-4 p-3 rounded-3 text-center\",\n      style: {\n        backgroundColor: '#f0f2ff',\n        width: '320px',\n        maxWidth: '100%'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-2\",\n        children: /*#__PURE__*/_jsxDEV(Icon, {\n          icon: \"fluent:question-circle-24-regular\",\n          width: \"28\",\n          height: \"28\",\n          color: \"#4361ee\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h6\", {\n        className: \"mb-1\",\n        style: {\n          fontSize: '14px'\n        },\n        children: \"Help center\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"small mb-2\",\n        style: {\n          color: '#6b7280',\n          fontSize: '12px'\n        },\n        children: [\"Any problem?\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 23\n        }, this), \"send us a message\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/user/help\",\n        className: \"btn btn-primary btn-sm w-100\",\n        style: {\n          backgroundColor: '#4361ee',\n          border: 'none',\n          borderRadius: '6px',\n          padding: '6px 12px',\n          fontSize: '13px'\n        },\n        children: \"Go to help center\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 96,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [!isMobile && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"logo-container border-end\",\n      style: {\n        backgroundColor: '#f6f7f9',\n        height: '92px',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        padding: '0.75rem'\n      },\n      children: /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/user/dashboard\",\n        className: \"text-decoration-none d-flex justify-content-center\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: getLogoByDomainAndAlt().logo,\n          alt: getLogoByDomainAndAlt().alt,\n          className: \"img-fluid\",\n          style: {\n            maxHeight: '100px',\n            maxWidth: '100%'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 9\n    }, this), isMobile && /*#__PURE__*/_jsxDEV(MobileToggleButton, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 20\n    }, this), showMobileSidebar && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-md-none\",\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundColor: 'rgba(0,0,0,0.5)',\n        zIndex: 1030\n      },\n      onClick: toggleMobileSidebar\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 9\n    }, this), isMobile && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `d-md-none mobile-sidebar border-end position-fixed ${showMobileSidebar ? 'show' : 'hide'}`,\n      style: {\n        width: '280px',\n        height: '100vh',\n        top: 0,\n        left: 0,\n        zIndex: 1040,\n        transform: showMobileSidebar ? 'translateX(0)' : 'translateX(-100%)',\n        transition: 'transform 0.3s ease-in-out'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"logo-container border-bottom\",\n        style: {\n          height: '78px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          padding: '0.75rem 1rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/user/dashboard\",\n          className: \"text-decoration-none\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: getLogoByDomainAndAlt().logo,\n            alt: getLogoByDomainAndAlt().alt,\n            className: \"img-fluid\",\n            style: {\n              maxHeight: '50px',\n              maxWidth: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-sm btn-outline-secondary rounded-circle\",\n          onClick: toggleMobileSidebar,\n          \"aria-label\": \"Close menu\",\n          children: /*#__PURE__*/_jsxDEV(Icon, {\n            icon: \"fluent:dismiss-24-filled\",\n            width: \"20\",\n            height: \"20\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overflow-auto\",\n        style: {\n          height: 'calc(100vh - 78px)'\n        },\n        children: /*#__PURE__*/_jsxDEV(SidebarContent, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 9\n    }, this), !isMobile && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sidebar overflow-auto\",\n      style: {\n        minHeight: 'calc(100vh - 120px)'\n      },\n      children: /*#__PURE__*/_jsxDEV(SidebarContent, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n}\n_s(SideBar, \"f0foN3JJ8fX5GAwDbECMk6iVHPM=\", false, function () {\n  return [useLocation, useNavigate];\n});\n_c = SideBar;\nexport default SideBar;\nvar _c;\n$RefreshReg$(_c, \"SideBar\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useLocation", "useNavigate", "Icon", "getLogoByDomainAndAlt", "logoutApi", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SideBar", "_s", "location", "navigate", "collapsed", "setCollapsed", "showMobileSidebar", "setShowMobileSidebar", "isMobile", "setIsMobile", "window", "innerWidth", "handleResize", "addEventListener", "removeEventListener", "toggleMobileSidebar", "isActive", "path", "pathname", "startsWith", "handleLinkClick", "handleLogout", "localStorage", "clear", "error", "console", "MobileToggleButton", "className", "onClick", "style", "border", "background", "children", "icon", "width", "height", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "<PERSON>bar<PERSON><PERSON>nt", "to", "role", "cursor", "backgroundColor", "max<PERSON><PERSON><PERSON>", "fontSize", "borderRadius", "padding", "display", "alignItems", "justifyContent", "src", "logo", "alt", "maxHeight", "position", "top", "left", "right", "bottom", "zIndex", "transform", "transition", "minHeight", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/NEW_LMS_FIXING/FRONT/src/components/user/SideBar.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\nimport { Icon } from '@iconify/react';\nimport { getLogoByDomainAndAlt } from '../../utils/logoUtils';\nimport { logoutApi } from '../../services/authService';\n\nfunction SideBar() {\n  const location = useLocation();\n  const navigate = useNavigate();\n\n  // State for toggle collapse in menu items\n  const [collapsed, setCollapsed] = useState(false);\n  // State for mobile sidebar visibility\n  const [showMobileSidebar, setShowMobileSidebar] = useState(false);\n  // Track if we're on mobile or not\n  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);\n\n  // Check screen size on mount and resize\n  useEffect(() => {\n    const handleResize = () => {\n      setIsMobile(window.innerWidth < 768);\n      // Auto-hide sidebar when resizing to desktop\n      if (window.innerWidth >= 768) {\n        setShowMobileSidebar(false);\n      }\n    };\n\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n\n  // Toggle mobile sidebar\n  const toggleMobileSidebar = () => {\n    setShowMobileSidebar(!showMobileSidebar);\n  };\n\n  const isActive = (path) => {\n    if (path === '/user/courses' && (\n      location.pathname.startsWith('/user/courses/courseDetails') ||\n      location.pathname.startsWith('/user/courses/OrderDetailsWithStripe') ||\n      location.pathname.startsWith('/user/courses/successPayment') ||\n      location.pathname.startsWith('/user/courses/Result') ||\n      location.pathname.startsWith('/user/courses/WatchCourse') ||\n      location.pathname.startsWith('/user/resultvideo')\n    )) {\n      return 'active';\n    }\n    if (path === '/user/AllClassroom' && (\n      location.pathname.startsWith('/user/AllClassroom/classroomDetails') ||\n      location.pathname.startsWith('/user/assignmentsQuestions')\n    )) {\n      return 'active';\n    }\n    return location.pathname === path ? 'active' : '';\n  };\n\n  const handleLinkClick = () => {\n    // Close sidebar on mobile when clicking a link\n    if (isMobile) {\n      setShowMobileSidebar(false);\n    }\n  };\n\n  const handleLogout = async () => {\n    try {\n      // Optionally call logout API\n      await logoutApi();\n\n      // Clear all local storage items\n      localStorage.clear();\n\n      // Redirect to login page\n      navigate('/');\n    } catch (error) {\n      console.error(\"Logout error:\", error);\n      localStorage.clear();\n      navigate('/');\n    }\n  };\n\n\n  // Mobile toggle button to be rendered in the layout\n  const MobileToggleButton = () => (\n    <button\n      className=\"btn p-0\"\n      onClick={toggleMobileSidebar}\n      aria-label=\"Toggle sidebar\"\n      style={{ border: 'none', background: 'none' }}\n    >\n      <Icon icon=\"mdi:menu\" width=\"32\" height=\"32\" color=\"#212529\" />\n    </button>\n  );\n\n  // Standard sidebar content\n  const SidebarContent = () => (\n    <div className=\"sidebar-nav py-2\">\n      <Link\n        to=\"/user/dashboard\"\n        className={`nav-item d-flex align-items-center py-2 px-3 ${isActive('/user/dashboard') ? 'active' : ''}`}\n        onClick={handleLinkClick}\n      >\n        <div className=\"nav-icon-wrapper me-3 d-flex align-items-center justify-content-center\">\n          <Icon icon=\"fluent:grid-24-regular\" width=\"22\" height=\"22\" />\n        </div>\n        <span className=\"nav-text\">Dashboard</span>\n      </Link>\n\n      <Link\n        to=\"/user/profile\"\n        className={`nav-item d-flex align-items-center py-2 px-3 ${isActive('/user/profile') ? 'active' : ''}`}\n        onClick={handleLinkClick}\n      >\n        <div className=\"nav-icon-wrapper me-3 d-flex align-items-center justify-content-center\">\n          <Icon icon=\"fluent:person-24-regular\" width=\"22\" height=\"22\" />\n        </div>\n        <span className=\"nav-text\">Profile</span>\n      </Link>\n\n      <Link\n        to=\"/user/courses\"\n        className={`nav-item d-flex align-items-center py-2 px-3 ${isActive('/user/courses') ? 'active' : ''}`}\n        onClick={handleLinkClick}\n      >\n        <div className=\"nav-icon-wrapper me-3 d-flex align-items-center justify-content-center\">\n          <Icon icon=\"fluent:book-24-regular\" width=\"22\" height=\"22\" />\n        </div>\n        <span className=\"nav-text\">Courses</span>\n      </Link>\n\n      <Link\n        to=\"/user/AllClassroom\"\n        className={`nav-item d-flex align-items-center py-2 px-3 ${isActive('/user/AllClassroom') ? 'active' : ''}`}\n        onClick={handleLinkClick}\n      >\n        <div className=\"nav-icon-wrapper me-3 d-flex align-items-center justify-content-center\">\n          <Icon icon=\"fluent:desktop-24-regular\" width=\"22\" height=\"22\" />\n        </div>\n        <span className=\"nav-text\">Classrooms</span>\n      </Link>\n\n      <Link\n        to=\"/user/certificates\"\n        className={`nav-item d-flex align-items-center py-2 px-3 ${isActive('/user/certificates') ? 'active' : ''}`}\n        onClick={handleLinkClick}\n      >\n        <div className=\"nav-icon-wrapper me-3 d-flex align-items-center justify-content-center\">\n          <Icon icon=\"fluent:document-ribbon-24-regular\" width=\"22\" height=\"22\" />\n        </div>\n        <span className=\"nav-text\">Certificates</span>\n      </Link>\n\n      {/* Notifications */}\n      <Link\n        to=\"/user/notifications\"\n        className={`nav-item d-flex align-items-center py-2 px-3 ${isActive('/user/notifications') ? 'active' : ''}`}\n        onClick={handleLinkClick}\n      >\n        <div className=\"nav-icon-wrapper me-3 d-flex align-items-center justify-content-center\">\n          <Icon icon=\"fluent:alert-24-regular\" width=\"22\" height=\"22\" />\n        </div>\n        <span className=\"nav-text\">Notifications</span>\n      </Link>\n\n      {/* Logout */}\n      <div\n        className=\"nav-item d-flex align-items-center py-2 px-3 text-danger\"\n        onClick={handleLogout}\n        role=\"button\"\n        style={{ cursor: 'pointer' }}\n      >\n        <div className=\"nav-icon-wrapper me-3 d-flex align-items-center justify-content-center\">\n          <Icon icon=\"fluent:sign-out-24-regular\" width=\"22\" height=\"22\" />\n        </div>\n        <span className=\"nav-text\">Logout</span>\n      </div>\n\n      {/* Help Center Card */}\n      <div \n        className=\"mx-auto mt-4 p-3 rounded-3 text-center\" \n        style={{ \n          backgroundColor: '#f0f2ff',\n          width: '320px',\n          maxWidth: '100%'\n        }}\n      >\n        <div className=\"mb-2\">\n          <Icon icon=\"fluent:question-circle-24-regular\" width=\"28\" height=\"28\" color=\"#4361ee\" />\n        </div>\n        <h6 className=\"mb-1\" style={{ fontSize: '14px' }}>Help center</h6>\n        <p className=\"small mb-2\" style={{ color: '#6b7280', fontSize: '12px' }}>\n          Any problem?<br />\n          send us a message\n        </p>\n        <Link \n          to=\"/user/help\"\n          className=\"btn btn-primary btn-sm w-100\"\n          style={{ \n            backgroundColor: '#4361ee',\n            border: 'none',\n            borderRadius: '6px',\n            padding: '6px 12px',\n            fontSize: '13px'\n          }}\n        >\n          Go to help center\n        </Link>\n      </div>\n    </div>\n  );\n\n  return (\n    <>\n      {/* Logo for desktop view */}\n      {!isMobile && (\n        <div className=\"logo-container border-end\" style={{ backgroundColor: '#f6f7f9', height: '92px', display: 'flex', alignItems: 'center', justifyContent: 'center', padding: '0.75rem' }}>\n          <Link to=\"/user/dashboard\" className=\"text-decoration-none d-flex justify-content-center\">\n            <img\n              src={getLogoByDomainAndAlt().logo} \n              alt={getLogoByDomainAndAlt().alt}\n              className=\"img-fluid\"\n              style={{ maxHeight: '100px', maxWidth: '100%' }}\n            />\n          </Link>\n        </div>\n      )}\n\n      {/* Mobile Toggle Button - To be rendered in the layout */}\n      {isMobile && <MobileToggleButton />}\n\n      {/* Mobile Sidebar Overlay */}\n      {showMobileSidebar && (\n        <div\n          className=\"d-md-none\"\n          style={{\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            backgroundColor: 'rgba(0,0,0,0.5)',\n            zIndex: 1030\n          }}\n          onClick={toggleMobileSidebar}\n        />\n      )}\n\n      {/* Mobile Sidebar */}\n      {isMobile && (\n        <div\n          className={`d-md-none mobile-sidebar border-end position-fixed ${showMobileSidebar ? 'show' : 'hide'}`}\n          style={{\n            width: '280px',\n            height: '100vh',\n            top: 0,\n            left: 0,\n            zIndex: 1040,\n            transform: showMobileSidebar ? 'translateX(0)' : 'translateX(-100%)',\n            transition: 'transform 0.3s ease-in-out'\n          }}\n        >\n          {/* Mobile Logo */}\n          <div className=\"logo-container border-bottom\" style={{ height: '78px', display: 'flex', alignItems: 'center', justifyContent: 'space-between', padding: '0.75rem 1rem' }}>\n            <Link to=\"/user/dashboard\" className=\"text-decoration-none\">\n              <img\n                src={getLogoByDomainAndAlt().logo}\n                alt={getLogoByDomainAndAlt().alt}\n                className=\"img-fluid\"\n                style={{ maxHeight: '50px', maxWidth: '100%' }}\n              />\n            </Link>\n            <button\n              className=\"btn btn-sm btn-outline-secondary rounded-circle\"\n              onClick={toggleMobileSidebar}\n              aria-label=\"Close menu\"\n            >\n              <Icon icon=\"fluent:dismiss-24-filled\" width=\"20\" height=\"20\" />\n            </button>\n          </div>\n          <div className=\"overflow-auto\" style={{ height: 'calc(100vh - 78px)' }}>\n            <SidebarContent />\n          </div>\n        </div>\n      )}\n\n      {/* Desktop Sidebar Content */}\n      {!isMobile && (\n        <div className=\"sidebar overflow-auto\" style={{ minHeight: 'calc(100vh - 120px)' }}>\n          <SidebarContent />\n        </div>\n      )}\n    </>\n  );\n}\n\nexport default SideBar;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,SAASC,IAAI,QAAQ,gBAAgB;AACrC,SAASC,qBAAqB,QAAQ,uBAAuB;AAC7D,SAASC,SAAS,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvD,SAASC,OAAOA,CAAA,EAAG;EAAAC,EAAA;EACjB,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAMY,QAAQ,GAAGX,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EACjD;EACA,MAAM,CAACkB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACjE;EACA,MAAM,CAACoB,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAACsB,MAAM,CAACC,UAAU,GAAG,GAAG,CAAC;;EAEjE;EACAtB,SAAS,CAAC,MAAM;IACd,MAAMuB,YAAY,GAAGA,CAAA,KAAM;MACzBH,WAAW,CAACC,MAAM,CAACC,UAAU,GAAG,GAAG,CAAC;MACpC;MACA,IAAID,MAAM,CAACC,UAAU,IAAI,GAAG,EAAE;QAC5BJ,oBAAoB,CAAC,KAAK,CAAC;MAC7B;IACF,CAAC;IAEDG,MAAM,CAACG,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;IAC/C,OAAO,MAAMF,MAAM,CAACI,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,mBAAmB,GAAGA,CAAA,KAAM;IAChCR,oBAAoB,CAAC,CAACD,iBAAiB,CAAC;EAC1C,CAAC;EAED,MAAMU,QAAQ,GAAIC,IAAI,IAAK;IACzB,IAAIA,IAAI,KAAK,eAAe,KAC1Bf,QAAQ,CAACgB,QAAQ,CAACC,UAAU,CAAC,6BAA6B,CAAC,IAC3DjB,QAAQ,CAACgB,QAAQ,CAACC,UAAU,CAAC,sCAAsC,CAAC,IACpEjB,QAAQ,CAACgB,QAAQ,CAACC,UAAU,CAAC,8BAA8B,CAAC,IAC5DjB,QAAQ,CAACgB,QAAQ,CAACC,UAAU,CAAC,sBAAsB,CAAC,IACpDjB,QAAQ,CAACgB,QAAQ,CAACC,UAAU,CAAC,2BAA2B,CAAC,IACzDjB,QAAQ,CAACgB,QAAQ,CAACC,UAAU,CAAC,mBAAmB,CAAC,CAClD,EAAE;MACD,OAAO,QAAQ;IACjB;IACA,IAAIF,IAAI,KAAK,oBAAoB,KAC/Bf,QAAQ,CAACgB,QAAQ,CAACC,UAAU,CAAC,qCAAqC,CAAC,IACnEjB,QAAQ,CAACgB,QAAQ,CAACC,UAAU,CAAC,4BAA4B,CAAC,CAC3D,EAAE;MACD,OAAO,QAAQ;IACjB;IACA,OAAOjB,QAAQ,CAACgB,QAAQ,KAAKD,IAAI,GAAG,QAAQ,GAAG,EAAE;EACnD,CAAC;EAED,MAAMG,eAAe,GAAGA,CAAA,KAAM;IAC5B;IACA,IAAIZ,QAAQ,EAAE;MACZD,oBAAoB,CAAC,KAAK,CAAC;IAC7B;EACF,CAAC;EAED,MAAMc,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF;MACA,MAAM1B,SAAS,CAAC,CAAC;;MAEjB;MACA2B,YAAY,CAACC,KAAK,CAAC,CAAC;;MAEpB;MACApB,QAAQ,CAAC,GAAG,CAAC;IACf,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCF,YAAY,CAACC,KAAK,CAAC,CAAC;MACpBpB,QAAQ,CAAC,GAAG,CAAC;IACf;EACF,CAAC;;EAGD;EACA,MAAMuB,kBAAkB,GAAGA,CAAA,kBACzB7B,OAAA;IACE8B,SAAS,EAAC,SAAS;IACnBC,OAAO,EAAEb,mBAAoB;IAC7B,cAAW,gBAAgB;IAC3Bc,KAAK,EAAE;MAAEC,MAAM,EAAE,MAAM;MAAEC,UAAU,EAAE;IAAO,CAAE;IAAAC,QAAA,eAE9CnC,OAAA,CAACJ,IAAI;MAACwC,IAAI,EAAC,UAAU;MAACC,KAAK,EAAC,IAAI;MAACC,MAAM,EAAC,IAAI;MAACC,KAAK,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACzD,CACT;;EAED;EACA,MAAMC,cAAc,GAAGA,CAAA,kBACrB5C,OAAA;IAAK8B,SAAS,EAAC,kBAAkB;IAAAK,QAAA,gBAC/BnC,OAAA,CAACP,IAAI;MACHoD,EAAE,EAAC,iBAAiB;MACpBf,SAAS,EAAE,gDAAgDX,QAAQ,CAAC,iBAAiB,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;MACzGY,OAAO,EAAER,eAAgB;MAAAY,QAAA,gBAEzBnC,OAAA;QAAK8B,SAAS,EAAC,wEAAwE;QAAAK,QAAA,eACrFnC,OAAA,CAACJ,IAAI;UAACwC,IAAI,EAAC,wBAAwB;UAACC,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC;QAAI;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,eACN3C,OAAA;QAAM8B,SAAS,EAAC,UAAU;QAAAK,QAAA,EAAC;MAAS;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC,eAEP3C,OAAA,CAACP,IAAI;MACHoD,EAAE,EAAC,eAAe;MAClBf,SAAS,EAAE,gDAAgDX,QAAQ,CAAC,eAAe,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;MACvGY,OAAO,EAAER,eAAgB;MAAAY,QAAA,gBAEzBnC,OAAA;QAAK8B,SAAS,EAAC,wEAAwE;QAAAK,QAAA,eACrFnC,OAAA,CAACJ,IAAI;UAACwC,IAAI,EAAC,0BAA0B;UAACC,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC;QAAI;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,eACN3C,OAAA;QAAM8B,SAAS,EAAC,UAAU;QAAAK,QAAA,EAAC;MAAO;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC,eAEP3C,OAAA,CAACP,IAAI;MACHoD,EAAE,EAAC,eAAe;MAClBf,SAAS,EAAE,gDAAgDX,QAAQ,CAAC,eAAe,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;MACvGY,OAAO,EAAER,eAAgB;MAAAY,QAAA,gBAEzBnC,OAAA;QAAK8B,SAAS,EAAC,wEAAwE;QAAAK,QAAA,eACrFnC,OAAA,CAACJ,IAAI;UAACwC,IAAI,EAAC,wBAAwB;UAACC,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC;QAAI;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,eACN3C,OAAA;QAAM8B,SAAS,EAAC,UAAU;QAAAK,QAAA,EAAC;MAAO;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC,eAEP3C,OAAA,CAACP,IAAI;MACHoD,EAAE,EAAC,oBAAoB;MACvBf,SAAS,EAAE,gDAAgDX,QAAQ,CAAC,oBAAoB,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;MAC5GY,OAAO,EAAER,eAAgB;MAAAY,QAAA,gBAEzBnC,OAAA;QAAK8B,SAAS,EAAC,wEAAwE;QAAAK,QAAA,eACrFnC,OAAA,CAACJ,IAAI;UAACwC,IAAI,EAAC,2BAA2B;UAACC,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC;QAAI;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,eACN3C,OAAA;QAAM8B,SAAS,EAAC,UAAU;QAAAK,QAAA,EAAC;MAAU;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxC,CAAC,eAEP3C,OAAA,CAACP,IAAI;MACHoD,EAAE,EAAC,oBAAoB;MACvBf,SAAS,EAAE,gDAAgDX,QAAQ,CAAC,oBAAoB,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;MAC5GY,OAAO,EAAER,eAAgB;MAAAY,QAAA,gBAEzBnC,OAAA;QAAK8B,SAAS,EAAC,wEAAwE;QAAAK,QAAA,eACrFnC,OAAA,CAACJ,IAAI;UAACwC,IAAI,EAAC,mCAAmC;UAACC,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC;QAAI;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CAAC,eACN3C,OAAA;QAAM8B,SAAS,EAAC,UAAU;QAAAK,QAAA,EAAC;MAAY;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC,eAGP3C,OAAA,CAACP,IAAI;MACHoD,EAAE,EAAC,qBAAqB;MACxBf,SAAS,EAAE,gDAAgDX,QAAQ,CAAC,qBAAqB,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;MAC7GY,OAAO,EAAER,eAAgB;MAAAY,QAAA,gBAEzBnC,OAAA;QAAK8B,SAAS,EAAC,wEAAwE;QAAAK,QAAA,eACrFnC,OAAA,CAACJ,IAAI;UAACwC,IAAI,EAAC,yBAAyB;UAACC,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC;QAAI;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,eACN3C,OAAA;QAAM8B,SAAS,EAAC,UAAU;QAAAK,QAAA,EAAC;MAAa;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3C,CAAC,eAGP3C,OAAA;MACE8B,SAAS,EAAC,0DAA0D;MACpEC,OAAO,EAAEP,YAAa;MACtBsB,IAAI,EAAC,QAAQ;MACbd,KAAK,EAAE;QAAEe,MAAM,EAAE;MAAU,CAAE;MAAAZ,QAAA,gBAE7BnC,OAAA;QAAK8B,SAAS,EAAC,wEAAwE;QAAAK,QAAA,eACrFnC,OAAA,CAACJ,IAAI;UAACwC,IAAI,EAAC,4BAA4B;UAACC,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC;QAAI;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC,eACN3C,OAAA;QAAM8B,SAAS,EAAC,UAAU;QAAAK,QAAA,EAAC;MAAM;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC,eAGN3C,OAAA;MACE8B,SAAS,EAAC,wCAAwC;MAClDE,KAAK,EAAE;QACLgB,eAAe,EAAE,SAAS;QAC1BX,KAAK,EAAE,OAAO;QACdY,QAAQ,EAAE;MACZ,CAAE;MAAAd,QAAA,gBAEFnC,OAAA;QAAK8B,SAAS,EAAC,MAAM;QAAAK,QAAA,eACnBnC,OAAA,CAACJ,IAAI;UAACwC,IAAI,EAAC,mCAAmC;UAACC,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC,IAAI;UAACC,KAAK,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrF,CAAC,eACN3C,OAAA;QAAI8B,SAAS,EAAC,MAAM;QAACE,KAAK,EAAE;UAAEkB,QAAQ,EAAE;QAAO,CAAE;QAAAf,QAAA,EAAC;MAAW;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClE3C,OAAA;QAAG8B,SAAS,EAAC,YAAY;QAACE,KAAK,EAAE;UAAEO,KAAK,EAAE,SAAS;UAAEW,QAAQ,EAAE;QAAO,CAAE;QAAAf,QAAA,GAAC,cAC3D,eAAAnC,OAAA;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,qBAEpB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJ3C,OAAA,CAACP,IAAI;QACHoD,EAAE,EAAC,YAAY;QACff,SAAS,EAAC,8BAA8B;QACxCE,KAAK,EAAE;UACLgB,eAAe,EAAE,SAAS;UAC1Bf,MAAM,EAAE,MAAM;UACdkB,YAAY,EAAE,KAAK;UACnBC,OAAO,EAAE,UAAU;UACnBF,QAAQ,EAAE;QACZ,CAAE;QAAAf,QAAA,EACH;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,oBACE3C,OAAA,CAAAE,SAAA;IAAAiC,QAAA,GAEG,CAACxB,QAAQ,iBACRX,OAAA;MAAK8B,SAAS,EAAC,2BAA2B;MAACE,KAAK,EAAE;QAAEgB,eAAe,EAAE,SAAS;QAAEV,MAAM,EAAE,MAAM;QAAEe,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,cAAc,EAAE,QAAQ;QAAEH,OAAO,EAAE;MAAU,CAAE;MAAAjB,QAAA,eACpLnC,OAAA,CAACP,IAAI;QAACoD,EAAE,EAAC,iBAAiB;QAACf,SAAS,EAAC,oDAAoD;QAAAK,QAAA,eACvFnC,OAAA;UACEwD,GAAG,EAAE3D,qBAAqB,CAAC,CAAC,CAAC4D,IAAK;UAClCC,GAAG,EAAE7D,qBAAqB,CAAC,CAAC,CAAC6D,GAAI;UACjC5B,SAAS,EAAC,WAAW;UACrBE,KAAK,EAAE;YAAE2B,SAAS,EAAE,OAAO;YAAEV,QAAQ,EAAE;UAAO;QAAE;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACN,EAGAhC,QAAQ,iBAAIX,OAAA,CAAC6B,kBAAkB;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAGlClC,iBAAiB,iBAChBT,OAAA;MACE8B,SAAS,EAAC,WAAW;MACrBE,KAAK,EAAE;QACL4B,QAAQ,EAAE,OAAO;QACjBC,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACThB,eAAe,EAAE,iBAAiB;QAClCiB,MAAM,EAAE;MACV,CAAE;MACFlC,OAAO,EAAEb;IAAoB;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CACF,EAGAhC,QAAQ,iBACPX,OAAA;MACE8B,SAAS,EAAE,sDAAsDrB,iBAAiB,GAAG,MAAM,GAAG,MAAM,EAAG;MACvGuB,KAAK,EAAE;QACLK,KAAK,EAAE,OAAO;QACdC,MAAM,EAAE,OAAO;QACfuB,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPG,MAAM,EAAE,IAAI;QACZC,SAAS,EAAEzD,iBAAiB,GAAG,eAAe,GAAG,mBAAmB;QACpE0D,UAAU,EAAE;MACd,CAAE;MAAAhC,QAAA,gBAGFnC,OAAA;QAAK8B,SAAS,EAAC,8BAA8B;QAACE,KAAK,EAAE;UAAEM,MAAM,EAAE,MAAM;UAAEe,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,cAAc,EAAE,eAAe;UAAEH,OAAO,EAAE;QAAe,CAAE;QAAAjB,QAAA,gBACvKnC,OAAA,CAACP,IAAI;UAACoD,EAAE,EAAC,iBAAiB;UAACf,SAAS,EAAC,sBAAsB;UAAAK,QAAA,eACzDnC,OAAA;YACEwD,GAAG,EAAE3D,qBAAqB,CAAC,CAAC,CAAC4D,IAAK;YAClCC,GAAG,EAAE7D,qBAAqB,CAAC,CAAC,CAAC6D,GAAI;YACjC5B,SAAS,EAAC,WAAW;YACrBE,KAAK,EAAE;cAAE2B,SAAS,EAAE,MAAM;cAAEV,QAAQ,EAAE;YAAO;UAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACP3C,OAAA;UACE8B,SAAS,EAAC,iDAAiD;UAC3DC,OAAO,EAAEb,mBAAoB;UAC7B,cAAW,YAAY;UAAAiB,QAAA,eAEvBnC,OAAA,CAACJ,IAAI;YAACwC,IAAI,EAAC,0BAA0B;YAACC,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACN3C,OAAA;QAAK8B,SAAS,EAAC,eAAe;QAACE,KAAK,EAAE;UAAEM,MAAM,EAAE;QAAqB,CAAE;QAAAH,QAAA,eACrEnC,OAAA,CAAC4C,cAAc;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGA,CAAChC,QAAQ,iBACRX,OAAA;MAAK8B,SAAS,EAAC,uBAAuB;MAACE,KAAK,EAAE;QAAEoC,SAAS,EAAE;MAAsB,CAAE;MAAAjC,QAAA,eACjFnC,OAAA,CAAC4C,cAAc;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CACN;EAAA,eACD,CAAC;AAEP;AAACvC,EAAA,CA9RQD,OAAO;EAAA,QACGT,WAAW,EACXC,WAAW;AAAA;AAAA0E,EAAA,GAFrBlE,OAAO;AAgShB,eAAeA,OAAO;AAAC,IAAAkE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}