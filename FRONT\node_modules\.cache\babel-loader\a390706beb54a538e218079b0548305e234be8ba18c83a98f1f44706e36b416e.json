{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NEW_LMS_FIXING\\\\FRONT\\\\src\\\\pages\\\\user\\\\settings\\\\SuccessPayUPayment.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { successPayUPayment } from \"../../../services/userService\";\nimport { useNavigate, useLocation } from \"react-router-dom\";\nimport { Icon } from \"@iconify/react\";\nimport \"./SuccessPayment.css\";\n\n// Helper function to get currency symbol\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst getCurrencySymbol = currency => {\n  switch (currency === null || currency === void 0 ? void 0 : currency.toUpperCase()) {\n    case 'INR':\n      return '₹';\n    case 'USD':\n      return '$';\n    case 'SGD':\n      return 'S$';\n    case 'EUR':\n      return '€';\n    case 'GBP':\n      return '£';\n    default:\n      return '$';\n    // Default to USD symbol\n  }\n};\nconst SuccessPayUPayment = () => {\n  _s();\n  var _data$price;\n  // alert(\"SuccessPayUPayment\");\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [data, setData] = useState({});\n  const [isLoading, setIsLoading] = useState(true);\n  const [hasError, setHasError] = useState(false);\n  const [retryCount, setRetryCount] = useState(0);\n  const maxRetries = 3;\n\n  // Extract query params\n  const queryParams = new URLSearchParams(location.search);\n  const courseId = queryParams.get(\"course_id\");\n  const txnId = queryParams.get(\"txnid\");\n  const sendToValidation = async (isRetry = false) => {\n    console.log(\"=== PayU Success Payment Validation Started ===\");\n    console.log(\"Course ID:\", courseId);\n    console.log(\"Transaction ID:\", txnId);\n    console.log(\"Is Retry:\", isRetry);\n    if (!courseId || !txnId) {\n      console.error(\"Missing course_id or txnid\");\n      setHasError(true);\n      setIsLoading(false);\n      return;\n    }\n    try {\n      // Add a small delay for retries to allow backend to process\n      if (isRetry) {\n        await new Promise(resolve => setTimeout(resolve, 2000));\n      }\n      console.log(\"Calling successPayUPayment API...\");\n      const response = await successPayUPayment(courseId, txnId);\n      console.log(\"Success PayU payment response:\", response);\n      if (response && response.success) {\n        console.log(\"Payment validation successful:\", response.data);\n        setData(response.data);\n        setHasError(false);\n        setIsLoading(false);\n      } else {\n        console.error(\"Payment validation failed:\", response);\n\n        // Retry logic\n        if (retryCount < maxRetries) {\n          console.log(`Retrying payment validation... (${retryCount + 1}/${maxRetries})`);\n          setRetryCount(prev => prev + 1);\n          setTimeout(() => sendToValidation(true), 3000);\n        } else {\n          setHasError(true);\n          setIsLoading(false);\n        }\n      }\n    } catch (error) {\n      console.error(\"Error in PayU success payment:\", error);\n\n      // Retry logic for errors\n      if (retryCount < maxRetries) {\n        console.log(`Retrying due to error... (${retryCount + 1}/${maxRetries})`);\n        setRetryCount(prev => prev + 1);\n        setTimeout(() => sendToValidation(true), 3000);\n      } else {\n        setHasError(true);\n        setIsLoading(false);\n      }\n    }\n  };\n  const redirectToCourse = () => {\n    navigate(\"/user/courses\");\n  };\n  const handleManualRetry = () => {\n    console.log('Manual retry triggered');\n    setRetryCount(0);\n    setIsLoading(true);\n    setHasError(false);\n    sendToValidation(true);\n  };\n\n  // Start validation when component mounts\n  useEffect(() => {\n    if (courseId && txnId) {\n      sendToValidation();\n    } else {\n      console.error(\"Missing required parameters\");\n      setHasError(true);\n      setIsLoading(false);\n    }\n  }, []);\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-center align-items-center\",\n      style: {\n        minHeight: \"60vh\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner-border text-primary mb-3\",\n          role: \"status\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-muted\",\n          children: retryCount > 0 ? `Retrying payment verification... (Attempt ${retryCount + 1}/${maxRetries})` : \"Verifying your PayU payment...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this), retryCount > 0 && /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"text-info d-block mt-2\",\n          children: \"Please wait, we're ensuring your payment is properly processed...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this);\n  }\n  if (hasError) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"payment-error-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-content text-center\",\n        children: [/*#__PURE__*/_jsxDEV(Icon, {\n          icon: \"mdi:alert-circle\",\n          className: \"text-danger mb-3\",\n          width: \"64\",\n          height: \"64\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-danger mb-3\",\n          children: \"Payment Verification Failed\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-muted mb-4\",\n          children: \"We couldn't verify your payment automatically. This might be due to a temporary issue.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"d-flex justify-content-center gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-primary\",\n            onClick: handleManualRetry,\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              icon: \"mdi:refresh\",\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this), \"Try Again\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"btn btn-outline-secondary\",\n            onClick: redirectToCourse,\n            children: \"Go to Courses\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"success-payment-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"success-payment-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"success-icon-wrapper mb-4\",\n        children: /*#__PURE__*/_jsxDEV(Icon, {\n          icon: \"mdi:check-circle\",\n          className: \"text-success\",\n          width: \"64\",\n          height: \"64\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"success-title\",\n        children: \"PayU Payment Successful!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"success-subtitle\",\n        children: \"Your transaction has been completed successfully via PayU.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"amount-display my-4\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"amount-value\",\n          children: [getCurrencySymbol(data.currency), (_data$price = data.price) !== null && _data$price !== void 0 ? _data$price : \"0\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"transaction-details\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detail-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"detail-label\",\n            children: \"Transaction ID\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"detail-value\",\n            children: [\"#\", data.payment_id ? data.payment_id.toString().toUpperCase().slice(0, 15) : \"-\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detail-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"detail-label\",\n            children: \"PayU Transaction ID\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"detail-value\",\n            children: [\"#\", data.transaction_id ? data.transaction_id.toString().toUpperCase().slice(0, 15) : \"-\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detail-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"detail-label\",\n            children: \"Date\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"detail-value\",\n            children: new Date().toLocaleDateString(\"en-IN\", {\n              year: \"numeric\",\n              month: \"long\",\n              day: \"numeric\"\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detail-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"detail-label\",\n            children: \"Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"detail-value status-success\",\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              icon: \"mdi:check-circle\",\n              className: \"me-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this), \"Completed\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detail-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"detail-label\",\n            children: \"Payment Method\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"detail-value\",\n            children: \"PayU Gateway\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"action-buttons mt-4\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn btn-primary btn-lg w-100 mb-3\",\n          onClick: redirectToCourse,\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            icon: \"mdi:play-circle\",\n            className: \"me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this), \"Go Back to Course\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 164,\n    columnNumber: 5\n  }, this);\n};\n_s(SuccessPayUPayment, \"DeGgc2uiZuA9ptdbp1TbXDZgSvk=\", false, function () {\n  return [useNavigate, useLocation];\n});\n_c = SuccessPayUPayment;\nexport default SuccessPayUPayment;\nvar _c;\n$RefreshReg$(_c, \"SuccessPayUPayment\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "successPayUPayment", "useNavigate", "useLocation", "Icon", "jsxDEV", "_jsxDEV", "getCurrencySymbol", "currency", "toUpperCase", "SuccessPayUPayment", "_s", "_data$price", "navigate", "location", "data", "setData", "isLoading", "setIsLoading", "<PERSON><PERSON><PERSON><PERSON>", "setHasError", "retryCount", "setRetryCount", "maxRetries", "queryParams", "URLSearchParams", "search", "courseId", "get", "txnId", "sendToValidation", "isRetry", "console", "log", "error", "Promise", "resolve", "setTimeout", "response", "success", "prev", "redirectToCourse", "handleManualRetry", "className", "style", "minHeight", "children", "role", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "icon", "width", "height", "onClick", "price", "payment_id", "toString", "slice", "transaction_id", "Date", "toLocaleDateString", "year", "month", "day", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/NEW_LMS_FIXING/FRONT/src/pages/user/settings/SuccessPayUPayment.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { successPayUPayment } from \"../../../services/userService\";\nimport { useNavigate, useLocation } from \"react-router-dom\";\nimport { Icon } from \"@iconify/react\";\nimport \"./SuccessPayment.css\";\n\n// Helper function to get currency symbol\nconst getCurrencySymbol = (currency) => {\n  switch (currency?.toUpperCase()) {\n    case 'INR':\n      return '₹';\n    case 'USD':\n      return '$';\n    case 'SGD':\n      return 'S$';\n    case 'EUR':\n      return '€';\n    case 'GBP':\n      return '£';\n    default:\n      return '$'; // Default to USD symbol\n  }\n};\n\nconst SuccessPayUPayment = () => {\n  // alert(\"SuccessPayUPayment\");\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  const [data, setData] = useState({});\n  const [isLoading, setIsLoading] = useState(true);\n  const [hasError, setHasError] = useState(false);\n  const [retryCount, setRetryCount] = useState(0);\n  const maxRetries = 3;\n\n  // Extract query params\n  const queryParams = new URLSearchParams(location.search);\n  const courseId = queryParams.get(\"course_id\");\n  const txnId = queryParams.get(\"txnid\");\n\n  const sendToValidation = async (isRetry = false) => {\n    console.log(\"=== PayU Success Payment Validation Started ===\");\n    console.log(\"Course ID:\", courseId);\n    console.log(\"Transaction ID:\", txnId);\n    console.log(\"Is Retry:\", isRetry);\n\n    if (!courseId || !txnId) {\n      console.error(\"Missing course_id or txnid\");\n      setHasError(true);\n      setIsLoading(false);\n      return;\n    }\n\n    try {\n      // Add a small delay for retries to allow backend to process\n      if (isRetry) {\n        await new Promise(resolve => setTimeout(resolve, 2000));\n      }\n\n      console.log(\"Calling successPayUPayment API...\");\n      const response = await successPayUPayment(courseId, txnId);\n      console.log(\"Success PayU payment response:\", response);\n\n      if (response && response.success) {\n        console.log(\"Payment validation successful:\", response.data);\n        setData(response.data);\n        setHasError(false);\n        setIsLoading(false);\n      } else {\n        console.error(\"Payment validation failed:\", response);\n        \n        // Retry logic\n        if (retryCount < maxRetries) {\n          console.log(`Retrying payment validation... (${retryCount + 1}/${maxRetries})`);\n          setRetryCount(prev => prev + 1);\n          setTimeout(() => sendToValidation(true), 3000);\n        } else {\n          setHasError(true);\n          setIsLoading(false);\n        }\n      }\n    } catch (error) {\n      console.error(\"Error in PayU success payment:\", error);\n      \n      // Retry logic for errors\n      if (retryCount < maxRetries) {\n        console.log(`Retrying due to error... (${retryCount + 1}/${maxRetries})`);\n        setRetryCount(prev => prev + 1);\n        setTimeout(() => sendToValidation(true), 3000);\n      } else {\n        setHasError(true);\n        setIsLoading(false);\n      }\n    }\n  };\n\n  const redirectToCourse = () => {\n    navigate(\"/user/courses\");\n  };\n\n  const handleManualRetry = () => {\n    console.log('Manual retry triggered');\n    setRetryCount(0);\n    setIsLoading(true);\n    setHasError(false);\n    sendToValidation(true);\n  };\n\n  // Start validation when component mounts\n  useEffect(() => {\n    if (courseId && txnId) {\n      sendToValidation();\n    } else {\n      console.error(\"Missing required parameters\");\n      setHasError(true);\n      setIsLoading(false);\n    }\n  }, []);\n\n  if (isLoading) {\n    return (\n      <div className=\"d-flex justify-content-center align-items-center\" style={{ minHeight: \"60vh\" }}>\n        <div className=\"text-center\">\n          <div className=\"spinner-border text-primary mb-3\" role=\"status\" />\n          <p className=\"text-muted\">\n            {retryCount > 0\n              ? `Retrying payment verification... (Attempt ${retryCount + 1}/${maxRetries})`\n              : \"Verifying your PayU payment...\"}\n          </p>\n          {retryCount > 0 && (\n            <small className=\"text-info d-block mt-2\">\n              Please wait, we're ensuring your payment is properly processed...\n            </small>\n          )}\n        </div>\n      </div>\n    );\n  }\n\n  if (hasError) {\n    return (\n      <div className=\"payment-error-container\">\n        <div className=\"error-content text-center\">\n          <Icon icon=\"mdi:alert-circle\" className=\"text-danger mb-3\" width=\"64\" height=\"64\" />\n          <h3 className=\"text-danger mb-3\">Payment Verification Failed</h3>\n          <p className=\"text-muted mb-4\">\n            We couldn't verify your payment automatically. This might be due to a temporary issue.\n          </p>\n          <div className=\"d-flex justify-content-center gap-3\">\n            <button className=\"btn btn-primary\" onClick={handleManualRetry}>\n              <Icon icon=\"mdi:refresh\" className=\"me-2\" />\n              Try Again\n            </button>\n            <button className=\"btn btn-outline-secondary\" onClick={redirectToCourse}>\n              Go to Courses\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"success-payment-container\">\n      <div className=\"success-payment-card\">\n        {/* Success Icon */}\n        <div className=\"success-icon-wrapper mb-4\">\n          <Icon icon=\"mdi:check-circle\" className=\"text-success\" width=\"64\" height=\"64\" />\n        </div>\n\n        {/* Success Message */}\n        <h2 className=\"success-title\">PayU Payment Successful!</h2>\n        <p className=\"success-subtitle\">Your transaction has been completed successfully via PayU.</p>\n\n        {/* Amount */}\n        <div className=\"amount-display my-4\">\n          <span className=\"amount-value\">{getCurrencySymbol(data.currency)}{data.price ?? \"0\"}</span>\n        </div>\n\n        {/* Transaction Details */}\n        <div className=\"transaction-details\">\n          <div className=\"detail-row\">\n            <span className=\"detail-label\">Transaction ID</span>\n            <span className=\"detail-value\">\n              #{data.payment_id ? data.payment_id.toString().toUpperCase().slice(0, 15) : \"-\"}\n            </span>\n          </div>\n          <div className=\"detail-row\">\n            <span className=\"detail-label\">PayU Transaction ID</span>\n            <span className=\"detail-value\">\n              #{data.transaction_id ? data.transaction_id.toString().toUpperCase().slice(0, 15) : \"-\"}\n            </span>\n          </div>\n          <div className=\"detail-row\">\n            <span className=\"detail-label\">Date</span>\n            <span className=\"detail-value\">\n              {new Date().toLocaleDateString(\"en-IN\", {\n                year: \"numeric\",\n                month: \"long\",\n                day: \"numeric\",\n              })}\n            </span>\n          </div>\n          <div className=\"detail-row\">\n            <span className=\"detail-label\">Status</span>\n            <span className=\"detail-value status-success\">\n              <Icon icon=\"mdi:check-circle\" className=\"me-1\" />\n              Completed\n            </span>\n          </div>\n          <div className=\"detail-row\">\n            <span className=\"detail-label\">Payment Method</span>\n            <span className=\"detail-value\">PayU Gateway</span>\n          </div>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"action-buttons mt-4\">\n          <button className=\"btn btn-primary btn-lg w-100 mb-3\" onClick={redirectToCourse}>\n            <Icon icon=\"mdi:play-circle\" className=\"me-2\" />\n            Go Back to Course\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SuccessPayUPayment;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,kBAAkB,QAAQ,+BAA+B;AAClE,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,IAAI,QAAQ,gBAAgB;AACrC,OAAO,sBAAsB;;AAE7B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,iBAAiB,GAAIC,QAAQ,IAAK;EACtC,QAAQA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEC,WAAW,CAAC,CAAC;IAC7B,KAAK,KAAK;MACR,OAAO,GAAG;IACZ,KAAK,KAAK;MACR,OAAO,GAAG;IACZ,KAAK,KAAK;MACR,OAAO,IAAI;IACb,KAAK,KAAK;MACR,OAAO,GAAG;IACZ,KAAK,KAAK;MACR,OAAO,GAAG;IACZ;MACE,OAAO,GAAG;IAAE;EAChB;AACF,CAAC;AAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,WAAA;EAC/B;EACA,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAMY,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACY,IAAI,EAAEC,OAAO,CAAC,GAAGhB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpC,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAMuB,UAAU,GAAG,CAAC;;EAEpB;EACA,MAAMC,WAAW,GAAG,IAAIC,eAAe,CAACX,QAAQ,CAACY,MAAM,CAAC;EACxD,MAAMC,QAAQ,GAAGH,WAAW,CAACI,GAAG,CAAC,WAAW,CAAC;EAC7C,MAAMC,KAAK,GAAGL,WAAW,CAACI,GAAG,CAAC,OAAO,CAAC;EAEtC,MAAME,gBAAgB,GAAG,MAAAA,CAAOC,OAAO,GAAG,KAAK,KAAK;IAClDC,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;IAC9DD,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEN,QAAQ,CAAC;IACnCK,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEJ,KAAK,CAAC;IACrCG,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEF,OAAO,CAAC;IAEjC,IAAI,CAACJ,QAAQ,IAAI,CAACE,KAAK,EAAE;MACvBG,OAAO,CAACE,KAAK,CAAC,4BAA4B,CAAC;MAC3Cd,WAAW,CAAC,IAAI,CAAC;MACjBF,YAAY,CAAC,KAAK,CAAC;MACnB;IACF;IAEA,IAAI;MACF;MACA,IAAIa,OAAO,EAAE;QACX,MAAM,IAAII,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MACzD;MAEAJ,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAChD,MAAMK,QAAQ,GAAG,MAAMrC,kBAAkB,CAAC0B,QAAQ,EAAEE,KAAK,CAAC;MAC1DG,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEK,QAAQ,CAAC;MAEvD,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,OAAO,EAAE;QAChCP,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEK,QAAQ,CAACvB,IAAI,CAAC;QAC5DC,OAAO,CAACsB,QAAQ,CAACvB,IAAI,CAAC;QACtBK,WAAW,CAAC,KAAK,CAAC;QAClBF,YAAY,CAAC,KAAK,CAAC;MACrB,CAAC,MAAM;QACLc,OAAO,CAACE,KAAK,CAAC,4BAA4B,EAAEI,QAAQ,CAAC;;QAErD;QACA,IAAIjB,UAAU,GAAGE,UAAU,EAAE;UAC3BS,OAAO,CAACC,GAAG,CAAC,mCAAmCZ,UAAU,GAAG,CAAC,IAAIE,UAAU,GAAG,CAAC;UAC/ED,aAAa,CAACkB,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;UAC/BH,UAAU,CAAC,MAAMP,gBAAgB,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;QAChD,CAAC,MAAM;UACLV,WAAW,CAAC,IAAI,CAAC;UACjBF,YAAY,CAAC,KAAK,CAAC;QACrB;MACF;IACF,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;;MAEtD;MACA,IAAIb,UAAU,GAAGE,UAAU,EAAE;QAC3BS,OAAO,CAACC,GAAG,CAAC,6BAA6BZ,UAAU,GAAG,CAAC,IAAIE,UAAU,GAAG,CAAC;QACzED,aAAa,CAACkB,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;QAC/BH,UAAU,CAAC,MAAMP,gBAAgB,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;MAChD,CAAC,MAAM;QACLV,WAAW,CAAC,IAAI,CAAC;QACjBF,YAAY,CAAC,KAAK,CAAC;MACrB;IACF;EACF,CAAC;EAED,MAAMuB,gBAAgB,GAAGA,CAAA,KAAM;IAC7B5B,QAAQ,CAAC,eAAe,CAAC;EAC3B,CAAC;EAED,MAAM6B,iBAAiB,GAAGA,CAAA,KAAM;IAC9BV,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrCX,aAAa,CAAC,CAAC,CAAC;IAChBJ,YAAY,CAAC,IAAI,CAAC;IAClBE,WAAW,CAAC,KAAK,CAAC;IAClBU,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;;EAED;EACA/B,SAAS,CAAC,MAAM;IACd,IAAI4B,QAAQ,IAAIE,KAAK,EAAE;MACrBC,gBAAgB,CAAC,CAAC;IACpB,CAAC,MAAM;MACLE,OAAO,CAACE,KAAK,CAAC,6BAA6B,CAAC;MAC5Cd,WAAW,CAAC,IAAI,CAAC;MACjBF,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,IAAID,SAAS,EAAE;IACb,oBACEX,OAAA;MAAKqC,SAAS,EAAC,kDAAkD;MAACC,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAO,CAAE;MAAAC,QAAA,eAC7FxC,OAAA;QAAKqC,SAAS,EAAC,aAAa;QAAAG,QAAA,gBAC1BxC,OAAA;UAAKqC,SAAS,EAAC,kCAAkC;UAACI,IAAI,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClE7C,OAAA;UAAGqC,SAAS,EAAC,YAAY;UAAAG,QAAA,EACtBzB,UAAU,GAAG,CAAC,GACX,6CAA6CA,UAAU,GAAG,CAAC,IAAIE,UAAU,GAAG,GAC5E;QAAgC;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,EACH9B,UAAU,GAAG,CAAC,iBACbf,OAAA;UAAOqC,SAAS,EAAC,wBAAwB;UAAAG,QAAA,EAAC;QAE1C;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIhC,QAAQ,EAAE;IACZ,oBACEb,OAAA;MAAKqC,SAAS,EAAC,yBAAyB;MAAAG,QAAA,eACtCxC,OAAA;QAAKqC,SAAS,EAAC,2BAA2B;QAAAG,QAAA,gBACxCxC,OAAA,CAACF,IAAI;UAACgD,IAAI,EAAC,kBAAkB;UAACT,SAAS,EAAC,kBAAkB;UAACU,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC;QAAI;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpF7C,OAAA;UAAIqC,SAAS,EAAC,kBAAkB;UAAAG,QAAA,EAAC;QAA2B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjE7C,OAAA;UAAGqC,SAAS,EAAC,iBAAiB;UAAAG,QAAA,EAAC;QAE/B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJ7C,OAAA;UAAKqC,SAAS,EAAC,qCAAqC;UAAAG,QAAA,gBAClDxC,OAAA;YAAQqC,SAAS,EAAC,iBAAiB;YAACY,OAAO,EAAEb,iBAAkB;YAAAI,QAAA,gBAC7DxC,OAAA,CAACF,IAAI;cAACgD,IAAI,EAAC,aAAa;cAACT,SAAS,EAAC;YAAM;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,aAE9C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT7C,OAAA;YAAQqC,SAAS,EAAC,2BAA2B;YAACY,OAAO,EAAEd,gBAAiB;YAAAK,QAAA,EAAC;UAEzE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE7C,OAAA;IAAKqC,SAAS,EAAC,2BAA2B;IAAAG,QAAA,eACxCxC,OAAA;MAAKqC,SAAS,EAAC,sBAAsB;MAAAG,QAAA,gBAEnCxC,OAAA;QAAKqC,SAAS,EAAC,2BAA2B;QAAAG,QAAA,eACxCxC,OAAA,CAACF,IAAI;UAACgD,IAAI,EAAC,kBAAkB;UAACT,SAAS,EAAC,cAAc;UAACU,KAAK,EAAC,IAAI;UAACC,MAAM,EAAC;QAAI;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7E,CAAC,eAGN7C,OAAA;QAAIqC,SAAS,EAAC,eAAe;QAAAG,QAAA,EAAC;MAAwB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3D7C,OAAA;QAAGqC,SAAS,EAAC,kBAAkB;QAAAG,QAAA,EAAC;MAA0D;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAG9F7C,OAAA;QAAKqC,SAAS,EAAC,qBAAqB;QAAAG,QAAA,eAClCxC,OAAA;UAAMqC,SAAS,EAAC,cAAc;UAAAG,QAAA,GAAEvC,iBAAiB,CAACQ,IAAI,CAACP,QAAQ,CAAC,GAAAI,WAAA,GAAEG,IAAI,CAACyC,KAAK,cAAA5C,WAAA,cAAAA,WAAA,GAAI,GAAG;QAAA;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxF,CAAC,eAGN7C,OAAA;QAAKqC,SAAS,EAAC,qBAAqB;QAAAG,QAAA,gBAClCxC,OAAA;UAAKqC,SAAS,EAAC,YAAY;UAAAG,QAAA,gBACzBxC,OAAA;YAAMqC,SAAS,EAAC,cAAc;YAAAG,QAAA,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpD7C,OAAA;YAAMqC,SAAS,EAAC,cAAc;YAAAG,QAAA,GAAC,GAC5B,EAAC/B,IAAI,CAAC0C,UAAU,GAAG1C,IAAI,CAAC0C,UAAU,CAACC,QAAQ,CAAC,CAAC,CAACjD,WAAW,CAAC,CAAC,CAACkD,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN7C,OAAA;UAAKqC,SAAS,EAAC,YAAY;UAAAG,QAAA,gBACzBxC,OAAA;YAAMqC,SAAS,EAAC,cAAc;YAAAG,QAAA,EAAC;UAAmB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzD7C,OAAA;YAAMqC,SAAS,EAAC,cAAc;YAAAG,QAAA,GAAC,GAC5B,EAAC/B,IAAI,CAAC6C,cAAc,GAAG7C,IAAI,CAAC6C,cAAc,CAACF,QAAQ,CAAC,CAAC,CAACjD,WAAW,CAAC,CAAC,CAACkD,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN7C,OAAA;UAAKqC,SAAS,EAAC,YAAY;UAAAG,QAAA,gBACzBxC,OAAA;YAAMqC,SAAS,EAAC,cAAc;YAAAG,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1C7C,OAAA;YAAMqC,SAAS,EAAC,cAAc;YAAAG,QAAA,EAC3B,IAAIe,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;cACtCC,IAAI,EAAE,SAAS;cACfC,KAAK,EAAE,MAAM;cACbC,GAAG,EAAE;YACP,CAAC;UAAC;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN7C,OAAA;UAAKqC,SAAS,EAAC,YAAY;UAAAG,QAAA,gBACzBxC,OAAA;YAAMqC,SAAS,EAAC,cAAc;YAAAG,QAAA,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC5C7C,OAAA;YAAMqC,SAAS,EAAC,6BAA6B;YAAAG,QAAA,gBAC3CxC,OAAA,CAACF,IAAI;cAACgD,IAAI,EAAC,kBAAkB;cAACT,SAAS,EAAC;YAAM;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,aAEnD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN7C,OAAA;UAAKqC,SAAS,EAAC,YAAY;UAAAG,QAAA,gBACzBxC,OAAA;YAAMqC,SAAS,EAAC,cAAc;YAAAG,QAAA,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpD7C,OAAA;YAAMqC,SAAS,EAAC,cAAc;YAAAG,QAAA,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN7C,OAAA;QAAKqC,SAAS,EAAC,qBAAqB;QAAAG,QAAA,eAClCxC,OAAA;UAAQqC,SAAS,EAAC,mCAAmC;UAACY,OAAO,EAAEd,gBAAiB;UAAAK,QAAA,gBAC9ExC,OAAA,CAACF,IAAI;YAACgD,IAAI,EAAC,iBAAiB;YAACT,SAAS,EAAC;UAAM;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,qBAElD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxC,EAAA,CA1MID,kBAAkB;EAAA,QAELR,WAAW,EACXC,WAAW;AAAA;AAAA+D,EAAA,GAHxBxD,kBAAkB;AA4MxB,eAAeA,kBAAkB;AAAC,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}