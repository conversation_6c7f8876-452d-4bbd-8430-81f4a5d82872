{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NEW_LMS_FIXING\\\\FRONT\\\\src\\\\pages\\\\admin\\\\courses\\\\CreateCourse.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Icon } from '@iconify/react';\nimport { useLocation, useParams, useNavigate } from 'react-router-dom';\nimport './CreateCourse.css';\nimport { decodeData } from '../../../utils/encodeAndEncode';\nimport { getCourseCertificate, createNewCourse, getCourseDetailsById, updateCourseDetails } from '../../../services/adminService';\nimport courseCategoriesData from '../../../utils/courseCategories.json';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst domain = process.env.REACT_APP_DOMAIN_URL;\nconsole.log('Domain from env:', domain);\nconsole.log('Domain type:', typeof domain);\n// alert(domain);\n\nfunction CreateCourse() {\n  _s();\n  var _location$state, _location$state2;\n  const location = useLocation();\n  const navigate = useNavigate();\n  const {\n    courseId\n  } = useParams();\n  // Only try to decode if courseId exists\n  const decodedCourseId = courseId ? decodeData(courseId) : null;\n  // console.log('Course ID:', courseId);\n  // console.log('Decoded Course ID:', decodedCourseId);\n\n  const isEditing = (_location$state = location.state) === null || _location$state === void 0 ? void 0 : _location$state.isEditing;\n  const initialCourseData = (_location$state2 = location.state) === null || _location$state2 === void 0 ? void 0 : _location$state2.courseData;\n  const [currentStep, setCurrentStep] = useState(1);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [isLoadingCourse, setIsLoadingCourse] = useState(false);\n  const [formData, setFormData] = useState({\n    courseTitle: '',\n    courseDescription: '',\n    courseLevel: 'intermediate',\n    courseLanguage: 'english',\n    courseCategory: '',\n    thumbnail: null,\n    thumbnailPreview: '',\n    certificateType: '',\n    courseType: 'free',\n    currency: domain === 'lms.tpi.sg' ? 'SGD' : 'INR',\n    price: '',\n    tags: [],\n    courseInfo: []\n  });\n  useEffect(() => {\n    if (isEditing && initialCourseData) {\n      setFormData(initialCourseData);\n    }\n  }, [isEditing, initialCourseData]);\n\n  // Fetch course details if courseId is available\n  useEffect(() => {\n    const fetchCourseDetails = async () => {\n      if (courseId && decodedCourseId) {\n        setIsLoadingCourse(true);\n        try {\n          console.log('Fetching course details for ID:', decodedCourseId);\n          const response = await getCourseDetailsById({\n            course_id: decodedCourseId\n          });\n          console.log('get Course Details Response:', response);\n          if (response.success && response.data.course) {\n            var _course$certificate_t;\n            const course = response.data.course;\n            console.log('Course details fetched:', course);\n\n            // Map API response to form data structure\n            setFormData({\n              courseTitle: course.course_name || '',\n              courseDescription: course.course_desc || '',\n              courseLevel: course.levels || '',\n              courseLanguage: course.course_language || '',\n              courseCategory: course.course_category || '',\n              thumbnail: null,\n              // Will be handled separately if needed\n              thumbnailPreview: course.banner_image || '',\n              certificateType: ((_course$certificate_t = course.certificate_template_id) === null || _course$certificate_t === void 0 ? void 0 : _course$certificate_t.toString()) || '',\n              courseType: course.course_type || 'free',\n              currency: course.currency || 'USD',\n              price: course.course_price || '',\n              tags: Array.isArray(course.tags) ? course.tags : [],\n              courseInfo: Array.isArray(course.course_info) ? course.course_info : []\n            });\n            console.log('Form data populated from API');\n          } else {\n            var _response$data, _response$data2;\n            console.error('Failed to fetch course details:', (_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.error_msg);\n            alert('Failed to load course details: ' + (((_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.error_msg) || 'Unknown error'));\n          }\n        } catch (error) {\n          console.error('Error fetching course details:', error);\n          alert('Error loading course details. Please try again.');\n        } finally {\n          setIsLoadingCourse(false);\n        }\n      }\n    };\n    fetchCourseDetails();\n  }, [courseId, decodedCourseId]);\n  const [certificateData, setCertificateData] = useState([]);\n  const fetchCertificateData = async () => {\n    try {\n      const response = await getCourseCertificate();\n      // console.log('Certificate Data:', response);\n      if (response.success) {\n        setCertificateData(response.data.templates);\n      } else {\n        // console.error('Invalid certificate data format:', response);\n        setCertificateData([]); // Reset to empty array if invalid data\n      }\n    } catch (error) {\n      console.error('Error fetching certificate data:', error);\n      setCertificateData([]); // Reset to empty array on error\n    }\n  };\n  useEffect(() => {\n    fetchCertificateData();\n  }, []);\n  const [tagInput, setTagInput] = useState('');\n  const [infoInput, setInfoInput] = useState('');\n  const [editingInfo, setEditingInfo] = useState(null);\n\n  // Memoized handlers for tag and info inputs\n  const handleTagInputChange = useCallback(e => {\n    setTagInput(e.target.value);\n  }, []);\n  const handleInfoInputChange = useCallback(e => {\n    setInfoInput(e.target.value);\n  }, []);\n  const handleInputChange = useCallback(e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prevState => ({\n      ...prevState,\n      [name]: value\n    }));\n  }, []);\n  const handleThumbnailChange = e => {\n    const file = e.target.files[0];\n    if (file) {\n      setFormData(prevState => ({\n        ...prevState,\n        thumbnail: file,\n        thumbnailPreview: URL.createObjectURL(file)\n      }));\n    }\n  };\n  const handleRemoveFile = type => {\n    if (type === 'thumbnail') {\n      setFormData(prevState => ({\n        ...prevState,\n        thumbnail: null,\n        thumbnailPreview: ''\n      }));\n    }\n  };\n  const handleAddTag = useCallback(e => {\n    e.preventDefault();\n    const tag = tagInput.trim();\n    if (tag && !formData.tags.includes(tag)) {\n      if (tag.length < 3) {\n        alert('Tag must be at least 3 characters long');\n        return;\n      }\n      if (formData.tags.length >= 8) {\n        alert('Maximum 8 tags allowed');\n        return;\n      }\n      setFormData(prev => ({\n        ...prev,\n        tags: [...prev.tags, tag]\n      }));\n      setTagInput('');\n    }\n  }, [tagInput, formData.tags]);\n  const handleRemoveTag = tagToRemove => {\n    setFormData(prev => ({\n      ...prev,\n      tags: prev.tags.filter(tag => tag !== tagToRemove)\n    }));\n  };\n  const handleTagInputKeyDown = e => {\n    if (e.key === 'Enter') {\n      e.preventDefault();\n      handleAddTag(e);\n    }\n  };\n  const handleAddInfo = useCallback(e => {\n    e.preventDefault();\n    if (infoInput.trim()) {\n      if (editingInfo !== null) {\n        setFormData(prev => ({\n          ...prev,\n          courseInfo: prev.courseInfo.map((info, index) => index === editingInfo ? infoInput.trim() : info)\n        }));\n        setEditingInfo(null);\n      } else {\n        setFormData(prev => ({\n          ...prev,\n          courseInfo: [...prev.courseInfo, infoInput.trim()]\n        }));\n      }\n      setInfoInput('');\n    }\n  }, [infoInput, editingInfo]);\n  const handleEditInfo = index => {\n    setInfoInput(formData.courseInfo[index]);\n    setEditingInfo(index);\n  };\n  const handleDeleteInfo = index => {\n    setFormData(prev => ({\n      ...prev,\n      courseInfo: prev.courseInfo.filter((_, i) => i !== index)\n    }));\n  };\n  const handleInfoInputKeyDown = e => {\n    if (e.key === 'Enter') {\n      e.preventDefault();\n      handleAddInfo(e);\n    }\n  };\n  const steps = [{\n    number: '01',\n    title: 'Course Information'\n  }, {\n    number: '02',\n    title: 'Course Media'\n  }, {\n    number: '03',\n    title: 'Additional information'\n  }, {\n    number: '04',\n    title: 'Pricing'\n  }, {\n    number: '05',\n    title: 'Preview & Submit'\n  }];\n\n  // Validation functions for each step\n  const validateStep1 = () => {\n    return formData.courseTitle.trim() !== '' && formData.courseDescription.trim() !== '' && formData.courseLevel !== '' && formData.courseLanguage !== '' && formData.courseCategory !== '';\n  };\n  const validateStep2 = () => {\n    // For editing mode, allow progression if thumbnail exists (either new upload or existing image)\n    if (courseId && decodedCourseId) {\n      return (formData.thumbnail !== null || formData.thumbnailPreview !== '') && formData.certificateType !== '';\n    }\n    // For new course creation, require new thumbnail upload\n    return formData.thumbnail !== null && formData.certificateType !== '';\n  };\n  const validateStep3 = () => {\n    // For editing mode, allow empty tags and courseInfo if course already exists\n    if (courseId && decodedCourseId) {\n      return true; // Allow progression in edit mode even with empty arrays\n    }\n    // For new course creation, require at least one tag and one course info\n    return formData.tags.length > 0 && formData.courseInfo.length > 0;\n  };\n  const validateStep4 = () => {\n    if (formData.courseType === 'free') {\n      return true;\n    }\n    return formData.price !== '' && parseFloat(formData.price) > 0;\n  };\n  const validateStep5 = () => {\n    // Preview step - all previous steps must be valid\n    return validateStep1() && validateStep2() && validateStep3() && validateStep4();\n  };\n  const isCurrentStepValid = () => {\n    switch (currentStep) {\n      case 1:\n        return validateStep1();\n      case 2:\n        return validateStep2();\n      case 3:\n        return validateStep3();\n      case 4:\n        return validateStep4();\n      case 5:\n        return validateStep5();\n      default:\n        return false;\n    }\n  };\n  const handleNext = () => {\n    if (currentStep < steps.length) {\n      setCurrentStep(currentStep + 1);\n    }\n  };\n  const handlePrevious = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n  const handleSubmit = async () => {\n    if (isCurrentStepValid()) {\n      setIsSubmitting(true);\n      try {\n        // Start the minimum 3-second timer\n        const minLoadingTime = new Promise(resolve => setTimeout(resolve, 3000));\n\n        // Create FormData object for file upload\n        const submitData = new FormData();\n\n        // Add all form fields to FormData\n        submitData.append('courseTitle', formData.courseTitle);\n        submitData.append('courseDescription', formData.courseDescription);\n        submitData.append('courseLevel', formData.courseLevel);\n        submitData.append('courseLanguage', formData.courseLanguage);\n        submitData.append('courseCategory', formData.courseCategory);\n        submitData.append('certificateType', formData.certificateType);\n        submitData.append('courseType', formData.courseType);\n        submitData.append('currency', formData.currency);\n        submitData.append('price', formData.price);\n        submitData.append('tags', JSON.stringify(formData.tags));\n        submitData.append('courseInfo', JSON.stringify(formData.courseInfo));\n        console.log('Submit Data:', submitData);\n\n        // Add thumbnail file if exists (for new upload)\n        if (formData.thumbnail) {\n          submitData.append('banner_image', formData.thumbnail);\n        }\n        console.log('Submitting course data...');\n        console.log('Is editing mode:', courseId && decodedCourseId);\n\n        // Choose API call based on edit mode\n        let apiCall;\n        if (courseId && decodedCourseId) {\n          // Update existing course\n          apiCall = updateCourseDetails(decodedCourseId, submitData);\n        } else {\n          // Create new course\n          apiCall = createNewCourse(submitData);\n        }\n\n        // Wait for both minimum time and API response\n        const [response] = await Promise.all([apiCall, minLoadingTime]);\n        if (response.success) {\n          console.log('Course operation successful:', response.data);\n          // Navigate to courses page after successful operation\n          navigate('/admin/courses');\n        } else {\n          var _response$data3;\n          setIsSubmitting(false);\n          const errorMsg = courseId && decodedCourseId ? 'Failed to update course' : 'Failed to create course';\n          alert(errorMsg + ': ' + (((_response$data3 = response.data) === null || _response$data3 === void 0 ? void 0 : _response$data3.error_msg) || 'Unknown error'));\n        }\n      } catch (error) {\n        setIsSubmitting(false);\n        console.error('Error with course operation:', error);\n        const errorMsg = courseId && decodedCourseId ? 'Error updating course' : 'Error creating course';\n        alert(errorMsg + '. Please try again.');\n      }\n    } else {\n      alert('Please fill in all required fields before submitting.');\n    }\n  };\n  const renderCourseInformationForm = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"course-information\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"courseTitle\",\n          className: \"form-label\",\n          children: \"Course Title *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          className: \"form-control\",\n          id: \"courseTitle\",\n          name: \"courseTitle\",\n          placeholder: \"Enter course title\",\n          value: formData.courseTitle,\n          onChange: handleInputChange,\n          maxLength: 100,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"text-muted\",\n          children: \"Maximum 100 characters allowed. Use a short and meaningful title for better readability.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"courseDescription\",\n          className: \"form-label\",\n          children: \"Course Description *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          className: \"form-control\",\n          id: \"courseDescription\",\n          name: \"courseDescription\",\n          rows: \"4\",\n          placeholder: \"Enter course description\",\n          value: formData.courseDescription,\n          onChange: handleInputChange,\n          maxLength: 1000,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"text-muted\",\n          children: \"Maximum 1000 characters allowed\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-4 mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"courseLevel\",\n          className: \"form-label\",\n          children: \"Course Level *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 417,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          className: \"form-select\",\n          id: \"courseLevel\",\n          name: \"courseLevel\",\n          value: formData.courseLevel,\n          onChange: handleInputChange,\n          required: true,\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            children: \"Select Level\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"beginner\",\n            children: \"Beginner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"intermediate\",\n            children: \"Intermediate\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"advanced\",\n            children: \"Advanced\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all-levels\",\n            children: \"All Levels\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 416,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-4 mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"courseLanguage\",\n          className: \"form-label\",\n          children: \"Course Language *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 435,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          className: \"form-select\",\n          id: \"courseLanguage\",\n          name: \"courseLanguage\",\n          value: formData.courseLanguage,\n          onChange: handleInputChange,\n          required: true,\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            children: \"Select Language\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"english\",\n            children: \"English\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"spanish\",\n            children: \"Spanish\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"french\",\n            children: \"French\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"german\",\n            children: \"German\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 448,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"chinese\",\n            children: \"Chinese\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 449,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"japanese\",\n            children: \"Japanese\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"hindi\",\n            children: \"Hindi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 436,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 434,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-4 mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"courseCategory\",\n          className: \"form-label\",\n          children: \"Course Category *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 456,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          className: \"form-select\",\n          id: \"courseCategory\",\n          name: \"courseCategory\",\n          value: formData.courseCategory,\n          onChange: handleInputChange,\n          required: true,\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            children: \"Select Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 13\n          }, this), courseCategoriesData.categories.map((category, index) => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: category,\n            children: category\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 455,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 383,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 382,\n    columnNumber: 5\n  }, this);\n  const renderCourseMediaForm = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"course-media\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-6 mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"form-label\",\n          children: \"Course Thumbnail *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 482,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: !formData.thumbnailPreview ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                id: \"thumbnail\",\n                className: \"d-none\",\n                accept: \"image/*\",\n                onChange: handleThumbnailChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"thumbnail\",\n                className: \"btn btn-outline-primary mb-3 d-flex flex-column align-items-center gap-2 mx-auto\",\n                style: {\n                  width: 'fit-content',\n                  cursor: 'pointer'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Icon, {\n                  icon: \"fluent:image-add-24-regular\",\n                  width: \"48\",\n                  height: \"48\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 499,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Upload Thumbnail\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-muted\",\n                  children: \"Recommended size: 1280x720px\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 501,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 494,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"position-relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: formData.thumbnailPreview,\n                alt: \"Course Thumbnail\",\n                className: \"img-fluid rounded\",\n                style: {\n                  width: '100%',\n                  height: 'auto'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 506,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn btn-danger d-flex align-items-center justify-content-center p-0 position-absolute\",\n                onClick: () => handleRemoveFile('thumbnail'),\n                type: \"button\",\n                style: {\n                  width: '24px',\n                  height: '24px',\n                  top: '8px',\n                  right: '8px',\n                  minWidth: 'unset',\n                  borderRadius: '4px'\n                },\n                children: /*#__PURE__*/_jsxDEV(Icon, {\n                  icon: \"fluent:delete-24-regular\",\n                  width: \"14\",\n                  height: \"14\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 525,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 483,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 481,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-6 mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"form-label\",\n          children: \"Certificate Type *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 535,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: [/*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"form-select mb-3\",\n              name: \"certificateType\",\n              value: formData.certificateType,\n              onChange: handleInputChange,\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select Certificate Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 545,\n                columnNumber: 17\n              }, this), Array.isArray(certificateData) && certificateData.map(cert => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: cert.id || cert._id,\n                children: cert.template_name || 'Unnamed Certificate'\n              }, cert.id || cert._id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 547,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 538,\n              columnNumber: 15\n            }, this), formData.certificateType && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"certificate-preview p-3 bg-light rounded\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(Icon, {\n                  icon: \"fluent:certificate-24-regular\",\n                  width: \"32\",\n                  height: \"32\",\n                  className: \"text-primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 556,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"mb-1\",\n                    children: [formData.certificateType.charAt(0).toUpperCase() + formData.certificateType.slice(1), \" Certificate\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 558,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: \"Will be issued upon course completion\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 559,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 557,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 555,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 554,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 536,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 534,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 479,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 478,\n    columnNumber: 5\n  }, this);\n  const renderAdditionalInformationForm = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"row\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"col-12 mb-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"Course Tags *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 577,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"row g-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-10\",\n                  children: /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    className: \"form-control rounded-0 rounded-start\",\n                    placeholder: \"Add tags (e.g., 'programming', 'web development')\",\n                    value: tagInput,\n                    onChange: handleTagInputChange,\n                    onKeyDown: handleTagInputKeyDown\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 581,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 580,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"btn btn-primary w-100 rounded-0 rounded-end\",\n                    onClick: handleAddTag,\n                    children: \"Add Tag\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 591,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 590,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 579,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"d-block text-muted mb-1\",\n                  children: \"Press Enter or click Add Tag to add\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 600,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"d-block text-muted\",\n                  children: [\"\\u2022 Minimum 3 characters per tag\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 603,\n                    columnNumber: 21\n                  }, this), \"\\u2022 Maximum 8 tags allowed\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 605,\n                    columnNumber: 21\n                  }, this), \"\\u2022 \", 8 - formData.tags.length, \" tags remaining\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 601,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 599,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex flex-wrap gap-2 mt-3\",\n              children: formData.tags.map((tag, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"badge bg-light text-dark border d-flex align-items-center gap-2 py-2 px-3\",\n                children: [tag, /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"btn btn-link text-danger p-0 d-flex align-items-center\",\n                  onClick: () => handleRemoveTag(tag),\n                  style: {\n                    minWidth: '20px',\n                    height: '20px'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Icon, {\n                    icon: \"fluent:dismiss-24-regular\",\n                    width: \"16\",\n                    height: \"16\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 623,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 617,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 612,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 576,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"Course Info *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 631,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"row g-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-10\",\n                  children: /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    className: \"form-control rounded-0 rounded-start\",\n                    placeholder: \"Add course information point (e.g., 'Lifetime access to course materials')\",\n                    value: infoInput,\n                    onChange: handleInfoInputChange,\n                    onKeyDown: handleInfoInputKeyDown\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 635,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 634,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"btn btn-primary w-100 rounded-0 rounded-end\",\n                    onClick: handleAddInfo,\n                    children: editingInfo !== null ? 'Update' : 'Add Info'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 645,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 644,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 633,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2\",\n                children: /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"d-block text-muted\",\n                  children: \"Add important information points about your course\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 654,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 653,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 632,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-3\",\n              children: formData.courseInfo.map((info, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center gap-2 p-2 border rounded mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(Icon, {\n                  icon: \"fluent:info-24-regular\",\n                  className: \"text-primary flex-shrink-0\",\n                  width: \"20\",\n                  height: \"20\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 666,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"flex-grow-1\",\n                  children: info\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 672,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    className: \"btn btn-link text-primary p-0 d-flex align-items-center\",\n                    onClick: () => handleEditInfo(index),\n                    style: {\n                      minWidth: '20px',\n                      height: '20px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Icon, {\n                      icon: \"fluent:edit-24-regular\",\n                      width: \"16\",\n                      height: \"16\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 680,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 674,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    className: \"btn btn-link text-danger p-0 d-flex align-items-center\",\n                    onClick: () => handleDeleteInfo(index),\n                    style: {\n                      minWidth: '20px',\n                      height: '20px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Icon, {\n                      icon: \"fluent:delete-24-regular\",\n                      width: \"16\",\n                      height: \"16\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 688,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 682,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 673,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 662,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 660,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 630,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 575,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 574,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 573,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 572,\n    columnNumber: 5\n  }, this);\n  const renderPreviewStep = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"row\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"col-12\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              icon: \"fluent:eye-24-regular\",\n              width: \"20\",\n              height: \"20\",\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 707,\n              columnNumber: 15\n            }, this), \"Course Preview\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 706,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-muted mb-0 mt-2\",\n            children: \"Review all your course information before submitting. If you need to make changes, use the \\\"Previous\\\" button to go back to any step.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 710,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 705,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"text-primary mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                icon: \"fluent:info-24-regular\",\n                width: \"18\",\n                height: \"18\",\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 718,\n                columnNumber: 17\n              }, this), \"Course Information\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 717,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-6 mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label text-muted\",\n                  children: \"Course Title\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 723,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-0 fw-medium\",\n                  children: formData.courseTitle || 'Not specified'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 724,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 722,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-6 mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label text-muted\",\n                  children: \"Course Category\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 727,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-0\",\n                  children: formData.courseCategory || 'Not specified'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 728,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 726,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-12 mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label text-muted\",\n                  children: \"Course Description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 731,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-0\",\n                  children: formData.courseDescription || 'Not specified'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 732,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 730,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-4 mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label text-muted\",\n                  children: \"Level\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 735,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-0 text-capitalize\",\n                  children: formData.courseLevel || 'Not specified'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 736,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 734,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-4 mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label text-muted\",\n                  children: \"Language\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 739,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-0 text-capitalize\",\n                  children: formData.courseLanguage || 'Not specified'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 740,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 738,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-4 mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label text-muted\",\n                  children: \"Certificate\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 743,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-0\",\n                  children: formData.certificateType ? `Certificate ID: ${formData.certificateType}` : 'Not specified'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 744,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 742,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 721,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 716,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 749,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"text-primary mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                icon: \"fluent:image-24-regular\",\n                width: \"18\",\n                height: \"18\",\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 754,\n                columnNumber: 17\n              }, this), \"Course Media\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 753,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"row\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-6 mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label text-muted\",\n                  children: \"Course Thumbnail\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 759,\n                  columnNumber: 19\n                }, this), formData.thumbnailPreview ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: formData.thumbnailPreview,\n                    alt: \"Course Thumbnail\",\n                    className: \"img-fluid rounded border\",\n                    style: {\n                      maxHeight: '150px',\n                      width: 'auto'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 762,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 761,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-0 text-muted\",\n                  children: \"No thumbnail uploaded\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 770,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 758,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 757,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 752,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 776,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"text-primary mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                icon: \"fluent:tag-24-regular\",\n                width: \"18\",\n                height: \"18\",\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 781,\n                columnNumber: 17\n              }, this), \"Additional Information\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 780,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-6 mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label text-muted\",\n                  children: \"Tags\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 786,\n                  columnNumber: 19\n                }, this), formData.tags.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex flex-wrap gap-2\",\n                  children: formData.tags.map((tag, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"badge bg-light text-dark border\",\n                    children: tag\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 790,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 788,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-0 text-muted\",\n                  children: \"No tags added\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 796,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 785,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-6 mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label text-muted\",\n                  children: \"Course Information Points\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 800,\n                  columnNumber: 19\n                }, this), formData.courseInfo.length > 0 ? /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"list-unstyled mb-0\",\n                  children: formData.courseInfo.map((info, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"mb-1\",\n                    children: [/*#__PURE__*/_jsxDEV(Icon, {\n                      icon: \"fluent:checkmark-circle-24-regular\",\n                      width: \"16\",\n                      height: \"16\",\n                      className: \"text-success me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 805,\n                      columnNumber: 27\n                    }, this), info]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 804,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 802,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-0 text-muted\",\n                  children: \"No course information added\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 811,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 799,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 784,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 779,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 817,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"text-primary mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                icon: \"fluent:payment-24-regular\",\n                width: \"18\",\n                height: \"18\",\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 822,\n                columnNumber: 17\n              }, this), \"Pricing Information\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 821,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-3 mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label text-muted\",\n                  children: \"Course Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 827,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-0\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `badge ${formData.courseType === 'free' ? 'bg-success' : 'bg-primary'}`,\n                    children: formData.courseType === 'free' ? 'Free Course' : 'Paid Course'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 829,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 828,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 826,\n                columnNumber: 17\n              }, this), formData.courseType === 'paid' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-3 mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label text-muted\",\n                    children: \"Currency\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 837,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mb-0\",\n                    children: formData.currency\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 838,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 836,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-3 mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label text-muted\",\n                    children: \"Price\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 841,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mb-0 fw-medium\",\n                    children: [formData.currency === 'USD' && '$', formData.currency === 'EUR' && '€', formData.currency === 'GBP' && '£', formData.currency === 'INR' && '₹', formData.currency === 'SGD' && 'S$', formData.price]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 842,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 840,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 825,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 820,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 714,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 704,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 703,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 702,\n    columnNumber: 5\n  }, this);\n  const renderPricingStep = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"row\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"col-12 mb-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"Course Type *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 868,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-check\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  className: \"form-check-input\",\n                  id: \"free\",\n                  name: \"courseType\",\n                  value: \"free\",\n                  checked: formData.courseType === 'free',\n                  onChange: e => setFormData(prev => ({\n                    ...prev,\n                    courseType: e.target.value,\n                    price: '',\n                    currency: 'USD'\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 871,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-check-label\",\n                  htmlFor: \"free\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Free Course\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 886,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 887,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: \"No payment required for enrollment\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 888,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 885,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 870,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-check\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  className: \"form-check-input\",\n                  id: \"paid\",\n                  name: \"courseType\",\n                  value: \"paid\",\n                  checked: formData.courseType === 'paid',\n                  onChange: e => setFormData(prev => ({\n                    ...prev,\n                    courseType: e.target.value\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 892,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-check-label\",\n                  htmlFor: \"paid\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Paid Course\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 905,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 906,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: \"Students must purchase to access content\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 907,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 904,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 891,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 869,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 867,\n            columnNumber: 13\n          }, this), formData.courseType === 'paid' && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Select Currency\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 916,\n                columnNumber: 7\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-2\",\n                children: /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-muted\",\n                  children: [\"Debug: Domain = \\\"\", domain, \"\\\"\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 918,\n                  columnNumber: 9\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 917,\n                columnNumber: 7\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"form-select\",\n                value: formData.currency,\n                onChange: e => setFormData(prev => ({\n                  ...prev,\n                  currency: e.target.value\n                })),\n                children: [domain === 'lms.tpi.sg' && /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"SGD\",\n                  children: \"SGD (S$)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 931,\n                  columnNumber: 11\n                }, this), (domain === 'lms.nxgenvarsity.com' || domain === 'lms.creatorfoundation.in') && /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"INR\",\n                  children: \"INR (\\u20B9)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 934,\n                  columnNumber: 11\n                }, this), domain !== 'lms.tpi.sg' && domain !== 'lms.nxgenvarsity.com' && domain !== 'lms.creatorfoundation.in' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"SGD\",\n                    children: \"SGD (S$)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 940,\n                    columnNumber: 13\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"INR\",\n                    children: \"INR (\\u20B9)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 941,\n                    columnNumber: 13\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"USD\",\n                    children: \"USD ($)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 942,\n                    columnNumber: 13\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"EUR\",\n                    children: \"EUR (\\u20AC)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 943,\n                    columnNumber: 13\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"GBP\",\n                    children: \"GBP (\\xA3)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 944,\n                    columnNumber: 13\n                  }, this)]\n                }, void 0, true)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 920,\n                columnNumber: 7\n              }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                className: \"text-muted\",\n                children: [domain === 'lms.tpi.sg' && 'Currency is fixed to SGD for this platform.', (domain === 'lms.nxgenvarsity.com' || domain === 'lms.creatorfoundation.in') && 'Currency is fixed to INR for this platform.', domain !== 'lms.tpi.sg' && domain !== 'lms.nxgenvarsity.com' && domain !== 'lms.creatorfoundation.in' && 'Select the currency for your course pricing.', !domain && 'No domain found']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 949,\n                columnNumber: 7\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 915,\n              columnNumber: 5\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Course Price *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 960,\n                columnNumber: 7\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"input-group-text\",\n                  children: [formData.currency === 'INR' && '₹', formData.currency === 'SGD' && 'S$', formData.currency === 'USD' && '$', formData.currency === 'EUR' && '€', formData.currency === 'GBP' && '£']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 962,\n                  columnNumber: 9\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  className: \"form-control\",\n                  placeholder: \"Enter course price\",\n                  value: formData.price,\n                  onChange: e => setFormData(prev => ({\n                    ...prev,\n                    price: e.target.value\n                  })),\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 969,\n                  columnNumber: 9\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 961,\n                columnNumber: 7\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 959,\n              columnNumber: 5\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 865,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 864,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 863,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 862,\n    columnNumber: 5\n  }, this);\n  const renderStepContent = () => {\n    switch (currentStep) {\n      case 1:\n        return renderCourseInformationForm();\n      case 2:\n        return renderCourseMediaForm();\n      case 3:\n        return renderAdditionalInformationForm();\n      case 4:\n        return renderPricingStep();\n      case 5:\n        return renderPreviewStep();\n      default:\n        return null;\n    }\n  };\n\n  // Loading overlay component for submission\n  const SubmissionLoadingOverlay = () => {\n    const isEditMode = courseId && decodedCourseId;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center\",\n      style: {\n        backgroundColor: 'rgba(0, 0, 0, 0.7)',\n        zIndex: 9999\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center text-white\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner-border text-primary mb-3\",\n          role: \"status\",\n          style: {\n            width: '3rem',\n            height: '3rem'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"visually-hidden\",\n            children: \"Loading...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1022,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1021,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n          className: \"mb-2\",\n          children: isEditMode ? 'Updating Your Course...' : 'Creating Your Course...'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1024,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mb-0\",\n          children: \"Please wait while we process your course information.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1025,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1020,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1015,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Loading overlay component for fetching course details\n  const CourseLoadingOverlay = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center\",\n    style: {\n      backgroundColor: 'rgba(0, 0, 0, 0.7)',\n      zIndex: 9999\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center text-white\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner-border text-primary mb-3\",\n        role: \"status\",\n        style: {\n          width: '3rem',\n          height: '3rem'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"visually-hidden\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1040,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1039,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n        className: \"mb-2\",\n        children: \"Loading Course Details...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1042,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mb-0\",\n        children: \"Please wait while we fetch the course information.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1043,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1038,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1033,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: [isSubmitting && /*#__PURE__*/_jsxDEV(SubmissionLoadingOverlay, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1051,\n      columnNumber: 24\n    }, this), isLoadingCourse && /*#__PURE__*/_jsxDEV(CourseLoadingOverlay, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1052,\n      columnNumber: 27\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stepper-card\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stepper-wrapper\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stepper-line-bg\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1058,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stepper-line-progress\",\n          style: {\n            width: `${(currentStep - 1) / (steps.length - 1) * 100}%`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1060,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stepper-content\",\n          children: steps.map((step, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stepper-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `stepper-circle ${currentStep > index + 1 ? 'completed' : currentStep === index + 1 ? 'active' : ''}`,\n              children: step.number\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1074,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `stepper-label ${currentStep === index + 1 ? 'active' : ''}`,\n              children: step.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1082,\n              columnNumber: 17\n            }, this)]\n          }, step.number, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1070,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1068,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1056,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1055,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stepper-card\",\n      children: renderStepContent()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1092,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stepper-card\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stepper-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn-previous\",\n          onClick: handlePrevious,\n          disabled: currentStep === 1 || isSubmitting,\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            icon: \"fluent:arrow-left-24-regular\",\n            width: \"20\",\n            height: \"20\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1104,\n            columnNumber: 13\n          }, this), \"Previous\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1099,\n          columnNumber: 11\n        }, this), currentStep === steps.length ? /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn-next\",\n          onClick: handleSubmit,\n          disabled: !isCurrentStepValid() || isSubmitting,\n          children: isSubmitting ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner-border spinner-border-sm me-2\",\n              role: \"status\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"visually-hidden\",\n                children: \"Loading...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1116,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1115,\n              columnNumber: 19\n            }, this), courseId && decodedCourseId ? 'Updating Course...' : 'Creating Course...']\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              icon: \"fluent:checkmark-24-regular\",\n              width: \"20\",\n              height: \"20\",\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1122,\n              columnNumber: 19\n            }, this), courseId && decodedCourseId ? 'Update Course' : 'Submit Course']\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1108,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn-next\",\n          onClick: handleNext,\n          disabled: !isCurrentStepValid(),\n          children: [currentStep === steps.length - 1 ? 'Preview' : 'Next', /*#__PURE__*/_jsxDEV(Icon, {\n            icon: \"fluent:arrow-right-24-regular\",\n            width: \"20\",\n            height: \"20\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1134,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1128,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1098,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1097,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1049,\n    columnNumber: 5\n  }, this);\n}\n_s(CreateCourse, \"vDxbNN/NWReyWPgRHaCEH9fovh0=\", false, function () {\n  return [useLocation, useNavigate, useParams];\n});\n_c = CreateCourse;\nexport default CreateCourse;\nvar _c;\n$RefreshReg$(_c, \"CreateCourse\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Icon", "useLocation", "useParams", "useNavigate", "decodeData", "getCourseCertificate", "createNewCourse", "getCourseDetailsById", "updateCourseDetails", "courseCategoriesData", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "domain", "process", "env", "REACT_APP_DOMAIN_URL", "console", "log", "CreateCourse", "_s", "_location$state", "_location$state2", "location", "navigate", "courseId", "decodedCourseId", "isEditing", "state", "initialCourseData", "courseData", "currentStep", "setCurrentStep", "isSubmitting", "setIsSubmitting", "isLoadingCourse", "setIsLoadingCourse", "formData", "setFormData", "courseTitle", "courseDescription", "courseLevel", "courseLanguage", "courseCategory", "thumbnail", "thumbnailPreview", "certificateType", "courseType", "currency", "price", "tags", "courseInfo", "fetchCourseDetails", "response", "course_id", "success", "data", "course", "_course$certificate_t", "course_name", "course_desc", "levels", "course_language", "course_category", "banner_image", "certificate_template_id", "toString", "course_type", "course_price", "Array", "isArray", "course_info", "_response$data", "_response$data2", "error", "error_msg", "alert", "certificateData", "setCertificateData", "fetchCertificateData", "templates", "tagInput", "setTagInput", "infoInput", "setInfoInput", "editingInfo", "setEditingInfo", "handleTagInputChange", "e", "target", "value", "handleInfoInputChange", "handleInputChange", "name", "prevState", "handleThumbnailChange", "file", "files", "URL", "createObjectURL", "handleRemoveFile", "type", "handleAddTag", "preventDefault", "tag", "trim", "includes", "length", "prev", "handleRemoveTag", "tagToRemove", "filter", "handleTagInputKeyDown", "key", "handleAddInfo", "map", "info", "index", "handleEditInfo", "handleDeleteInfo", "_", "i", "handleInfoInputKeyDown", "steps", "number", "title", "validateStep1", "validateStep2", "validateStep3", "validateStep4", "parseFloat", "validateStep5", "isCurrentStepValid", "handleNext", "handlePrevious", "handleSubmit", "minLoadingTime", "Promise", "resolve", "setTimeout", "submitData", "FormData", "append", "JSON", "stringify", "apiCall", "all", "_response$data3", "errorMsg", "renderCourseInformationForm", "className", "children", "htmlFor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "id", "placeholder", "onChange", "max<PERSON><PERSON><PERSON>", "required", "rows", "categories", "category", "renderCourseMediaForm", "accept", "style", "width", "cursor", "icon", "height", "src", "alt", "onClick", "top", "right", "min<PERSON><PERSON><PERSON>", "borderRadius", "cert", "_id", "template_name", "char<PERSON>t", "toUpperCase", "slice", "renderAdditionalInformationForm", "onKeyDown", "renderPreviewStep", "maxHeight", "renderPricingStep", "checked", "renderStepContent", "SubmissionLoadingOverlay", "isEditMode", "backgroundColor", "zIndex", "role", "CourseLoadingOverlay", "step", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/NEW_LMS_FIXING/FRONT/src/pages/admin/courses/CreateCourse.jsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { Icon } from '@iconify/react';\nimport { useLocation, useParams, useNavigate } from 'react-router-dom';\nimport './CreateCourse.css';\nimport { decodeData } from '../../../utils/encodeAndEncode';\nimport { getCourseCertificate, createNewCourse, getCourseDetailsById, updateCourseDetails } from '../../../services/adminService';\nimport courseCategoriesData from '../../../utils/courseCategories.json';\nconst domain = process.env.REACT_APP_DOMAIN_URL;\nconsole.log('Domain from env:', domain);\nconsole.log('Domain type:', typeof domain);\n// alert(domain);\n\nfunction CreateCourse() {\n  const location = useLocation();\n  const navigate = useNavigate();\n  const { courseId } = useParams();\n  // Only try to decode if courseId exists\n  const decodedCourseId = courseId ? decodeData(courseId) : null;\n  // console.log('Course ID:', courseId);\n  // console.log('Decoded Course ID:', decodedCourseId);\n\n  const isEditing = location.state?.isEditing;\n  const initialCourseData = location.state?.courseData;\n\n  const [currentStep, setCurrentStep] = useState(1);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [isLoadingCourse, setIsLoadingCourse] = useState(false);\n  const [formData, setFormData] = useState({\n    courseTitle: '',\n    courseDescription: '',\n    courseLevel: 'intermediate',\n    courseLanguage: 'english',\n    courseCategory: '',\n    thumbnail: null,\n    thumbnailPreview: '',\n    certificateType: '',\n    courseType: 'free',\n    currency: domain === 'lms.tpi.sg'? 'SGD' : 'INR',\n    price: '',\n    tags: [],\n    courseInfo: []\n  });\n\n  useEffect(() => {\n    if (isEditing && initialCourseData) {\n      setFormData(initialCourseData);\n    }\n  }, [isEditing, initialCourseData]);\n\n  // Fetch course details if courseId is available\n  useEffect(() => {\n    const fetchCourseDetails = async () => {\n      if (courseId && decodedCourseId) {\n        setIsLoadingCourse(true);\n        try {\n          console.log('Fetching course details for ID:', decodedCourseId);\n          const response = await getCourseDetailsById({ course_id: decodedCourseId });\n\n          console.log('get Course Details Response:', response);\n\n          if (response.success && response.data.course) {\n            const course = response.data.course;\n            console.log('Course details fetched:', course);\n\n            // Map API response to form data structure\n            setFormData({\n              courseTitle: course.course_name || '',\n              courseDescription: course.course_desc || '',\n              courseLevel: course.levels || '',\n              courseLanguage: course.course_language || '',\n              courseCategory: course.course_category || '',\n              thumbnail: null, // Will be handled separately if needed\n              thumbnailPreview: course.banner_image || '',\n              certificateType: course.certificate_template_id?.toString() || '',\n              courseType: course.course_type || 'free',\n              currency: course.currency || 'USD',\n              price: course.course_price || '',\n              tags: Array.isArray(course.tags) ? course.tags : [],\n              courseInfo: Array.isArray(course.course_info) ? course.course_info : []\n            });\n\n            console.log('Form data populated from API');\n          } else {\n            console.error('Failed to fetch course details:', response.data?.error_msg);\n            alert('Failed to load course details: ' + (response.data?.error_msg || 'Unknown error'));\n          }\n        } catch (error) {\n          console.error('Error fetching course details:', error);\n          alert('Error loading course details. Please try again.');\n        } finally {\n          setIsLoadingCourse(false);\n        }\n      }\n    };\n\n    fetchCourseDetails();\n  }, [courseId, decodedCourseId]);\n\n  const [certificateData, setCertificateData] = useState([]);\n\n  const fetchCertificateData = async () => {\n    try {\n      const response = await getCourseCertificate();\n      // console.log('Certificate Data:', response);\n      if (response.success) {\n        setCertificateData(response.data.templates);\n      } else {\n        // console.error('Invalid certificate data format:', response);\n        setCertificateData([]); // Reset to empty array if invalid data\n      }\n    } catch (error) {\n      console.error('Error fetching certificate data:', error);\n      setCertificateData([]); // Reset to empty array on error\n    }\n  };\n\n  useEffect(() => {\n    fetchCertificateData();\n  }, []);\n\n  const [tagInput, setTagInput] = useState('');\n  const [infoInput, setInfoInput] = useState('');\n  const [editingInfo, setEditingInfo] = useState(null);\n\n  // Memoized handlers for tag and info inputs\n  const handleTagInputChange = useCallback((e) => {\n    setTagInput(e.target.value);\n  }, []);\n\n  const handleInfoInputChange = useCallback((e) => {\n    setInfoInput(e.target.value);\n  }, []);\n\n  const handleInputChange = useCallback((e) => {\n    const { name, value } = e.target;\n    setFormData(prevState => ({\n      ...prevState,\n      [name]: value\n    }));\n  }, []);\n\n  const handleThumbnailChange = (e) => {\n    const file = e.target.files[0];\n    if (file) {\n      setFormData(prevState => ({\n        ...prevState,\n        thumbnail: file,\n        thumbnailPreview: URL.createObjectURL(file)\n      }));\n    }\n  };\n\n  const handleRemoveFile = (type) => {\n    if (type === 'thumbnail') {\n      setFormData(prevState => ({\n        ...prevState,\n        thumbnail: null,\n        thumbnailPreview: ''\n      }));\n    }\n  };\n\n  const handleAddTag = useCallback((e) => {\n    e.preventDefault();\n    const tag = tagInput.trim();\n    if (tag && !formData.tags.includes(tag)) {\n      if (tag.length < 3) {\n        alert('Tag must be at least 3 characters long');\n        return;\n      }\n      if (formData.tags.length >= 8) {\n        alert('Maximum 8 tags allowed');\n        return;\n      }\n      setFormData(prev => ({\n        ...prev,\n        tags: [...prev.tags, tag]\n      }));\n      setTagInput('');\n    }\n  }, [tagInput, formData.tags]);\n\n  const handleRemoveTag = (tagToRemove) => {\n    setFormData(prev => ({\n      ...prev,\n      tags: prev.tags.filter(tag => tag !== tagToRemove)\n    }));\n  };\n\n  const handleTagInputKeyDown = (e) => {\n    if (e.key === 'Enter') {\n      e.preventDefault();\n      handleAddTag(e);\n    }\n  };\n\n  const handleAddInfo = useCallback((e) => {\n    e.preventDefault();\n    if (infoInput.trim()) {\n      if (editingInfo !== null) {\n        setFormData(prev => ({\n          ...prev,\n          courseInfo: prev.courseInfo.map((info, index) =>\n            index === editingInfo ? infoInput.trim() : info\n          )\n        }));\n        setEditingInfo(null);\n      } else {\n        setFormData(prev => ({\n          ...prev,\n          courseInfo: [...prev.courseInfo, infoInput.trim()]\n        }));\n      }\n      setInfoInput('');\n    }\n  }, [infoInput, editingInfo]);\n\n  const handleEditInfo = (index) => {\n    setInfoInput(formData.courseInfo[index]);\n    setEditingInfo(index);\n  };\n\n  const handleDeleteInfo = (index) => {\n    setFormData(prev => ({\n      ...prev,\n      courseInfo: prev.courseInfo.filter((_, i) => i !== index)\n    }));\n  };\n\n  const handleInfoInputKeyDown = (e) => {\n    if (e.key === 'Enter') {\n      e.preventDefault();\n      handleAddInfo(e);\n    }\n  };\n\n  const steps = [\n    { number: '01', title: 'Course Information' },\n    { number: '02', title: 'Course Media' },\n    { number: '03', title: 'Additional information' },\n    { number: '04', title: 'Pricing' },\n    { number: '05', title: 'Preview & Submit' }\n  ];\n\n  // Validation functions for each step\n  const validateStep1 = () => {\n    return formData.courseTitle.trim() !== '' &&\n           formData.courseDescription.trim() !== '' &&\n           formData.courseLevel !== '' &&\n           formData.courseLanguage !== '' &&\n           formData.courseCategory !== '';\n  };\n\n  const validateStep2 = () => {\n    // For editing mode, allow progression if thumbnail exists (either new upload or existing image)\n    if (courseId && decodedCourseId) {\n      return (formData.thumbnail !== null || formData.thumbnailPreview !== '') &&\n             formData.certificateType !== '';\n    }\n    // For new course creation, require new thumbnail upload\n    return formData.thumbnail !== null && formData.certificateType !== '';\n  };\n\n  const validateStep3 = () => {\n    // For editing mode, allow empty tags and courseInfo if course already exists\n    if (courseId && decodedCourseId) {\n      return true; // Allow progression in edit mode even with empty arrays\n    }\n    // For new course creation, require at least one tag and one course info\n    return formData.tags.length > 0 && formData.courseInfo.length > 0;\n  };\n\n  const validateStep4 = () => {\n    if (formData.courseType === 'free') {\n      return true;\n    }\n    return formData.price !== '' && parseFloat(formData.price) > 0;\n  };\n\n  const validateStep5 = () => {\n    // Preview step - all previous steps must be valid\n    return validateStep1() && validateStep2() && validateStep3() && validateStep4();\n  };\n\n  const isCurrentStepValid = () => {\n    switch (currentStep) {\n      case 1:\n        return validateStep1();\n      case 2:\n        return validateStep2();\n      case 3:\n        return validateStep3();\n      case 4:\n        return validateStep4();\n      case 5:\n        return validateStep5();\n      default:\n        return false;\n    }\n  };\n\n  const handleNext = () => {\n    if (currentStep < steps.length) {\n      setCurrentStep(currentStep + 1);\n    }\n  };\n\n  const handlePrevious = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n\n  const handleSubmit = async () => {\n    if (isCurrentStepValid()) {\n      setIsSubmitting(true);\n\n      try {\n        // Start the minimum 3-second timer\n        const minLoadingTime = new Promise(resolve => setTimeout(resolve, 3000));\n\n        // Create FormData object for file upload\n        const submitData = new FormData();\n\n        // Add all form fields to FormData\n        submitData.append('courseTitle', formData.courseTitle);\n        submitData.append('courseDescription', formData.courseDescription);\n        submitData.append('courseLevel', formData.courseLevel);\n        submitData.append('courseLanguage', formData.courseLanguage);\n        submitData.append('courseCategory', formData.courseCategory);\n        submitData.append('certificateType', formData.certificateType);\n        submitData.append('courseType', formData.courseType);\n        submitData.append('currency', formData.currency);\n        submitData.append('price', formData.price);\n        submitData.append('tags', JSON.stringify(formData.tags));\n        submitData.append('courseInfo', JSON.stringify(formData.courseInfo));\n        \n        console.log('Submit Data:', submitData);\n\n        // Add thumbnail file if exists (for new upload)\n        if (formData.thumbnail) {\n          submitData.append('banner_image', formData.thumbnail);\n        }\n\n        console.log('Submitting course data...');\n        console.log('Is editing mode:', courseId && decodedCourseId);\n\n        // Choose API call based on edit mode\n        let apiCall;\n        if (courseId && decodedCourseId) {\n          // Update existing course\n          apiCall = updateCourseDetails(decodedCourseId, submitData);\n        } else {\n          // Create new course\n          apiCall = createNewCourse(submitData);\n        }\n\n        // Wait for both minimum time and API response\n        const [response] = await Promise.all([apiCall, minLoadingTime]);\n\n        if (response.success) {\n          console.log('Course operation successful:', response.data);\n          // Navigate to courses page after successful operation\n          navigate('/admin/courses');\n        } else {\n          setIsSubmitting(false);\n          const errorMsg = courseId && decodedCourseId ? 'Failed to update course' : 'Failed to create course';\n          alert(errorMsg + ': ' + (response.data?.error_msg || 'Unknown error'));\n        }\n      } catch (error) {\n        setIsSubmitting(false);\n        console.error('Error with course operation:', error);\n        const errorMsg = courseId && decodedCourseId ? 'Error updating course' : 'Error creating course';\n        alert(errorMsg + '. Please try again.');\n      }\n    } else {\n      alert('Please fill in all required fields before submitting.');\n    }\n  };\n\n  const renderCourseInformationForm = () => (\n    <div className=\"course-information\">\n      <div className=\"row\">\n        <div className=\"col-12 mb-3\">\n          <label htmlFor=\"courseTitle\" className=\"form-label\">Course Title *</label>\n          <input\n            type=\"text\"\n            className=\"form-control\"\n            id=\"courseTitle\"\n            name=\"courseTitle\"\n            placeholder=\"Enter course title\"\n            value={formData.courseTitle}\n            onChange={handleInputChange}\n            maxLength={100}\n            required\n          />\n          <small className=\"text-muted\">Maximum 100 characters allowed. Use a short and meaningful title for better readability.</small>\n        </div>\n\n        <div className=\"col-12 mb-3\">\n          <label htmlFor=\"courseDescription\" className=\"form-label\">Course Description *</label>\n          <textarea\n            className=\"form-control\"\n            id=\"courseDescription\"\n            name=\"courseDescription\"\n            rows=\"4\"\n            placeholder=\"Enter course description\"\n            value={formData.courseDescription}\n            onChange={handleInputChange}\n            maxLength={1000}\n            required\n          ></textarea>\n          <small className=\"text-muted\">Maximum 1000 characters allowed</small>\n        </div>\n\n        <div className=\"col-md-4 mb-3\">\n          <label htmlFor=\"courseLevel\" className=\"form-label\">Course Level *</label>\n          <select\n            className=\"form-select\"\n            id=\"courseLevel\"\n            name=\"courseLevel\"\n            value={formData.courseLevel}\n            onChange={handleInputChange}\n            required\n          >\n            <option value=\"\">Select Level</option>\n            <option value=\"beginner\">Beginner</option>\n            <option value=\"intermediate\">Intermediate</option>\n            <option value=\"advanced\">Advanced</option>\n            <option value=\"all-levels\">All Levels</option>\n          </select>\n        </div>\n\n        <div className=\"col-md-4 mb-3\">\n          <label htmlFor=\"courseLanguage\" className=\"form-label\">Course Language *</label>\n          <select\n            className=\"form-select\"\n            id=\"courseLanguage\"\n            name=\"courseLanguage\"\n            value={formData.courseLanguage}\n            onChange={handleInputChange}\n            required\n          >\n            <option value=\"\">Select Language</option>\n            <option value=\"english\">English</option>\n            <option value=\"spanish\">Spanish</option>\n            <option value=\"french\">French</option>\n            <option value=\"german\">German</option>\n            <option value=\"chinese\">Chinese</option>\n            <option value=\"japanese\">Japanese</option>\n            <option value=\"hindi\">Hindi</option>\n          </select>\n        </div>\n\n        <div className=\"col-md-4 mb-3\">\n          <label htmlFor=\"courseCategory\" className=\"form-label\">Course Category *</label>\n          <select\n            className=\"form-select\"\n            id=\"courseCategory\"\n            name=\"courseCategory\"\n            value={formData.courseCategory}\n            onChange={handleInputChange}\n            required\n          >\n            <option value=\"\">Select Category</option>\n            {courseCategoriesData.categories.map((category, index) => (\n              <option key={index} value={category}>\n                {category}\n              </option>\n            ))}\n          </select>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderCourseMediaForm = () => (\n    <div className=\"course-media\">\n      <div className=\"row\">\n        {/* Thumbnail Upload */}\n        <div className=\"col-md-6 mb-4\">\n          <label className=\"form-label\">Course Thumbnail *</label>\n          <div className=\"card\">\n            <div className=\"card-body\">\n              {!formData.thumbnailPreview ? (\n                <div className=\"text-center p-4\">\n                  <input\n                    type=\"file\"\n                    id=\"thumbnail\"\n                    className=\"d-none\"\n                    accept=\"image/*\"\n                    onChange={handleThumbnailChange}\n                  />\n                  <label \n                    htmlFor=\"thumbnail\" \n                    className=\"btn btn-outline-primary mb-3 d-flex flex-column align-items-center gap-2 mx-auto\"\n                    style={{ width: 'fit-content', cursor: 'pointer' }}\n                  >\n                    <Icon icon=\"fluent:image-add-24-regular\" width=\"48\" height=\"48\" />\n                    <span>Upload Thumbnail</span>\n                    <small className=\"text-muted\">Recommended size: 1280x720px</small>\n                  </label>\n                </div>\n              ) : (\n                <div className=\"position-relative\">\n                  <img\n                    src={formData.thumbnailPreview}\n                    alt=\"Course Thumbnail\"\n                    className=\"img-fluid rounded\"\n                    style={{ width: '100%', height: 'auto' }}\n                  />\n                  <button\n                    className=\"btn btn-danger d-flex align-items-center justify-content-center p-0 position-absolute\"\n                    onClick={() => handleRemoveFile('thumbnail')}\n                    type=\"button\"\n                    style={{ \n                      width: '24px', \n                      height: '24px', \n                      top: '8px', \n                      right: '8px',\n                      minWidth: 'unset',\n                      borderRadius: '4px'\n                    }}\n                  >\n                    <Icon icon=\"fluent:delete-24-regular\" width=\"14\" height=\"14\" />\n                  </button>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Certificate Selection */}\n        <div className=\"col-md-6 mb-4\">\n          <label className=\"form-label\">Certificate Type *</label>\n          <div className=\"card\">\n            <div className=\"card-body\">\n              <select\n                className=\"form-select mb-3\"\n                name=\"certificateType\"\n                value={formData.certificateType}\n                onChange={handleInputChange}\n                required\n              >\n                <option value=\"\">Select Certificate Type</option>\n                {Array.isArray(certificateData) && certificateData.map((cert) => (\n                  <option key={cert.id || cert._id} value={cert.id || cert._id}>\n                    {cert.template_name || 'Unnamed Certificate'}\n                  </option>\n                ))}\n              </select>\n\n              {formData.certificateType && (\n                <div className=\"certificate-preview p-3 bg-light rounded\">\n                  <div className=\"d-flex align-items-center gap-3\">\n                    <Icon icon=\"fluent:certificate-24-regular\" width=\"32\" height=\"32\" className=\"text-primary\" />\n                    <div>\n                      <h6 className=\"mb-1\">{formData.certificateType.charAt(0).toUpperCase() + formData.certificateType.slice(1)} Certificate</h6>\n                      <small className=\"text-muted\">Will be issued upon course completion</small>\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderAdditionalInformationForm = () => (\n    <div className=\"row\">\n      <div className=\"col-12 mb-4\">\n        <div className=\"card\">\n          <div className=\"card-body\">\n            <div className=\"mb-4\">\n              <label className=\"form-label\">Course Tags *</label>\n              <div className=\"mb-2\">\n                <div className=\"row g-2\">\n                  <div className=\"col-10\">\n                    <input\n                      type=\"text\"\n                      className=\"form-control rounded-0 rounded-start\"\n                      placeholder=\"Add tags (e.g., 'programming', 'web development')\"\n                      value={tagInput}\n                      onChange={handleTagInputChange}\n                      onKeyDown={handleTagInputKeyDown}\n                    />\n                  </div>\n                  <div className=\"col-2\">\n                    <button \n                      className=\"btn btn-primary w-100 rounded-0 rounded-end\"\n                      onClick={handleAddTag}\n                    >\n                      Add Tag\n                    </button>\n                  </div>\n                </div>\n                <div className=\"mt-2\">\n                  <small className=\"d-block text-muted mb-1\">Press Enter or click Add Tag to add</small>\n                  <small className=\"d-block text-muted\">\n                    • Minimum 3 characters per tag\n                    <br />\n                    • Maximum 8 tags allowed\n                    <br />\n                    • {8 - formData.tags.length} tags remaining\n                  </small>\n                </div>\n              </div>\n              <div className=\"d-flex flex-wrap gap-2 mt-3\">\n                {formData.tags.map((tag, index) => (\n                  <div \n                    key={index}\n                    className=\"badge bg-light text-dark border d-flex align-items-center gap-2 py-2 px-3\"\n                  >\n                    {tag}\n                    <button\n                      type=\"button\"\n                      className=\"btn btn-link text-danger p-0 d-flex align-items-center\"\n                      onClick={() => handleRemoveTag(tag)}\n                      style={{ minWidth: '20px', height: '20px' }}\n                    >\n                      <Icon icon=\"fluent:dismiss-24-regular\" width=\"16\" height=\"16\" />\n                    </button>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            <div className=\"mb-4\">\n              <label className=\"form-label\">Course Info *</label>\n              <div className=\"mb-2\">\n                <div className=\"row g-2\">\n                  <div className=\"col-10\">\n                    <input\n                      type=\"text\"\n                      className=\"form-control rounded-0 rounded-start\"\n                      placeholder=\"Add course information point (e.g., 'Lifetime access to course materials')\"\n                      value={infoInput}\n                      onChange={handleInfoInputChange}\n                      onKeyDown={handleInfoInputKeyDown}\n                    />\n                  </div>\n                  <div className=\"col-2\">\n                    <button\n                      className=\"btn btn-primary w-100 rounded-0 rounded-end\"\n                      onClick={handleAddInfo}\n                    >\n                      {editingInfo !== null ? 'Update' : 'Add Info'}\n                    </button>\n                  </div>\n                </div>\n                <div className=\"mt-2\">\n                  <small className=\"d-block text-muted\">\n                    Add important information points about your course\n                  </small>\n                </div>\n              </div>\n\n              <div className=\"mt-3\">\n                {formData.courseInfo.map((info, index) => (\n                  <div\n                    key={index}\n                    className=\"d-flex align-items-center gap-2 p-2 border rounded mb-2\"\n                  >\n                    <Icon\n                      icon=\"fluent:info-24-regular\"\n                      className=\"text-primary flex-shrink-0\"\n                      width=\"20\"\n                      height=\"20\"\n                    />\n                    <span className=\"flex-grow-1\">{info}</span>\n                    <div className=\"d-flex gap-2\">\n                      <button\n                        type=\"button\"\n                        className=\"btn btn-link text-primary p-0 d-flex align-items-center\"\n                        onClick={() => handleEditInfo(index)}\n                        style={{ minWidth: '20px', height: '20px' }}\n                      >\n                        <Icon icon=\"fluent:edit-24-regular\" width=\"16\" height=\"16\" />\n                      </button>\n                      <button\n                        type=\"button\"\n                        className=\"btn btn-link text-danger p-0 d-flex align-items-center\"\n                        onClick={() => handleDeleteInfo(index)}\n                        style={{ minWidth: '20px', height: '20px' }}\n                      >\n                        <Icon icon=\"fluent:delete-24-regular\" width=\"16\" height=\"16\" />\n                      </button>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderPreviewStep = () => (\n    <div className=\"row\">\n      <div className=\"col-12\">\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h5 className=\"mb-0\">\n              <Icon icon=\"fluent:eye-24-regular\" width=\"20\" height=\"20\" className=\"me-2\" />\n              Course Preview\n            </h5>\n            <p className=\"text-muted mb-0 mt-2\">\n              Review all your course information before submitting. If you need to make changes, use the \"Previous\" button to go back to any step.\n            </p>\n          </div>\n          <div className=\"card-body\">\n            {/* Course Information Section */}\n            <div className=\"mb-4\">\n              <h6 className=\"text-primary mb-3\">\n                <Icon icon=\"fluent:info-24-regular\" width=\"18\" height=\"18\" className=\"me-2\" />\n                Course Information\n              </h6>\n              <div className=\"row\">\n                <div className=\"col-md-6 mb-3\">\n                  <label className=\"form-label text-muted\">Course Title</label>\n                  <p className=\"mb-0 fw-medium\">{formData.courseTitle || 'Not specified'}</p>\n                </div>\n                <div className=\"col-md-6 mb-3\">\n                  <label className=\"form-label text-muted\">Course Category</label>\n                  <p className=\"mb-0\">{formData.courseCategory || 'Not specified'}</p>\n                </div>\n                <div className=\"col-12 mb-3\">\n                  <label className=\"form-label text-muted\">Course Description</label>\n                  <p className=\"mb-0\">{formData.courseDescription || 'Not specified'}</p>\n                </div>\n                <div className=\"col-md-4 mb-3\">\n                  <label className=\"form-label text-muted\">Level</label>\n                  <p className=\"mb-0 text-capitalize\">{formData.courseLevel || 'Not specified'}</p>\n                </div>\n                <div className=\"col-md-4 mb-3\">\n                  <label className=\"form-label text-muted\">Language</label>\n                  <p className=\"mb-0 text-capitalize\">{formData.courseLanguage || 'Not specified'}</p>\n                </div>\n                <div className=\"col-md-4 mb-3\">\n                  <label className=\"form-label text-muted\">Certificate</label>\n                  <p className=\"mb-0\">{formData.certificateType ? `Certificate ID: ${formData.certificateType}` : 'Not specified'}</p>\n                </div>\n              </div>\n            </div>\n\n            <hr />\n\n            {/* Course Media Section */}\n            <div className=\"mb-4\">\n              <h6 className=\"text-primary mb-3\">\n                <Icon icon=\"fluent:image-24-regular\" width=\"18\" height=\"18\" className=\"me-2\" />\n                Course Media\n              </h6>\n              <div className=\"row\">\n                <div className=\"col-md-6 mb-3\">\n                  <label className=\"form-label text-muted\">Course Thumbnail</label>\n                  {formData.thumbnailPreview ? (\n                    <div>\n                      <img\n                        src={formData.thumbnailPreview}\n                        alt=\"Course Thumbnail\"\n                        className=\"img-fluid rounded border\"\n                        style={{ maxHeight: '150px', width: 'auto' }}\n                      />\n                    </div>\n                  ) : (\n                    <p className=\"mb-0 text-muted\">No thumbnail uploaded</p>\n                  )}\n                </div>\n              </div>\n            </div>\n\n            <hr />\n\n            {/* Additional Information Section */}\n            <div className=\"mb-4\">\n              <h6 className=\"text-primary mb-3\">\n                <Icon icon=\"fluent:tag-24-regular\" width=\"18\" height=\"18\" className=\"me-2\" />\n                Additional Information\n              </h6>\n              <div className=\"row\">\n                <div className=\"col-md-6 mb-3\">\n                  <label className=\"form-label text-muted\">Tags</label>\n                  {formData.tags.length > 0 ? (\n                    <div className=\"d-flex flex-wrap gap-2\">\n                      {formData.tags.map((tag, index) => (\n                        <span key={index} className=\"badge bg-light text-dark border\">\n                          {tag}\n                        </span>\n                      ))}\n                    </div>\n                  ) : (\n                    <p className=\"mb-0 text-muted\">No tags added</p>\n                  )}\n                </div>\n                <div className=\"col-md-6 mb-3\">\n                  <label className=\"form-label text-muted\">Course Information Points</label>\n                  {formData.courseInfo.length > 0 ? (\n                    <ul className=\"list-unstyled mb-0\">\n                      {formData.courseInfo.map((info, index) => (\n                        <li key={index} className=\"mb-1\">\n                          <Icon icon=\"fluent:checkmark-circle-24-regular\" width=\"16\" height=\"16\" className=\"text-success me-2\" />\n                          {info}\n                        </li>\n                      ))}\n                    </ul>\n                  ) : (\n                    <p className=\"mb-0 text-muted\">No course information added</p>\n                  )}\n                </div>\n              </div>\n            </div>\n\n            <hr />\n\n            {/* Pricing Section */}\n            <div className=\"mb-4\">\n              <h6 className=\"text-primary mb-3\">\n                <Icon icon=\"fluent:payment-24-regular\" width=\"18\" height=\"18\" className=\"me-2\" />\n                Pricing Information\n              </h6>\n              <div className=\"row\">\n                <div className=\"col-md-3 mb-3\">\n                  <label className=\"form-label text-muted\">Course Type</label>\n                  <p className=\"mb-0\">\n                    <span className={`badge ${formData.courseType === 'free' ? 'bg-success' : 'bg-primary'}`}>\n                      {formData.courseType === 'free' ? 'Free Course' : 'Paid Course'}\n                    </span>\n                  </p>\n                </div>\n                {formData.courseType === 'paid' && (\n                  <>\n                    <div className=\"col-md-3 mb-3\">\n                      <label className=\"form-label text-muted\">Currency</label>\n                      <p className=\"mb-0\">{formData.currency}</p>\n                    </div>\n                    <div className=\"col-md-3 mb-3\">\n                      <label className=\"form-label text-muted\">Price</label>\n                      <p className=\"mb-0 fw-medium\">\n                        {formData.currency === 'USD' && '$'}\n                        {formData.currency === 'EUR' && '€'}\n                        {formData.currency === 'GBP' && '£'}\n                        {formData.currency === 'INR' && '₹'}\n                        {formData.currency === 'SGD' && 'S$'}\n                        {formData.price}\n                      </p>\n                    </div>\n                  </>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderPricingStep = () => (\n    <div className=\"row\">\n      <div className=\"col-12 mb-4\">\n        <div className=\"card\">\n          <div className=\"card-body\">\n  \n            <div className=\"mb-4\">\n              <label className=\"form-label\">Course Type *</label>\n              <div className=\"d-flex gap-4\">\n                <div className=\"form-check\">\n                  <input\n                    type=\"radio\"\n                    className=\"form-check-input\"\n                    id=\"free\"\n                    name=\"courseType\"\n                    value=\"free\"\n                    checked={formData.courseType === 'free'}\n                    onChange={(e) => setFormData(prev => ({\n                      ...prev,\n                      courseType: e.target.value,\n                      price: '',\n                      currency: 'USD'\n                    }))}\n                  />\n                  <label className=\"form-check-label\" htmlFor=\"free\">\n                    <strong>Free Course</strong>\n                    <br />\n                    <small className=\"text-muted\">No payment required for enrollment</small>\n                  </label>\n                </div>\n                <div className=\"form-check\">\n                  <input\n                    type=\"radio\"\n                    className=\"form-check-input\"\n                    id=\"paid\"\n                    name=\"courseType\"\n                    value=\"paid\"\n                    checked={formData.courseType === 'paid'}\n                    onChange={(e) => setFormData(prev => ({\n                      ...prev,\n                      courseType: e.target.value\n                    }))}\n                  />\n                  <label className=\"form-check-label\" htmlFor=\"paid\">\n                    <strong>Paid Course</strong>\n                    <br />\n                    <small className=\"text-muted\">Students must purchase to access content</small>\n                  </label>\n                </div>\n              </div>\n            </div>\n\n            {formData.courseType === 'paid' && (\n  <>\n    <div className=\"mb-3\">\n      <label className=\"form-label\">Select Currency</label>\n      <div className=\"mb-2\">\n        <small className=\"text-muted\">Debug: Domain = \"{domain}\"</small>\n      </div>\n      <select\n        className=\"form-select\"\n        value={formData.currency}\n        onChange={(e) =>\n          setFormData((prev) => ({\n            ...prev,\n            currency: e.target.value,\n          }))\n        }\n      >\n        {domain === 'lms.tpi.sg' && (\n          <option value=\"SGD\">SGD (S$)</option>\n        )}\n        {(domain === 'lms.nxgenvarsity.com' || domain === 'lms.creatorfoundation.in') && (\n          <option value=\"INR\">INR (₹)</option>\n        )}\n        {domain !== 'lms.tpi.sg' && \n         domain !== 'lms.nxgenvarsity.com' && \n         domain !== 'lms.creatorfoundation.in' && (\n          <>\n            <option value=\"SGD\">SGD (S$)</option>\n            <option value=\"INR\">INR (₹)</option>\n            <option value=\"USD\">USD ($)</option>\n            <option value=\"EUR\">EUR (€)</option>\n            <option value=\"GBP\">GBP (£)</option>\n          </>\n        )}\n      </select>\n\n      <small className=\"text-muted\">\n        {domain === 'lms.tpi.sg' && 'Currency is fixed to SGD for this platform.'}\n        {(domain === 'lms.nxgenvarsity.com' || domain === 'lms.creatorfoundation.in') && 'Currency is fixed to INR for this platform.'}\n        {domain !== 'lms.tpi.sg' && \n         domain !== 'lms.nxgenvarsity.com' && \n         domain !== 'lms.creatorfoundation.in' && 'Select the currency for your course pricing.'}\n        {!domain && 'No domain found'}\n      </small>\n    </div>\n\n    <div className=\"mb-3\">\n      <label className=\"form-label\">Course Price *</label>\n      <div className=\"input-group\">\n        <span className=\"input-group-text\">\n          {formData.currency === 'INR' && '₹'}\n          {formData.currency === 'SGD' && 'S$'}\n          {formData.currency === 'USD' && '$'}\n          {formData.currency === 'EUR' && '€'}\n          {formData.currency === 'GBP' && '£'}\n        </span>\n        <input\n          type=\"number\"\n          className=\"form-control\"\n          placeholder=\"Enter course price\"\n          value={formData.price}\n          onChange={(e) =>\n            setFormData((prev) => ({\n              ...prev,\n              price: e.target.value,\n            }))\n          }\n          required\n        />\n      </div>\n    </div>\n  </>\n)}\n\n\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderStepContent = () => {\n    switch (currentStep) {\n      case 1:\n        return renderCourseInformationForm();\n      case 2:\n        return renderCourseMediaForm();\n      case 3:\n        return renderAdditionalInformationForm();\n      case 4:\n        return renderPricingStep();\n      case 5:\n        return renderPreviewStep();\n      default:\n        return null;\n    }\n  };\n\n  // Loading overlay component for submission\n  const SubmissionLoadingOverlay = () => {\n    const isEditMode = courseId && decodedCourseId;\n    return (\n      <div className=\"position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center\"\n           style={{\n             backgroundColor: 'rgba(0, 0, 0, 0.7)',\n             zIndex: 9999\n           }}>\n        <div className=\"text-center text-white\">\n          <div className=\"spinner-border text-primary mb-3\" role=\"status\" style={{ width: '3rem', height: '3rem' }}>\n            <span className=\"visually-hidden\">Loading...</span>\n          </div>\n          <h5 className=\"mb-2\">{isEditMode ? 'Updating Your Course...' : 'Creating Your Course...'}</h5>\n          <p className=\"mb-0\">Please wait while we process your course information.</p>\n        </div>\n      </div>\n    );\n  };\n\n  // Loading overlay component for fetching course details\n  const CourseLoadingOverlay = () => (\n    <div className=\"position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center\"\n         style={{\n           backgroundColor: 'rgba(0, 0, 0, 0.7)',\n           zIndex: 9999\n         }}>\n      <div className=\"text-center text-white\">\n        <div className=\"spinner-border text-primary mb-3\" role=\"status\" style={{ width: '3rem', height: '3rem' }}>\n          <span className=\"visually-hidden\">Loading...</span>\n        </div>\n        <h5 className=\"mb-2\">Loading Course Details...</h5>\n        <p className=\"mb-0\">Please wait while we fetch the course information.</p>\n      </div>\n    </div>\n  );\n\n  return (\n    <div className=\"container\">\n      {/* Loading Overlays */}\n      {isSubmitting && <SubmissionLoadingOverlay />}\n      {isLoadingCourse && <CourseLoadingOverlay />}\n\n      {/* Steps Header */}\n      <div className=\"stepper-card\">\n        <div className=\"stepper-wrapper\">\n          {/* Background Line */}\n          <div className=\"stepper-line-bg\"></div>\n          {/* Progress Line */}\n          <div \n            className=\"stepper-line-progress\"\n            style={{ \n              width: `${((currentStep - 1) / (steps.length - 1)) * 100}%`\n            }}\n          ></div>\n          \n          {/* Step Indicators */}\n          <div className=\"stepper-content\">\n            {steps.map((step, index) => (\n              <div \n                key={step.number} \n                className=\"stepper-item\"\n              >\n                <div \n                  className={`stepper-circle ${\n                    currentStep > index + 1 ? 'completed' :\n                    currentStep === index + 1 ? 'active' : ''\n                  }`}\n                >\n                  {step.number}\n                </div>\n                <div className={`stepper-label ${currentStep === index + 1 ? 'active' : ''}`}>\n                  {step.title}\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Content Area */}\n      <div className=\"stepper-card\">\n        {renderStepContent()}\n      </div>\n\n      {/* Navigation Buttons */}\n      <div className=\"stepper-card\">\n        <div className=\"stepper-content\">\n          <button\n            className=\"btn-previous\"\n            onClick={handlePrevious}\n            disabled={currentStep === 1 || isSubmitting}\n          >\n            <Icon icon=\"fluent:arrow-left-24-regular\" width=\"20\" height=\"20\" />\n            Previous\n          </button>\n          {currentStep === steps.length ? (\n            <button\n              className=\"btn-next\"\n              onClick={handleSubmit}\n              disabled={!isCurrentStepValid() || isSubmitting}\n            >\n              {isSubmitting ? (\n                <>\n                  <div className=\"spinner-border spinner-border-sm me-2\" role=\"status\">\n                    <span className=\"visually-hidden\">Loading...</span>\n                  </div>\n                  {courseId && decodedCourseId ? 'Updating Course...' : 'Creating Course...'}\n                </>\n              ) : (\n                <>\n                  <Icon icon=\"fluent:checkmark-24-regular\" width=\"20\" height=\"20\" className=\"me-2\" />\n                  {courseId && decodedCourseId ? 'Update Course' : 'Submit Course'}\n                </>\n              )}\n            </button>\n          ) : (\n            <button\n              className=\"btn-next\"\n              onClick={handleNext}\n              disabled={!isCurrentStepValid()}\n            >\n              {currentStep === steps.length - 1 ? 'Preview' : 'Next'}\n              <Icon icon=\"fluent:arrow-right-24-regular\" width=\"20\" height=\"20\" />\n            </button>\n          )}\n        </div>\n      </div>\n\n    </div>\n  );\n}\n\nexport default CreateCourse;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,IAAI,QAAQ,gBAAgB;AACrC,SAASC,WAAW,EAAEC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACtE,OAAO,oBAAoB;AAC3B,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,oBAAoB,EAAEC,eAAe,EAAEC,oBAAoB,EAAEC,mBAAmB,QAAQ,gCAAgC;AACjI,OAAOC,oBAAoB,MAAM,sCAAsC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACxE,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,oBAAoB;AAC/CC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEL,MAAM,CAAC;AACvCI,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,OAAOL,MAAM,CAAC;AAC1C;;AAEA,SAASM,YAAYA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,gBAAA;EACtB,MAAMC,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAMwB,QAAQ,GAAGtB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEuB;EAAS,CAAC,GAAGxB,SAAS,CAAC,CAAC;EAChC;EACA,MAAMyB,eAAe,GAAGD,QAAQ,GAAGtB,UAAU,CAACsB,QAAQ,CAAC,GAAG,IAAI;EAC9D;EACA;;EAEA,MAAME,SAAS,IAAAN,eAAA,GAAGE,QAAQ,CAACK,KAAK,cAAAP,eAAA,uBAAdA,eAAA,CAAgBM,SAAS;EAC3C,MAAME,iBAAiB,IAAAP,gBAAA,GAAGC,QAAQ,CAACK,KAAK,cAAAN,gBAAA,uBAAdA,gBAAA,CAAgBQ,UAAU;EAEpD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACqC,YAAY,EAAEC,eAAe,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACuC,eAAe,EAAEC,kBAAkB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACyC,QAAQ,EAAEC,WAAW,CAAC,GAAG1C,QAAQ,CAAC;IACvC2C,WAAW,EAAE,EAAE;IACfC,iBAAiB,EAAE,EAAE;IACrBC,WAAW,EAAE,cAAc;IAC3BC,cAAc,EAAE,SAAS;IACzBC,cAAc,EAAE,EAAE;IAClBC,SAAS,EAAE,IAAI;IACfC,gBAAgB,EAAE,EAAE;IACpBC,eAAe,EAAE,EAAE;IACnBC,UAAU,EAAE,MAAM;IAClBC,QAAQ,EAAEnC,MAAM,KAAK,YAAY,GAAE,KAAK,GAAG,KAAK;IAChDoC,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,EAAE;IACRC,UAAU,EAAE;EACd,CAAC,CAAC;EAEFtD,SAAS,CAAC,MAAM;IACd,IAAI8B,SAAS,IAAIE,iBAAiB,EAAE;MAClCS,WAAW,CAACT,iBAAiB,CAAC;IAChC;EACF,CAAC,EAAE,CAACF,SAAS,EAAEE,iBAAiB,CAAC,CAAC;;EAElC;EACAhC,SAAS,CAAC,MAAM;IACd,MAAMuD,kBAAkB,GAAG,MAAAA,CAAA,KAAY;MACrC,IAAI3B,QAAQ,IAAIC,eAAe,EAAE;QAC/BU,kBAAkB,CAAC,IAAI,CAAC;QACxB,IAAI;UACFnB,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEQ,eAAe,CAAC;UAC/D,MAAM2B,QAAQ,GAAG,MAAM/C,oBAAoB,CAAC;YAAEgD,SAAS,EAAE5B;UAAgB,CAAC,CAAC;UAE3ET,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEmC,QAAQ,CAAC;UAErD,IAAIA,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,CAACC,MAAM,EAAE;YAAA,IAAAC,qBAAA;YAC5C,MAAMD,MAAM,GAAGJ,QAAQ,CAACG,IAAI,CAACC,MAAM;YACnCxC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEuC,MAAM,CAAC;;YAE9C;YACAnB,WAAW,CAAC;cACVC,WAAW,EAAEkB,MAAM,CAACE,WAAW,IAAI,EAAE;cACrCnB,iBAAiB,EAAEiB,MAAM,CAACG,WAAW,IAAI,EAAE;cAC3CnB,WAAW,EAAEgB,MAAM,CAACI,MAAM,IAAI,EAAE;cAChCnB,cAAc,EAAEe,MAAM,CAACK,eAAe,IAAI,EAAE;cAC5CnB,cAAc,EAAEc,MAAM,CAACM,eAAe,IAAI,EAAE;cAC5CnB,SAAS,EAAE,IAAI;cAAE;cACjBC,gBAAgB,EAAEY,MAAM,CAACO,YAAY,IAAI,EAAE;cAC3ClB,eAAe,EAAE,EAAAY,qBAAA,GAAAD,MAAM,CAACQ,uBAAuB,cAAAP,qBAAA,uBAA9BA,qBAAA,CAAgCQ,QAAQ,CAAC,CAAC,KAAI,EAAE;cACjEnB,UAAU,EAAEU,MAAM,CAACU,WAAW,IAAI,MAAM;cACxCnB,QAAQ,EAAES,MAAM,CAACT,QAAQ,IAAI,KAAK;cAClCC,KAAK,EAAEQ,MAAM,CAACW,YAAY,IAAI,EAAE;cAChClB,IAAI,EAAEmB,KAAK,CAACC,OAAO,CAACb,MAAM,CAACP,IAAI,CAAC,GAAGO,MAAM,CAACP,IAAI,GAAG,EAAE;cACnDC,UAAU,EAAEkB,KAAK,CAACC,OAAO,CAACb,MAAM,CAACc,WAAW,CAAC,GAAGd,MAAM,CAACc,WAAW,GAAG;YACvE,CAAC,CAAC;YAEFtD,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;UAC7C,CAAC,MAAM;YAAA,IAAAsD,cAAA,EAAAC,eAAA;YACLxD,OAAO,CAACyD,KAAK,CAAC,iCAAiC,GAAAF,cAAA,GAAEnB,QAAQ,CAACG,IAAI,cAAAgB,cAAA,uBAAbA,cAAA,CAAeG,SAAS,CAAC;YAC1EC,KAAK,CAAC,iCAAiC,IAAI,EAAAH,eAAA,GAAApB,QAAQ,CAACG,IAAI,cAAAiB,eAAA,uBAAbA,eAAA,CAAeE,SAAS,KAAI,eAAe,CAAC,CAAC;UAC1F;QACF,CAAC,CAAC,OAAOD,KAAK,EAAE;UACdzD,OAAO,CAACyD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;UACtDE,KAAK,CAAC,iDAAiD,CAAC;QAC1D,CAAC,SAAS;UACRxC,kBAAkB,CAAC,KAAK,CAAC;QAC3B;MACF;IACF,CAAC;IAEDgB,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAAC3B,QAAQ,EAAEC,eAAe,CAAC,CAAC;EAE/B,MAAM,CAACmD,eAAe,EAAEC,kBAAkB,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC;EAE1D,MAAMmF,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,MAAM1B,QAAQ,GAAG,MAAMjD,oBAAoB,CAAC,CAAC;MAC7C;MACA,IAAIiD,QAAQ,CAACE,OAAO,EAAE;QACpBuB,kBAAkB,CAACzB,QAAQ,CAACG,IAAI,CAACwB,SAAS,CAAC;MAC7C,CAAC,MAAM;QACL;QACAF,kBAAkB,CAAC,EAAE,CAAC,CAAC,CAAC;MAC1B;IACF,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdzD,OAAO,CAACyD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxDI,kBAAkB,CAAC,EAAE,CAAC,CAAC,CAAC;IAC1B;EACF,CAAC;EAEDjF,SAAS,CAAC,MAAM;IACdkF,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM,CAACE,QAAQ,EAAEC,WAAW,CAAC,GAAGtF,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACuF,SAAS,EAAEC,YAAY,CAAC,GAAGxF,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACyF,WAAW,EAAEC,cAAc,CAAC,GAAG1F,QAAQ,CAAC,IAAI,CAAC;;EAEpD;EACA,MAAM2F,oBAAoB,GAAGzF,WAAW,CAAE0F,CAAC,IAAK;IAC9CN,WAAW,CAACM,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC7B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,qBAAqB,GAAG7F,WAAW,CAAE0F,CAAC,IAAK;IAC/CJ,YAAY,CAACI,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC9B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAME,iBAAiB,GAAG9F,WAAW,CAAE0F,CAAC,IAAK;IAC3C,MAAM;MAAEK,IAAI;MAAEH;IAAM,CAAC,GAAGF,CAAC,CAACC,MAAM;IAChCnD,WAAW,CAACwD,SAAS,KAAK;MACxB,GAAGA,SAAS;MACZ,CAACD,IAAI,GAAGH;IACV,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,qBAAqB,GAAIP,CAAC,IAAK;IACnC,MAAMQ,IAAI,GAAGR,CAAC,CAACC,MAAM,CAACQ,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAID,IAAI,EAAE;MACR1D,WAAW,CAACwD,SAAS,KAAK;QACxB,GAAGA,SAAS;QACZlD,SAAS,EAAEoD,IAAI;QACfnD,gBAAgB,EAAEqD,GAAG,CAACC,eAAe,CAACH,IAAI;MAC5C,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMI,gBAAgB,GAAIC,IAAI,IAAK;IACjC,IAAIA,IAAI,KAAK,WAAW,EAAE;MACxB/D,WAAW,CAACwD,SAAS,KAAK;QACxB,GAAGA,SAAS;QACZlD,SAAS,EAAE,IAAI;QACfC,gBAAgB,EAAE;MACpB,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMyD,YAAY,GAAGxG,WAAW,CAAE0F,CAAC,IAAK;IACtCA,CAAC,CAACe,cAAc,CAAC,CAAC;IAClB,MAAMC,GAAG,GAAGvB,QAAQ,CAACwB,IAAI,CAAC,CAAC;IAC3B,IAAID,GAAG,IAAI,CAACnE,QAAQ,CAACa,IAAI,CAACwD,QAAQ,CAACF,GAAG,CAAC,EAAE;MACvC,IAAIA,GAAG,CAACG,MAAM,GAAG,CAAC,EAAE;QAClB/B,KAAK,CAAC,wCAAwC,CAAC;QAC/C;MACF;MACA,IAAIvC,QAAQ,CAACa,IAAI,CAACyD,MAAM,IAAI,CAAC,EAAE;QAC7B/B,KAAK,CAAC,wBAAwB,CAAC;QAC/B;MACF;MACAtC,WAAW,CAACsE,IAAI,KAAK;QACnB,GAAGA,IAAI;QACP1D,IAAI,EAAE,CAAC,GAAG0D,IAAI,CAAC1D,IAAI,EAAEsD,GAAG;MAC1B,CAAC,CAAC,CAAC;MACHtB,WAAW,CAAC,EAAE,CAAC;IACjB;EACF,CAAC,EAAE,CAACD,QAAQ,EAAE5C,QAAQ,CAACa,IAAI,CAAC,CAAC;EAE7B,MAAM2D,eAAe,GAAIC,WAAW,IAAK;IACvCxE,WAAW,CAACsE,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP1D,IAAI,EAAE0D,IAAI,CAAC1D,IAAI,CAAC6D,MAAM,CAACP,GAAG,IAAIA,GAAG,KAAKM,WAAW;IACnD,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,qBAAqB,GAAIxB,CAAC,IAAK;IACnC,IAAIA,CAAC,CAACyB,GAAG,KAAK,OAAO,EAAE;MACrBzB,CAAC,CAACe,cAAc,CAAC,CAAC;MAClBD,YAAY,CAACd,CAAC,CAAC;IACjB;EACF,CAAC;EAED,MAAM0B,aAAa,GAAGpH,WAAW,CAAE0F,CAAC,IAAK;IACvCA,CAAC,CAACe,cAAc,CAAC,CAAC;IAClB,IAAIpB,SAAS,CAACsB,IAAI,CAAC,CAAC,EAAE;MACpB,IAAIpB,WAAW,KAAK,IAAI,EAAE;QACxB/C,WAAW,CAACsE,IAAI,KAAK;UACnB,GAAGA,IAAI;UACPzD,UAAU,EAAEyD,IAAI,CAACzD,UAAU,CAACgE,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAC1CA,KAAK,KAAKhC,WAAW,GAAGF,SAAS,CAACsB,IAAI,CAAC,CAAC,GAAGW,IAC7C;QACF,CAAC,CAAC,CAAC;QACH9B,cAAc,CAAC,IAAI,CAAC;MACtB,CAAC,MAAM;QACLhD,WAAW,CAACsE,IAAI,KAAK;UACnB,GAAGA,IAAI;UACPzD,UAAU,EAAE,CAAC,GAAGyD,IAAI,CAACzD,UAAU,EAAEgC,SAAS,CAACsB,IAAI,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;MACL;MACArB,YAAY,CAAC,EAAE,CAAC;IAClB;EACF,CAAC,EAAE,CAACD,SAAS,EAAEE,WAAW,CAAC,CAAC;EAE5B,MAAMiC,cAAc,GAAID,KAAK,IAAK;IAChCjC,YAAY,CAAC/C,QAAQ,CAACc,UAAU,CAACkE,KAAK,CAAC,CAAC;IACxC/B,cAAc,CAAC+B,KAAK,CAAC;EACvB,CAAC;EAED,MAAME,gBAAgB,GAAIF,KAAK,IAAK;IAClC/E,WAAW,CAACsE,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPzD,UAAU,EAAEyD,IAAI,CAACzD,UAAU,CAAC4D,MAAM,CAAC,CAACS,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKJ,KAAK;IAC1D,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMK,sBAAsB,GAAIlC,CAAC,IAAK;IACpC,IAAIA,CAAC,CAACyB,GAAG,KAAK,OAAO,EAAE;MACrBzB,CAAC,CAACe,cAAc,CAAC,CAAC;MAClBW,aAAa,CAAC1B,CAAC,CAAC;IAClB;EACF,CAAC;EAED,MAAMmC,KAAK,GAAG,CACZ;IAAEC,MAAM,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAqB,CAAC,EAC7C;IAAED,MAAM,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAe,CAAC,EACvC;IAAED,MAAM,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAyB,CAAC,EACjD;IAAED,MAAM,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAU,CAAC,EAClC;IAAED,MAAM,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAmB,CAAC,CAC5C;;EAED;EACA,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B,OAAOzF,QAAQ,CAACE,WAAW,CAACkE,IAAI,CAAC,CAAC,KAAK,EAAE,IAClCpE,QAAQ,CAACG,iBAAiB,CAACiE,IAAI,CAAC,CAAC,KAAK,EAAE,IACxCpE,QAAQ,CAACI,WAAW,KAAK,EAAE,IAC3BJ,QAAQ,CAACK,cAAc,KAAK,EAAE,IAC9BL,QAAQ,CAACM,cAAc,KAAK,EAAE;EACvC,CAAC;EAED,MAAMoF,aAAa,GAAGA,CAAA,KAAM;IAC1B;IACA,IAAItG,QAAQ,IAAIC,eAAe,EAAE;MAC/B,OAAO,CAACW,QAAQ,CAACO,SAAS,KAAK,IAAI,IAAIP,QAAQ,CAACQ,gBAAgB,KAAK,EAAE,KAChER,QAAQ,CAACS,eAAe,KAAK,EAAE;IACxC;IACA;IACA,OAAOT,QAAQ,CAACO,SAAS,KAAK,IAAI,IAAIP,QAAQ,CAACS,eAAe,KAAK,EAAE;EACvE,CAAC;EAED,MAAMkF,aAAa,GAAGA,CAAA,KAAM;IAC1B;IACA,IAAIvG,QAAQ,IAAIC,eAAe,EAAE;MAC/B,OAAO,IAAI,CAAC,CAAC;IACf;IACA;IACA,OAAOW,QAAQ,CAACa,IAAI,CAACyD,MAAM,GAAG,CAAC,IAAItE,QAAQ,CAACc,UAAU,CAACwD,MAAM,GAAG,CAAC;EACnE,CAAC;EAED,MAAMsB,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI5F,QAAQ,CAACU,UAAU,KAAK,MAAM,EAAE;MAClC,OAAO,IAAI;IACb;IACA,OAAOV,QAAQ,CAACY,KAAK,KAAK,EAAE,IAAIiF,UAAU,CAAC7F,QAAQ,CAACY,KAAK,CAAC,GAAG,CAAC;EAChE,CAAC;EAED,MAAMkF,aAAa,GAAGA,CAAA,KAAM;IAC1B;IACA,OAAOL,aAAa,CAAC,CAAC,IAAIC,aAAa,CAAC,CAAC,IAAIC,aAAa,CAAC,CAAC,IAAIC,aAAa,CAAC,CAAC;EACjF,CAAC;EAED,MAAMG,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,QAAQrG,WAAW;MACjB,KAAK,CAAC;QACJ,OAAO+F,aAAa,CAAC,CAAC;MACxB,KAAK,CAAC;QACJ,OAAOC,aAAa,CAAC,CAAC;MACxB,KAAK,CAAC;QACJ,OAAOC,aAAa,CAAC,CAAC;MACxB,KAAK,CAAC;QACJ,OAAOC,aAAa,CAAC,CAAC;MACxB,KAAK,CAAC;QACJ,OAAOE,aAAa,CAAC,CAAC;MACxB;QACE,OAAO,KAAK;IAChB;EACF,CAAC;EAED,MAAME,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAItG,WAAW,GAAG4F,KAAK,CAAChB,MAAM,EAAE;MAC9B3E,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;IACjC;EACF,CAAC;EAED,MAAMuG,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIvG,WAAW,GAAG,CAAC,EAAE;MACnBC,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;IACjC;EACF,CAAC;EAED,MAAMwG,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAIH,kBAAkB,CAAC,CAAC,EAAE;MACxBlG,eAAe,CAAC,IAAI,CAAC;MAErB,IAAI;QACF;QACA,MAAMsG,cAAc,GAAG,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;QAExE;QACA,MAAME,UAAU,GAAG,IAAIC,QAAQ,CAAC,CAAC;;QAEjC;QACAD,UAAU,CAACE,MAAM,CAAC,aAAa,EAAEzG,QAAQ,CAACE,WAAW,CAAC;QACtDqG,UAAU,CAACE,MAAM,CAAC,mBAAmB,EAAEzG,QAAQ,CAACG,iBAAiB,CAAC;QAClEoG,UAAU,CAACE,MAAM,CAAC,aAAa,EAAEzG,QAAQ,CAACI,WAAW,CAAC;QACtDmG,UAAU,CAACE,MAAM,CAAC,gBAAgB,EAAEzG,QAAQ,CAACK,cAAc,CAAC;QAC5DkG,UAAU,CAACE,MAAM,CAAC,gBAAgB,EAAEzG,QAAQ,CAACM,cAAc,CAAC;QAC5DiG,UAAU,CAACE,MAAM,CAAC,iBAAiB,EAAEzG,QAAQ,CAACS,eAAe,CAAC;QAC9D8F,UAAU,CAACE,MAAM,CAAC,YAAY,EAAEzG,QAAQ,CAACU,UAAU,CAAC;QACpD6F,UAAU,CAACE,MAAM,CAAC,UAAU,EAAEzG,QAAQ,CAACW,QAAQ,CAAC;QAChD4F,UAAU,CAACE,MAAM,CAAC,OAAO,EAAEzG,QAAQ,CAACY,KAAK,CAAC;QAC1C2F,UAAU,CAACE,MAAM,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAAC3G,QAAQ,CAACa,IAAI,CAAC,CAAC;QACxD0F,UAAU,CAACE,MAAM,CAAC,YAAY,EAAEC,IAAI,CAACC,SAAS,CAAC3G,QAAQ,CAACc,UAAU,CAAC,CAAC;QAEpElC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE0H,UAAU,CAAC;;QAEvC;QACA,IAAIvG,QAAQ,CAACO,SAAS,EAAE;UACtBgG,UAAU,CAACE,MAAM,CAAC,cAAc,EAAEzG,QAAQ,CAACO,SAAS,CAAC;QACvD;QAEA3B,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxCD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEO,QAAQ,IAAIC,eAAe,CAAC;;QAE5D;QACA,IAAIuH,OAAO;QACX,IAAIxH,QAAQ,IAAIC,eAAe,EAAE;UAC/B;UACAuH,OAAO,GAAG1I,mBAAmB,CAACmB,eAAe,EAAEkH,UAAU,CAAC;QAC5D,CAAC,MAAM;UACL;UACAK,OAAO,GAAG5I,eAAe,CAACuI,UAAU,CAAC;QACvC;;QAEA;QACA,MAAM,CAACvF,QAAQ,CAAC,GAAG,MAAMoF,OAAO,CAACS,GAAG,CAAC,CAACD,OAAO,EAAET,cAAc,CAAC,CAAC;QAE/D,IAAInF,QAAQ,CAACE,OAAO,EAAE;UACpBtC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEmC,QAAQ,CAACG,IAAI,CAAC;UAC1D;UACAhC,QAAQ,CAAC,gBAAgB,CAAC;QAC5B,CAAC,MAAM;UAAA,IAAA2H,eAAA;UACLjH,eAAe,CAAC,KAAK,CAAC;UACtB,MAAMkH,QAAQ,GAAG3H,QAAQ,IAAIC,eAAe,GAAG,yBAAyB,GAAG,yBAAyB;UACpGkD,KAAK,CAACwE,QAAQ,GAAG,IAAI,IAAI,EAAAD,eAAA,GAAA9F,QAAQ,CAACG,IAAI,cAAA2F,eAAA,uBAAbA,eAAA,CAAexE,SAAS,KAAI,eAAe,CAAC,CAAC;QACxE;MACF,CAAC,CAAC,OAAOD,KAAK,EAAE;QACdxC,eAAe,CAAC,KAAK,CAAC;QACtBjB,OAAO,CAACyD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD,MAAM0E,QAAQ,GAAG3H,QAAQ,IAAIC,eAAe,GAAG,uBAAuB,GAAG,uBAAuB;QAChGkD,KAAK,CAACwE,QAAQ,GAAG,qBAAqB,CAAC;MACzC;IACF,CAAC,MAAM;MACLxE,KAAK,CAAC,uDAAuD,CAAC;IAChE;EACF,CAAC;EAED,MAAMyE,2BAA2B,GAAGA,CAAA,kBAClC3I,OAAA;IAAK4I,SAAS,EAAC,oBAAoB;IAAAC,QAAA,eACjC7I,OAAA;MAAK4I,SAAS,EAAC,KAAK;MAAAC,QAAA,gBAClB7I,OAAA;QAAK4I,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B7I,OAAA;UAAO8I,OAAO,EAAC,aAAa;UAACF,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1ElJ,OAAA;UACE2F,IAAI,EAAC,MAAM;UACXiD,SAAS,EAAC,cAAc;UACxBO,EAAE,EAAC,aAAa;UAChBhE,IAAI,EAAC,aAAa;UAClBiE,WAAW,EAAC,oBAAoB;UAChCpE,KAAK,EAAErD,QAAQ,CAACE,WAAY;UAC5BwH,QAAQ,EAAEnE,iBAAkB;UAC5BoE,SAAS,EAAE,GAAI;UACfC,QAAQ;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFlJ,OAAA;UAAO4I,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAwF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3H,CAAC,eAENlJ,OAAA;QAAK4I,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B7I,OAAA;UAAO8I,OAAO,EAAC,mBAAmB;UAACF,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAoB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACtFlJ,OAAA;UACE4I,SAAS,EAAC,cAAc;UACxBO,EAAE,EAAC,mBAAmB;UACtBhE,IAAI,EAAC,mBAAmB;UACxBqE,IAAI,EAAC,GAAG;UACRJ,WAAW,EAAC,0BAA0B;UACtCpE,KAAK,EAAErD,QAAQ,CAACG,iBAAkB;UAClCuH,QAAQ,EAAEnE,iBAAkB;UAC5BoE,SAAS,EAAE,IAAK;UAChBC,QAAQ;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACZlJ,OAAA;UAAO4I,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAA+B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC,eAENlJ,OAAA;QAAK4I,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B7I,OAAA;UAAO8I,OAAO,EAAC,aAAa;UAACF,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1ElJ,OAAA;UACE4I,SAAS,EAAC,aAAa;UACvBO,EAAE,EAAC,aAAa;UAChBhE,IAAI,EAAC,aAAa;UAClBH,KAAK,EAAErD,QAAQ,CAACI,WAAY;UAC5BsH,QAAQ,EAAEnE,iBAAkB;UAC5BqE,QAAQ;UAAAV,QAAA,gBAER7I,OAAA;YAAQgF,KAAK,EAAC,EAAE;YAAA6D,QAAA,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtClJ,OAAA;YAAQgF,KAAK,EAAC,UAAU;YAAA6D,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1ClJ,OAAA;YAAQgF,KAAK,EAAC,cAAc;YAAA6D,QAAA,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAClDlJ,OAAA;YAAQgF,KAAK,EAAC,UAAU;YAAA6D,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1ClJ,OAAA;YAAQgF,KAAK,EAAC,YAAY;YAAA6D,QAAA,EAAC;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENlJ,OAAA;QAAK4I,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B7I,OAAA;UAAO8I,OAAO,EAAC,gBAAgB;UAACF,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAChFlJ,OAAA;UACE4I,SAAS,EAAC,aAAa;UACvBO,EAAE,EAAC,gBAAgB;UACnBhE,IAAI,EAAC,gBAAgB;UACrBH,KAAK,EAAErD,QAAQ,CAACK,cAAe;UAC/BqH,QAAQ,EAAEnE,iBAAkB;UAC5BqE,QAAQ;UAAAV,QAAA,gBAER7I,OAAA;YAAQgF,KAAK,EAAC,EAAE;YAAA6D,QAAA,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACzClJ,OAAA;YAAQgF,KAAK,EAAC,SAAS;YAAA6D,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxClJ,OAAA;YAAQgF,KAAK,EAAC,SAAS;YAAA6D,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxClJ,OAAA;YAAQgF,KAAK,EAAC,QAAQ;YAAA6D,QAAA,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtClJ,OAAA;YAAQgF,KAAK,EAAC,QAAQ;YAAA6D,QAAA,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtClJ,OAAA;YAAQgF,KAAK,EAAC,SAAS;YAAA6D,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxClJ,OAAA;YAAQgF,KAAK,EAAC,UAAU;YAAA6D,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1ClJ,OAAA;YAAQgF,KAAK,EAAC,OAAO;YAAA6D,QAAA,EAAC;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENlJ,OAAA;QAAK4I,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B7I,OAAA;UAAO8I,OAAO,EAAC,gBAAgB;UAACF,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAChFlJ,OAAA;UACE4I,SAAS,EAAC,aAAa;UACvBO,EAAE,EAAC,gBAAgB;UACnBhE,IAAI,EAAC,gBAAgB;UACrBH,KAAK,EAAErD,QAAQ,CAACM,cAAe;UAC/BoH,QAAQ,EAAEnE,iBAAkB;UAC5BqE,QAAQ;UAAAV,QAAA,gBAER7I,OAAA;YAAQgF,KAAK,EAAC,EAAE;YAAA6D,QAAA,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACxCpJ,oBAAoB,CAAC2J,UAAU,CAAChD,GAAG,CAAC,CAACiD,QAAQ,EAAE/C,KAAK,kBACnD3G,OAAA;YAAoBgF,KAAK,EAAE0E,QAAS;YAAAb,QAAA,EACjCa;UAAQ,GADE/C,KAAK;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEV,CACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMS,qBAAqB,GAAGA,CAAA,kBAC5B3J,OAAA;IAAK4I,SAAS,EAAC,cAAc;IAAAC,QAAA,eAC3B7I,OAAA;MAAK4I,SAAS,EAAC,KAAK;MAAAC,QAAA,gBAElB7I,OAAA;QAAK4I,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B7I,OAAA;UAAO4I,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAkB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxDlJ,OAAA;UAAK4I,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnB7I,OAAA;YAAK4I,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvB,CAAClH,QAAQ,CAACQ,gBAAgB,gBACzBnC,OAAA;cAAK4I,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9B7I,OAAA;gBACE2F,IAAI,EAAC,MAAM;gBACXwD,EAAE,EAAC,WAAW;gBACdP,SAAS,EAAC,QAAQ;gBAClBgB,MAAM,EAAC,SAAS;gBAChBP,QAAQ,EAAEhE;cAAsB;gBAAA0D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACFlJ,OAAA;gBACE8I,OAAO,EAAC,WAAW;gBACnBF,SAAS,EAAC,kFAAkF;gBAC5FiB,KAAK,EAAE;kBAAEC,KAAK,EAAE,aAAa;kBAAEC,MAAM,EAAE;gBAAU,CAAE;gBAAAlB,QAAA,gBAEnD7I,OAAA,CAACX,IAAI;kBAAC2K,IAAI,EAAC,6BAA6B;kBAACF,KAAK,EAAC,IAAI;kBAACG,MAAM,EAAC;gBAAI;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAClElJ,OAAA;kBAAA6I,QAAA,EAAM;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC7BlJ,OAAA;kBAAO4I,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAA4B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,gBAENlJ,OAAA;cAAK4I,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC7I,OAAA;gBACEkK,GAAG,EAAEvI,QAAQ,CAACQ,gBAAiB;gBAC/BgI,GAAG,EAAC,kBAAkB;gBACtBvB,SAAS,EAAC,mBAAmB;gBAC7BiB,KAAK,EAAE;kBAAEC,KAAK,EAAE,MAAM;kBAAEG,MAAM,EAAE;gBAAO;cAAE;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eACFlJ,OAAA;gBACE4I,SAAS,EAAC,uFAAuF;gBACjGwB,OAAO,EAAEA,CAAA,KAAM1E,gBAAgB,CAAC,WAAW,CAAE;gBAC7CC,IAAI,EAAC,QAAQ;gBACbkE,KAAK,EAAE;kBACLC,KAAK,EAAE,MAAM;kBACbG,MAAM,EAAE,MAAM;kBACdI,GAAG,EAAE,KAAK;kBACVC,KAAK,EAAE,KAAK;kBACZC,QAAQ,EAAE,OAAO;kBACjBC,YAAY,EAAE;gBAChB,CAAE;gBAAA3B,QAAA,eAEF7I,OAAA,CAACX,IAAI;kBAAC2K,IAAI,EAAC,0BAA0B;kBAACF,KAAK,EAAC,IAAI;kBAACG,MAAM,EAAC;gBAAI;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNlJ,OAAA;QAAK4I,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5B7I,OAAA;UAAO4I,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAkB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxDlJ,OAAA;UAAK4I,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnB7I,OAAA;YAAK4I,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB7I,OAAA;cACE4I,SAAS,EAAC,kBAAkB;cAC5BzD,IAAI,EAAC,iBAAiB;cACtBH,KAAK,EAAErD,QAAQ,CAACS,eAAgB;cAChCiH,QAAQ,EAAEnE,iBAAkB;cAC5BqE,QAAQ;cAAAV,QAAA,gBAER7I,OAAA;gBAAQgF,KAAK,EAAC,EAAE;gBAAA6D,QAAA,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAChDvF,KAAK,CAACC,OAAO,CAACO,eAAe,CAAC,IAAIA,eAAe,CAACsC,GAAG,CAAEgE,IAAI,iBAC1DzK,OAAA;gBAAkCgF,KAAK,EAAEyF,IAAI,CAACtB,EAAE,IAAIsB,IAAI,CAACC,GAAI;gBAAA7B,QAAA,EAC1D4B,IAAI,CAACE,aAAa,IAAI;cAAqB,GADjCF,IAAI,CAACtB,EAAE,IAAIsB,IAAI,CAACC,GAAG;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAExB,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,EAERvH,QAAQ,CAACS,eAAe,iBACvBpC,OAAA;cAAK4I,SAAS,EAAC,0CAA0C;cAAAC,QAAA,eACvD7I,OAAA;gBAAK4I,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,gBAC9C7I,OAAA,CAACX,IAAI;kBAAC2K,IAAI,EAAC,+BAA+B;kBAACF,KAAK,EAAC,IAAI;kBAACG,MAAM,EAAC,IAAI;kBAACrB,SAAS,EAAC;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC7FlJ,OAAA;kBAAA6I,QAAA,gBACE7I,OAAA;oBAAI4I,SAAS,EAAC,MAAM;oBAAAC,QAAA,GAAElH,QAAQ,CAACS,eAAe,CAACwI,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGlJ,QAAQ,CAACS,eAAe,CAAC0I,KAAK,CAAC,CAAC,CAAC,EAAC,cAAY;kBAAA;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5HlJ,OAAA;oBAAO4I,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAqC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAM6B,+BAA+B,GAAGA,CAAA,kBACtC/K,OAAA;IAAK4I,SAAS,EAAC,KAAK;IAAAC,QAAA,eAClB7I,OAAA;MAAK4I,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1B7I,OAAA;QAAK4I,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnB7I,OAAA;UAAK4I,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB7I,OAAA;YAAK4I,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnB7I,OAAA;cAAO4I,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnDlJ,OAAA;cAAK4I,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB7I,OAAA;gBAAK4I,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBACtB7I,OAAA;kBAAK4I,SAAS,EAAC,QAAQ;kBAAAC,QAAA,eACrB7I,OAAA;oBACE2F,IAAI,EAAC,MAAM;oBACXiD,SAAS,EAAC,sCAAsC;oBAChDQ,WAAW,EAAC,mDAAmD;oBAC/DpE,KAAK,EAAET,QAAS;oBAChB8E,QAAQ,EAAExE,oBAAqB;oBAC/BmG,SAAS,EAAE1E;kBAAsB;oBAAAyC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNlJ,OAAA;kBAAK4I,SAAS,EAAC,OAAO;kBAAAC,QAAA,eACpB7I,OAAA;oBACE4I,SAAS,EAAC,6CAA6C;oBACvDwB,OAAO,EAAExE,YAAa;oBAAAiD,QAAA,EACvB;kBAED;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlJ,OAAA;gBAAK4I,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnB7I,OAAA;kBAAO4I,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAC;gBAAmC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtFlJ,OAAA;kBAAO4I,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,GAAC,qCAEpC,eAAA7I,OAAA;oBAAA+I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,iCAEN,eAAAlJ,OAAA;oBAAA+I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,WACJ,EAAC,CAAC,GAAGvH,QAAQ,CAACa,IAAI,CAACyD,MAAM,EAAC,iBAC9B;gBAAA;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlJ,OAAA;cAAK4I,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EACzClH,QAAQ,CAACa,IAAI,CAACiE,GAAG,CAAC,CAACX,GAAG,EAAEa,KAAK,kBAC5B3G,OAAA;gBAEE4I,SAAS,EAAC,2EAA2E;gBAAAC,QAAA,GAEpF/C,GAAG,eACJ9F,OAAA;kBACE2F,IAAI,EAAC,QAAQ;kBACbiD,SAAS,EAAC,wDAAwD;kBAClEwB,OAAO,EAAEA,CAAA,KAAMjE,eAAe,CAACL,GAAG,CAAE;kBACpC+D,KAAK,EAAE;oBAAEU,QAAQ,EAAE,MAAM;oBAAEN,MAAM,EAAE;kBAAO,CAAE;kBAAApB,QAAA,eAE5C7I,OAAA,CAACX,IAAI;oBAAC2K,IAAI,EAAC,2BAA2B;oBAACF,KAAK,EAAC,IAAI;oBAACG,MAAM,EAAC;kBAAI;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CAAC;cAAA,GAXJvC,KAAK;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAYP,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlJ,OAAA;YAAK4I,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnB7I,OAAA;cAAO4I,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnDlJ,OAAA;cAAK4I,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB7I,OAAA;gBAAK4I,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBACtB7I,OAAA;kBAAK4I,SAAS,EAAC,QAAQ;kBAAAC,QAAA,eACrB7I,OAAA;oBACE2F,IAAI,EAAC,MAAM;oBACXiD,SAAS,EAAC,sCAAsC;oBAChDQ,WAAW,EAAC,4EAA4E;oBACxFpE,KAAK,EAAEP,SAAU;oBACjB4E,QAAQ,EAAEpE,qBAAsB;oBAChC+F,SAAS,EAAEhE;kBAAuB;oBAAA+B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNlJ,OAAA;kBAAK4I,SAAS,EAAC,OAAO;kBAAAC,QAAA,eACpB7I,OAAA;oBACE4I,SAAS,EAAC,6CAA6C;oBACvDwB,OAAO,EAAE5D,aAAc;oBAAAqC,QAAA,EAEtBlE,WAAW,KAAK,IAAI,GAAG,QAAQ,GAAG;kBAAU;oBAAAoE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlJ,OAAA;gBAAK4I,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnB7I,OAAA;kBAAO4I,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAC;gBAEtC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENlJ,OAAA;cAAK4I,SAAS,EAAC,MAAM;cAAAC,QAAA,EAClBlH,QAAQ,CAACc,UAAU,CAACgE,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACnC3G,OAAA;gBAEE4I,SAAS,EAAC,yDAAyD;gBAAAC,QAAA,gBAEnE7I,OAAA,CAACX,IAAI;kBACH2K,IAAI,EAAC,wBAAwB;kBAC7BpB,SAAS,EAAC,4BAA4B;kBACtCkB,KAAK,EAAC,IAAI;kBACVG,MAAM,EAAC;gBAAI;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACFlJ,OAAA;kBAAM4I,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAEnC;gBAAI;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3ClJ,OAAA;kBAAK4I,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3B7I,OAAA;oBACE2F,IAAI,EAAC,QAAQ;oBACbiD,SAAS,EAAC,yDAAyD;oBACnEwB,OAAO,EAAEA,CAAA,KAAMxD,cAAc,CAACD,KAAK,CAAE;oBACrCkD,KAAK,EAAE;sBAAEU,QAAQ,EAAE,MAAM;sBAAEN,MAAM,EAAE;oBAAO,CAAE;oBAAApB,QAAA,eAE5C7I,OAAA,CAACX,IAAI;sBAAC2K,IAAI,EAAC,wBAAwB;sBAACF,KAAK,EAAC,IAAI;sBAACG,MAAM,EAAC;oBAAI;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD,CAAC,eACTlJ,OAAA;oBACE2F,IAAI,EAAC,QAAQ;oBACbiD,SAAS,EAAC,wDAAwD;oBAClEwB,OAAO,EAAEA,CAAA,KAAMvD,gBAAgB,CAACF,KAAK,CAAE;oBACvCkD,KAAK,EAAE;sBAAEU,QAAQ,EAAE,MAAM;sBAAEN,MAAM,EAAE;oBAAO,CAAE;oBAAApB,QAAA,eAE5C7I,OAAA,CAACX,IAAI;sBAAC2K,IAAI,EAAC,0BAA0B;sBAACF,KAAK,EAAC,IAAI;sBAACG,MAAM,EAAC;oBAAI;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA,GA3BDvC,KAAK;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA4BP,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAM+B,iBAAiB,GAAGA,CAAA,kBACxBjL,OAAA;IAAK4I,SAAS,EAAC,KAAK;IAAAC,QAAA,eAClB7I,OAAA;MAAK4I,SAAS,EAAC,QAAQ;MAAAC,QAAA,eACrB7I,OAAA;QAAK4I,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB7I,OAAA;UAAK4I,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B7I,OAAA;YAAI4I,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAClB7I,OAAA,CAACX,IAAI;cAAC2K,IAAI,EAAC,uBAAuB;cAACF,KAAK,EAAC,IAAI;cAACG,MAAM,EAAC,IAAI;cAACrB,SAAS,EAAC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,kBAE/E;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLlJ,OAAA;YAAG4I,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAC;UAEpC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNlJ,OAAA;UAAK4I,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAExB7I,OAAA;YAAK4I,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnB7I,OAAA;cAAI4I,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC/B7I,OAAA,CAACX,IAAI;gBAAC2K,IAAI,EAAC,wBAAwB;gBAACF,KAAK,EAAC,IAAI;gBAACG,MAAM,EAAC,IAAI;gBAACrB,SAAS,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,sBAEhF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLlJ,OAAA;cAAK4I,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClB7I,OAAA;gBAAK4I,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5B7I,OAAA;kBAAO4I,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7DlJ,OAAA;kBAAG4I,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAElH,QAAQ,CAACE,WAAW,IAAI;gBAAe;kBAAAkH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE,CAAC,eACNlJ,OAAA;gBAAK4I,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5B7I,OAAA;kBAAO4I,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAChElJ,OAAA;kBAAG4I,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAElH,QAAQ,CAACM,cAAc,IAAI;gBAAe;kBAAA8G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC,eACNlJ,OAAA;gBAAK4I,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1B7I,OAAA;kBAAO4I,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnElJ,OAAA;kBAAG4I,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAElH,QAAQ,CAACG,iBAAiB,IAAI;gBAAe;kBAAAiH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC,eACNlJ,OAAA;gBAAK4I,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5B7I,OAAA;kBAAO4I,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtDlJ,OAAA;kBAAG4I,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,EAAElH,QAAQ,CAACI,WAAW,IAAI;gBAAe;kBAAAgH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC,eACNlJ,OAAA;gBAAK4I,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5B7I,OAAA;kBAAO4I,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzDlJ,OAAA;kBAAG4I,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,EAAElH,QAAQ,CAACK,cAAc,IAAI;gBAAe;kBAAA+G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF,CAAC,eACNlJ,OAAA;gBAAK4I,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5B7I,OAAA;kBAAO4I,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5DlJ,OAAA;kBAAG4I,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAElH,QAAQ,CAACS,eAAe,GAAG,mBAAmBT,QAAQ,CAACS,eAAe,EAAE,GAAG;gBAAe;kBAAA2G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlJ,OAAA;YAAA+I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAGNlJ,OAAA;YAAK4I,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnB7I,OAAA;cAAI4I,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC/B7I,OAAA,CAACX,IAAI;gBAAC2K,IAAI,EAAC,yBAAyB;gBAACF,KAAK,EAAC,IAAI;gBAACG,MAAM,EAAC,IAAI;gBAACrB,SAAS,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEjF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLlJ,OAAA;cAAK4I,SAAS,EAAC,KAAK;cAAAC,QAAA,eAClB7I,OAAA;gBAAK4I,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5B7I,OAAA;kBAAO4I,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EAChEvH,QAAQ,CAACQ,gBAAgB,gBACxBnC,OAAA;kBAAA6I,QAAA,eACE7I,OAAA;oBACEkK,GAAG,EAAEvI,QAAQ,CAACQ,gBAAiB;oBAC/BgI,GAAG,EAAC,kBAAkB;oBACtBvB,SAAS,EAAC,0BAA0B;oBACpCiB,KAAK,EAAE;sBAAEqB,SAAS,EAAE,OAAO;sBAAEpB,KAAK,EAAE;oBAAO;kBAAE;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,gBAENlJ,OAAA;kBAAG4I,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CACxD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlJ,OAAA;YAAA+I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAGNlJ,OAAA;YAAK4I,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnB7I,OAAA;cAAI4I,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC/B7I,OAAA,CAACX,IAAI;gBAAC2K,IAAI,EAAC,uBAAuB;gBAACF,KAAK,EAAC,IAAI;gBAACG,MAAM,EAAC,IAAI;gBAACrB,SAAS,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,0BAE/E;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLlJ,OAAA;cAAK4I,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClB7I,OAAA;gBAAK4I,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5B7I,OAAA;kBAAO4I,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EACpDvH,QAAQ,CAACa,IAAI,CAACyD,MAAM,GAAG,CAAC,gBACvBjG,OAAA;kBAAK4I,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,EACpClH,QAAQ,CAACa,IAAI,CAACiE,GAAG,CAAC,CAACX,GAAG,EAAEa,KAAK,kBAC5B3G,OAAA;oBAAkB4I,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAC1D/C;kBAAG,GADKa,KAAK;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEV,CACP;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,gBAENlJ,OAAA;kBAAG4I,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAChD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNlJ,OAAA;gBAAK4I,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5B7I,OAAA;kBAAO4I,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAyB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EACzEvH,QAAQ,CAACc,UAAU,CAACwD,MAAM,GAAG,CAAC,gBAC7BjG,OAAA;kBAAI4I,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAC/BlH,QAAQ,CAACc,UAAU,CAACgE,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACnC3G,OAAA;oBAAgB4I,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBAC9B7I,OAAA,CAACX,IAAI;sBAAC2K,IAAI,EAAC,oCAAoC;sBAACF,KAAK,EAAC,IAAI;sBAACG,MAAM,EAAC,IAAI;sBAACrB,SAAS,EAAC;oBAAmB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACtGxC,IAAI;kBAAA,GAFEC,KAAK;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAGV,CACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,gBAELlJ,OAAA;kBAAG4I,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAA2B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAC9D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlJ,OAAA;YAAA+I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAGNlJ,OAAA;YAAK4I,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnB7I,OAAA;cAAI4I,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC/B7I,OAAA,CAACX,IAAI;gBAAC2K,IAAI,EAAC,2BAA2B;gBAACF,KAAK,EAAC,IAAI;gBAACG,MAAM,EAAC,IAAI;gBAACrB,SAAS,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,uBAEnF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLlJ,OAAA;cAAK4I,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClB7I,OAAA;gBAAK4I,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5B7I,OAAA;kBAAO4I,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5DlJ,OAAA;kBAAG4I,SAAS,EAAC,MAAM;kBAAAC,QAAA,eACjB7I,OAAA;oBAAM4I,SAAS,EAAE,SAASjH,QAAQ,CAACU,UAAU,KAAK,MAAM,GAAG,YAAY,GAAG,YAAY,EAAG;oBAAAwG,QAAA,EACtFlH,QAAQ,CAACU,UAAU,KAAK,MAAM,GAAG,aAAa,GAAG;kBAAa;oBAAA0G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,EACLvH,QAAQ,CAACU,UAAU,KAAK,MAAM,iBAC7BrC,OAAA,CAAAE,SAAA;gBAAA2I,QAAA,gBACE7I,OAAA;kBAAK4I,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5B7I,OAAA;oBAAO4I,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACzDlJ,OAAA;oBAAG4I,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAElH,QAAQ,CAACW;kBAAQ;oBAAAyG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACNlJ,OAAA;kBAAK4I,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5B7I,OAAA;oBAAO4I,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtDlJ,OAAA;oBAAG4I,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,GAC1BlH,QAAQ,CAACW,QAAQ,KAAK,KAAK,IAAI,GAAG,EAClCX,QAAQ,CAACW,QAAQ,KAAK,KAAK,IAAI,GAAG,EAClCX,QAAQ,CAACW,QAAQ,KAAK,KAAK,IAAI,GAAG,EAClCX,QAAQ,CAACW,QAAQ,KAAK,KAAK,IAAI,GAAG,EAClCX,QAAQ,CAACW,QAAQ,KAAK,KAAK,IAAI,IAAI,EACnCX,QAAQ,CAACY,KAAK;kBAAA;oBAAAwG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA,eACN,CACH;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMiC,iBAAiB,GAAGA,CAAA,kBACxBnL,OAAA;IAAK4I,SAAS,EAAC,KAAK;IAAAC,QAAA,eAClB7I,OAAA;MAAK4I,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1B7I,OAAA;QAAK4I,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnB7I,OAAA;UAAK4I,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAExB7I,OAAA;YAAK4I,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnB7I,OAAA;cAAO4I,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnDlJ,OAAA;cAAK4I,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3B7I,OAAA;gBAAK4I,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB7I,OAAA;kBACE2F,IAAI,EAAC,OAAO;kBACZiD,SAAS,EAAC,kBAAkB;kBAC5BO,EAAE,EAAC,MAAM;kBACThE,IAAI,EAAC,YAAY;kBACjBH,KAAK,EAAC,MAAM;kBACZoG,OAAO,EAAEzJ,QAAQ,CAACU,UAAU,KAAK,MAAO;kBACxCgH,QAAQ,EAAGvE,CAAC,IAAKlD,WAAW,CAACsE,IAAI,KAAK;oBACpC,GAAGA,IAAI;oBACP7D,UAAU,EAAEyC,CAAC,CAACC,MAAM,CAACC,KAAK;oBAC1BzC,KAAK,EAAE,EAAE;oBACTD,QAAQ,EAAE;kBACZ,CAAC,CAAC;gBAAE;kBAAAyG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACFlJ,OAAA;kBAAO4I,SAAS,EAAC,kBAAkB;kBAACE,OAAO,EAAC,MAAM;kBAAAD,QAAA,gBAChD7I,OAAA;oBAAA6I,QAAA,EAAQ;kBAAW;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5BlJ,OAAA;oBAAA+I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNlJ,OAAA;oBAAO4I,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAkC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNlJ,OAAA;gBAAK4I,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB7I,OAAA;kBACE2F,IAAI,EAAC,OAAO;kBACZiD,SAAS,EAAC,kBAAkB;kBAC5BO,EAAE,EAAC,MAAM;kBACThE,IAAI,EAAC,YAAY;kBACjBH,KAAK,EAAC,MAAM;kBACZoG,OAAO,EAAEzJ,QAAQ,CAACU,UAAU,KAAK,MAAO;kBACxCgH,QAAQ,EAAGvE,CAAC,IAAKlD,WAAW,CAACsE,IAAI,KAAK;oBACpC,GAAGA,IAAI;oBACP7D,UAAU,EAAEyC,CAAC,CAACC,MAAM,CAACC;kBACvB,CAAC,CAAC;gBAAE;kBAAA+D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACFlJ,OAAA;kBAAO4I,SAAS,EAAC,kBAAkB;kBAACE,OAAO,EAAC,MAAM;kBAAAD,QAAA,gBAChD7I,OAAA;oBAAA6I,QAAA,EAAQ;kBAAW;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5BlJ,OAAA;oBAAA+I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNlJ,OAAA;oBAAO4I,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAwC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAELvH,QAAQ,CAACU,UAAU,KAAK,MAAM,iBACzCrC,OAAA,CAAAE,SAAA;YAAA2I,QAAA,gBACE7I,OAAA;cAAK4I,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB7I,OAAA;gBAAO4I,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrDlJ,OAAA;gBAAK4I,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnB7I,OAAA;kBAAO4I,SAAS,EAAC,YAAY;kBAAAC,QAAA,GAAC,oBAAiB,EAAC1I,MAAM,EAAC,IAAC;gBAAA;kBAAA4I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC,eACNlJ,OAAA;gBACE4I,SAAS,EAAC,aAAa;gBACvB5D,KAAK,EAAErD,QAAQ,CAACW,QAAS;gBACzB+G,QAAQ,EAAGvE,CAAC,IACVlD,WAAW,CAAEsE,IAAI,KAAM;kBACrB,GAAGA,IAAI;kBACP5D,QAAQ,EAAEwC,CAAC,CAACC,MAAM,CAACC;gBACrB,CAAC,CAAC,CACH;gBAAA6D,QAAA,GAEA1I,MAAM,KAAK,YAAY,iBACtBH,OAAA;kBAAQgF,KAAK,EAAC,KAAK;kBAAA6D,QAAA,EAAC;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACrC,EACA,CAAC/I,MAAM,KAAK,sBAAsB,IAAIA,MAAM,KAAK,0BAA0B,kBAC1EH,OAAA;kBAAQgF,KAAK,EAAC,KAAK;kBAAA6D,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACpC,EACA/I,MAAM,KAAK,YAAY,IACvBA,MAAM,KAAK,sBAAsB,IACjCA,MAAM,KAAK,0BAA0B,iBACpCH,OAAA,CAAAE,SAAA;kBAAA2I,QAAA,gBACE7I,OAAA;oBAAQgF,KAAK,EAAC,KAAK;oBAAA6D,QAAA,EAAC;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACrClJ,OAAA;oBAAQgF,KAAK,EAAC,KAAK;oBAAA6D,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACpClJ,OAAA;oBAAQgF,KAAK,EAAC,KAAK;oBAAA6D,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACpClJ,OAAA;oBAAQgF,KAAK,EAAC,KAAK;oBAAA6D,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACpClJ,OAAA;oBAAQgF,KAAK,EAAC,KAAK;oBAAA6D,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA,eACpC,CACH;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC,eAETlJ,OAAA;gBAAO4I,SAAS,EAAC,YAAY;gBAAAC,QAAA,GAC1B1I,MAAM,KAAK,YAAY,IAAI,6CAA6C,EACxE,CAACA,MAAM,KAAK,sBAAsB,IAAIA,MAAM,KAAK,0BAA0B,KAAK,6CAA6C,EAC7HA,MAAM,KAAK,YAAY,IACvBA,MAAM,KAAK,sBAAsB,IACjCA,MAAM,KAAK,0BAA0B,IAAI,8CAA8C,EACvF,CAACA,MAAM,IAAI,iBAAiB;cAAA;gBAAA4I,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENlJ,OAAA;cAAK4I,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB7I,OAAA;gBAAO4I,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpDlJ,OAAA;gBAAK4I,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1B7I,OAAA;kBAAM4I,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,GAC/BlH,QAAQ,CAACW,QAAQ,KAAK,KAAK,IAAI,GAAG,EAClCX,QAAQ,CAACW,QAAQ,KAAK,KAAK,IAAI,IAAI,EACnCX,QAAQ,CAACW,QAAQ,KAAK,KAAK,IAAI,GAAG,EAClCX,QAAQ,CAACW,QAAQ,KAAK,KAAK,IAAI,GAAG,EAClCX,QAAQ,CAACW,QAAQ,KAAK,KAAK,IAAI,GAAG;gBAAA;kBAAAyG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eACPlJ,OAAA;kBACE2F,IAAI,EAAC,QAAQ;kBACbiD,SAAS,EAAC,cAAc;kBACxBQ,WAAW,EAAC,oBAAoB;kBAChCpE,KAAK,EAAErD,QAAQ,CAACY,KAAM;kBACtB8G,QAAQ,EAAGvE,CAAC,IACVlD,WAAW,CAAEsE,IAAI,KAAM;oBACrB,GAAGA,IAAI;oBACP3D,KAAK,EAAEuC,CAAC,CAACC,MAAM,CAACC;kBAClB,CAAC,CAAC,CACH;kBACDuE,QAAQ;gBAAA;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,eACN,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGc;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMmC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,QAAQhK,WAAW;MACjB,KAAK,CAAC;QACJ,OAAOsH,2BAA2B,CAAC,CAAC;MACtC,KAAK,CAAC;QACJ,OAAOgB,qBAAqB,CAAC,CAAC;MAChC,KAAK,CAAC;QACJ,OAAOoB,+BAA+B,CAAC,CAAC;MAC1C,KAAK,CAAC;QACJ,OAAOI,iBAAiB,CAAC,CAAC;MAC5B,KAAK,CAAC;QACJ,OAAOF,iBAAiB,CAAC,CAAC;MAC5B;QACE,OAAO,IAAI;IACf;EACF,CAAC;;EAED;EACA,MAAMK,wBAAwB,GAAGA,CAAA,KAAM;IACrC,MAAMC,UAAU,GAAGxK,QAAQ,IAAIC,eAAe;IAC9C,oBACEhB,OAAA;MAAK4I,SAAS,EAAC,2FAA2F;MACrGiB,KAAK,EAAE;QACL2B,eAAe,EAAE,oBAAoB;QACrCC,MAAM,EAAE;MACV,CAAE;MAAA5C,QAAA,eACL7I,OAAA;QAAK4I,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrC7I,OAAA;UAAK4I,SAAS,EAAC,kCAAkC;UAAC8C,IAAI,EAAC,QAAQ;UAAC7B,KAAK,EAAE;YAAEC,KAAK,EAAE,MAAM;YAAEG,MAAM,EAAE;UAAO,CAAE;UAAApB,QAAA,eACvG7I,OAAA;YAAM4I,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eACNlJ,OAAA;UAAI4I,SAAS,EAAC,MAAM;UAAAC,QAAA,EAAE0C,UAAU,GAAG,yBAAyB,GAAG;QAAyB;UAAAxC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC9FlJ,OAAA;UAAG4I,SAAS,EAAC,MAAM;UAAAC,QAAA,EAAC;QAAqD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;;EAED;EACA,MAAMyC,oBAAoB,GAAGA,CAAA,kBAC3B3L,OAAA;IAAK4I,SAAS,EAAC,2FAA2F;IACrGiB,KAAK,EAAE;MACL2B,eAAe,EAAE,oBAAoB;MACrCC,MAAM,EAAE;IACV,CAAE;IAAA5C,QAAA,eACL7I,OAAA;MAAK4I,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACrC7I,OAAA;QAAK4I,SAAS,EAAC,kCAAkC;QAAC8C,IAAI,EAAC,QAAQ;QAAC7B,KAAK,EAAE;UAAEC,KAAK,EAAE,MAAM;UAAEG,MAAM,EAAE;QAAO,CAAE;QAAApB,QAAA,eACvG7I,OAAA;UAAM4I,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAU;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC,eACNlJ,OAAA;QAAI4I,SAAS,EAAC,MAAM;QAAAC,QAAA,EAAC;MAAyB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnDlJ,OAAA;QAAG4I,SAAS,EAAC,MAAM;QAAAC,QAAA,EAAC;MAAkD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,oBACElJ,OAAA;IAAK4I,SAAS,EAAC,WAAW;IAAAC,QAAA,GAEvBtH,YAAY,iBAAIvB,OAAA,CAACsL,wBAAwB;MAAAvC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAC5CzH,eAAe,iBAAIzB,OAAA,CAAC2L,oBAAoB;MAAA5C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAG5ClJ,OAAA;MAAK4I,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC3B7I,OAAA;QAAK4I,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAE9B7I,OAAA;UAAK4I,SAAS,EAAC;QAAiB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAEvClJ,OAAA;UACE4I,SAAS,EAAC,uBAAuB;UACjCiB,KAAK,EAAE;YACLC,KAAK,EAAE,GAAI,CAACzI,WAAW,GAAG,CAAC,KAAK4F,KAAK,CAAChB,MAAM,GAAG,CAAC,CAAC,GAAI,GAAG;UAC1D;QAAE;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGPlJ,OAAA;UAAK4I,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAC7B5B,KAAK,CAACR,GAAG,CAAC,CAACmF,IAAI,EAAEjF,KAAK,kBACrB3G,OAAA;YAEE4I,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAExB7I,OAAA;cACE4I,SAAS,EAAE,kBACTvH,WAAW,GAAGsF,KAAK,GAAG,CAAC,GAAG,WAAW,GACrCtF,WAAW,KAAKsF,KAAK,GAAG,CAAC,GAAG,QAAQ,GAAG,EAAE,EACxC;cAAAkC,QAAA,EAEF+C,IAAI,CAAC1E;YAAM;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACNlJ,OAAA;cAAK4I,SAAS,EAAE,iBAAiBvH,WAAW,KAAKsF,KAAK,GAAG,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;cAAAkC,QAAA,EAC1E+C,IAAI,CAACzE;YAAK;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA,GAbD0C,IAAI,CAAC1E,MAAM;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcb,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlJ,OAAA;MAAK4I,SAAS,EAAC,cAAc;MAAAC,QAAA,EAC1BwC,iBAAiB,CAAC;IAAC;MAAAtC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,eAGNlJ,OAAA;MAAK4I,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC3B7I,OAAA;QAAK4I,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B7I,OAAA;UACE4I,SAAS,EAAC,cAAc;UACxBwB,OAAO,EAAExC,cAAe;UACxBiE,QAAQ,EAAExK,WAAW,KAAK,CAAC,IAAIE,YAAa;UAAAsH,QAAA,gBAE5C7I,OAAA,CAACX,IAAI;YAAC2K,IAAI,EAAC,8BAA8B;YAACF,KAAK,EAAC,IAAI;YAACG,MAAM,EAAC;UAAI;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,YAErE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACR7H,WAAW,KAAK4F,KAAK,CAAChB,MAAM,gBAC3BjG,OAAA;UACE4I,SAAS,EAAC,UAAU;UACpBwB,OAAO,EAAEvC,YAAa;UACtBgE,QAAQ,EAAE,CAACnE,kBAAkB,CAAC,CAAC,IAAInG,YAAa;UAAAsH,QAAA,EAE/CtH,YAAY,gBACXvB,OAAA,CAAAE,SAAA;YAAA2I,QAAA,gBACE7I,OAAA;cAAK4I,SAAS,EAAC,uCAAuC;cAAC8C,IAAI,EAAC,QAAQ;cAAA7C,QAAA,eAClE7I,OAAA;gBAAM4I,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,EACLnI,QAAQ,IAAIC,eAAe,GAAG,oBAAoB,GAAG,oBAAoB;UAAA,eAC1E,CAAC,gBAEHhB,OAAA,CAAAE,SAAA;YAAA2I,QAAA,gBACE7I,OAAA,CAACX,IAAI;cAAC2K,IAAI,EAAC,6BAA6B;cAACF,KAAK,EAAC,IAAI;cAACG,MAAM,EAAC,IAAI;cAACrB,SAAS,EAAC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAClFnI,QAAQ,IAAIC,eAAe,GAAG,eAAe,GAAG,eAAe;UAAA,eAChE;QACH;UAAA+H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,gBAETlJ,OAAA;UACE4I,SAAS,EAAC,UAAU;UACpBwB,OAAO,EAAEzC,UAAW;UACpBkE,QAAQ,EAAE,CAACnE,kBAAkB,CAAC,CAAE;UAAAmB,QAAA,GAE/BxH,WAAW,KAAK4F,KAAK,CAAChB,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,MAAM,eACtDjG,OAAA,CAACX,IAAI;YAAC2K,IAAI,EAAC,+BAA+B;YAACF,KAAK,EAAC,IAAI;YAACG,MAAM,EAAC;UAAI;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEH,CAAC;AAEV;AAACxI,EAAA,CAzmCQD,YAAY;EAAA,QACFnB,WAAW,EACXE,WAAW,EACPD,SAAS;AAAA;AAAAuM,EAAA,GAHvBrL,YAAY;AA2mCrB,eAAeA,YAAY;AAAC,IAAAqL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}