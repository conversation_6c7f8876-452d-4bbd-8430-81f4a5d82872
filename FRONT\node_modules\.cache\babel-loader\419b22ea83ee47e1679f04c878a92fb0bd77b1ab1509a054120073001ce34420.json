{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NEW_LMS_FIXING\\\\FRONT\\\\src\\\\pages\\\\admin\\\\courses\\\\CreateCourse.jsx\",\n  _process$env$REACT_AP,\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Icon } from '@iconify/react';\nimport { useLocation, useParams, useNavigate } from 'react-router-dom';\nimport './CreateCourse.css';\nimport { decodeData } from '../../../utils/encodeAndEncode';\nimport { getCourseCertificate, createNewCourse, getCourseDetailsById, updateCourseDetails } from '../../../services/adminService';\nimport courseCategoriesData from '../../../utils/courseCategories.json';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst domain = (_process$env$REACT_AP = process.env.REACT_APP_DOMAIN_URL) === null || _process$env$REACT_AP === void 0 ? void 0 : _process$env$REACT_AP.replace(/\"/g, '').trim();\nfunction CreateCourse() {\n  _s();\n  var _location$state, _location$state2;\n  const location = useLocation();\n  const navigate = useNavigate();\n  const {\n    courseId\n  } = useParams();\n  // Only try to decode if courseId exists\n  const decodedCourseId = courseId ? decodeData(courseId) : null;\n  // console.log('Course ID:', courseId);\n  // console.log('Decoded Course ID:', decodedCourseId);\n\n  const isEditing = (_location$state = location.state) === null || _location$state === void 0 ? void 0 : _location$state.isEditing;\n  const initialCourseData = (_location$state2 = location.state) === null || _location$state2 === void 0 ? void 0 : _location$state2.courseData;\n  const [currentStep, setCurrentStep] = useState(1);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [isLoadingCourse, setIsLoadingCourse] = useState(false);\n  const [formData, setFormData] = useState({\n    courseTitle: '',\n    courseDescription: '',\n    courseLevel: 'intermediate',\n    courseLanguage: 'english',\n    courseCategory: '',\n    thumbnail: null,\n    thumbnailPreview: '',\n    certificateType: '',\n    courseType: 'free',\n    currency: 'SGD',\n    price: '',\n    tags: [],\n    courseInfo: []\n  });\n  useEffect(() => {\n    if (isEditing && initialCourseData) {\n      setFormData(initialCourseData);\n    }\n  }, [isEditing, initialCourseData]);\n\n  // Fetch course details if courseId is available\n  useEffect(() => {\n    const fetchCourseDetails = async () => {\n      if (courseId && decodedCourseId) {\n        setIsLoadingCourse(true);\n        try {\n          console.log('Fetching course details for ID:', decodedCourseId);\n          const response = await getCourseDetailsById({\n            course_id: decodedCourseId\n          });\n          console.log('get Course Details Response:', response);\n          if (response.success && response.data.course) {\n            var _course$certificate_t;\n            const course = response.data.course;\n            console.log('Course details fetched:', course);\n\n            // Map API response to form data structure\n            setFormData({\n              courseTitle: course.course_name || '',\n              courseDescription: course.course_desc || '',\n              courseLevel: course.levels || '',\n              courseLanguage: course.course_language || '',\n              courseCategory: course.course_category || '',\n              thumbnail: null,\n              // Will be handled separately if needed\n              thumbnailPreview: course.banner_image || '',\n              certificateType: ((_course$certificate_t = course.certificate_template_id) === null || _course$certificate_t === void 0 ? void 0 : _course$certificate_t.toString()) || '',\n              courseType: course.course_type || 'free',\n              currency: course.currency || 'USD',\n              price: course.course_price || '',\n              tags: Array.isArray(course.tags) ? course.tags : [],\n              courseInfo: Array.isArray(course.course_info) ? course.course_info : []\n            });\n            console.log('Form data populated from API');\n          } else {\n            var _response$data, _response$data2;\n            console.error('Failed to fetch course details:', (_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.error_msg);\n            alert('Failed to load course details: ' + (((_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.error_msg) || 'Unknown error'));\n          }\n        } catch (error) {\n          console.error('Error fetching course details:', error);\n          alert('Error loading course details. Please try again.');\n        } finally {\n          setIsLoadingCourse(false);\n        }\n      }\n    };\n    fetchCourseDetails();\n  }, [courseId, decodedCourseId]);\n  const [certificateData, setCertificateData] = useState([]);\n  const fetchCertificateData = async () => {\n    try {\n      const response = await getCourseCertificate();\n      // console.log('Certificate Data:', response);\n      if (response.success) {\n        setCertificateData(response.data.templates);\n      } else {\n        // console.error('Invalid certificate data format:', response);\n        setCertificateData([]); // Reset to empty array if invalid data\n      }\n    } catch (error) {\n      console.error('Error fetching certificate data:', error);\n      setCertificateData([]); // Reset to empty array on error\n    }\n  };\n  useEffect(() => {\n    fetchCertificateData();\n  }, []);\n  const [tagInput, setTagInput] = useState('');\n  const [infoInput, setInfoInput] = useState('');\n  const [editingInfo, setEditingInfo] = useState(null);\n\n  // Memoized handlers for tag and info inputs\n  const handleTagInputChange = useCallback(e => {\n    setTagInput(e.target.value);\n  }, []);\n  const handleInfoInputChange = useCallback(e => {\n    setInfoInput(e.target.value);\n  }, []);\n  const handleInputChange = useCallback(e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prevState => ({\n      ...prevState,\n      [name]: value\n    }));\n  }, []);\n  const handleThumbnailChange = e => {\n    const file = e.target.files[0];\n    if (file) {\n      setFormData(prevState => ({\n        ...prevState,\n        thumbnail: file,\n        thumbnailPreview: URL.createObjectURL(file)\n      }));\n    }\n  };\n  const handleRemoveFile = type => {\n    if (type === 'thumbnail') {\n      setFormData(prevState => ({\n        ...prevState,\n        thumbnail: null,\n        thumbnailPreview: ''\n      }));\n    }\n  };\n  const handleAddTag = useCallback(e => {\n    e.preventDefault();\n    const tag = tagInput.trim();\n    if (tag && !formData.tags.includes(tag)) {\n      if (tag.length < 3) {\n        alert('Tag must be at least 3 characters long');\n        return;\n      }\n      if (formData.tags.length >= 8) {\n        alert('Maximum 8 tags allowed');\n        return;\n      }\n      setFormData(prev => ({\n        ...prev,\n        tags: [...prev.tags, tag]\n      }));\n      setTagInput('');\n    }\n  }, [tagInput, formData.tags]);\n  const handleRemoveTag = tagToRemove => {\n    setFormData(prev => ({\n      ...prev,\n      tags: prev.tags.filter(tag => tag !== tagToRemove)\n    }));\n  };\n  const handleTagInputKeyDown = e => {\n    if (e.key === 'Enter') {\n      e.preventDefault();\n      handleAddTag(e);\n    }\n  };\n  const handleAddInfo = useCallback(e => {\n    e.preventDefault();\n    if (infoInput.trim()) {\n      if (editingInfo !== null) {\n        setFormData(prev => ({\n          ...prev,\n          courseInfo: prev.courseInfo.map((info, index) => index === editingInfo ? infoInput.trim() : info)\n        }));\n        setEditingInfo(null);\n      } else {\n        setFormData(prev => ({\n          ...prev,\n          courseInfo: [...prev.courseInfo, infoInput.trim()]\n        }));\n      }\n      setInfoInput('');\n    }\n  }, [infoInput, editingInfo]);\n  const handleEditInfo = index => {\n    setInfoInput(formData.courseInfo[index]);\n    setEditingInfo(index);\n  };\n  const handleDeleteInfo = index => {\n    setFormData(prev => ({\n      ...prev,\n      courseInfo: prev.courseInfo.filter((_, i) => i !== index)\n    }));\n  };\n  const handleInfoInputKeyDown = e => {\n    if (e.key === 'Enter') {\n      e.preventDefault();\n      handleAddInfo(e);\n    }\n  };\n  const steps = [{\n    number: '01',\n    title: 'Course Information'\n  }, {\n    number: '02',\n    title: 'Course Media'\n  }, {\n    number: '03',\n    title: 'Additional information'\n  }, {\n    number: '04',\n    title: 'Pricing'\n  }, {\n    number: '05',\n    title: 'Preview & Submit'\n  }];\n\n  // Validation functions for each step\n  const validateStep1 = () => {\n    return formData.courseTitle.trim() !== '' && formData.courseDescription.trim() !== '' && formData.courseLevel !== '' && formData.courseLanguage !== '' && formData.courseCategory !== '';\n  };\n  const validateStep2 = () => {\n    // For editing mode, allow progression if thumbnail exists (either new upload or existing image)\n    if (courseId && decodedCourseId) {\n      return (formData.thumbnail !== null || formData.thumbnailPreview !== '') && formData.certificateType !== '';\n    }\n    // For new course creation, require new thumbnail upload\n    return formData.thumbnail !== null && formData.certificateType !== '';\n  };\n  const validateStep3 = () => {\n    // For editing mode, allow empty tags and courseInfo if course already exists\n    if (courseId && decodedCourseId) {\n      return true; // Allow progression in edit mode even with empty arrays\n    }\n    // For new course creation, require at least one tag and one course info\n    return formData.tags.length > 0 && formData.courseInfo.length > 0;\n  };\n  const validateStep4 = () => {\n    if (formData.courseType === 'free') {\n      return true;\n    }\n    return formData.price !== '' && parseFloat(formData.price) > 0;\n  };\n  const validateStep5 = () => {\n    // Preview step - all previous steps must be valid\n    return validateStep1() && validateStep2() && validateStep3() && validateStep4();\n  };\n  const isCurrentStepValid = () => {\n    switch (currentStep) {\n      case 1:\n        return validateStep1();\n      case 2:\n        return validateStep2();\n      case 3:\n        return validateStep3();\n      case 4:\n        return validateStep4();\n      case 5:\n        return validateStep5();\n      default:\n        return false;\n    }\n  };\n  const handleNext = () => {\n    if (currentStep < steps.length) {\n      setCurrentStep(currentStep + 1);\n    }\n  };\n  const handlePrevious = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n  const handleSubmit = async () => {\n    if (isCurrentStepValid()) {\n      setIsSubmitting(true);\n      try {\n        // Start the minimum 3-second timer\n        const minLoadingTime = new Promise(resolve => setTimeout(resolve, 3000));\n\n        // Create FormData object for file upload\n        const submitData = new FormData();\n\n        // Add all form fields to FormData\n        submitData.append('courseTitle', formData.courseTitle);\n        submitData.append('courseDescription', formData.courseDescription);\n        submitData.append('courseLevel', formData.courseLevel);\n        submitData.append('courseLanguage', formData.courseLanguage);\n        submitData.append('courseCategory', formData.courseCategory);\n        submitData.append('certificateType', formData.certificateType);\n        submitData.append('courseType', formData.courseType);\n        submitData.append('currency', formData.currency);\n        submitData.append('price', formData.price);\n        submitData.append('tags', JSON.stringify(formData.tags));\n        submitData.append('courseInfo', JSON.stringify(formData.courseInfo));\n\n        // add loop print the data \n        for (const [key, value] of Object.entries(formData)) {\n          console.log(`${key}: ${value}`);\n        }\n\n        // Add thumbnail file if exists (for new upload)\n        if (formData.thumbnail) {\n          submitData.append('banner_image', formData.thumbnail);\n        }\n        console.log('Submitting course data...');\n        console.log('Is editing mode:', courseId && decodedCourseId);\n\n        // Choose API call based on edit mode\n        let apiCall;\n        if (courseId && decodedCourseId) {\n          // Update existing course\n          apiCall = updateCourseDetails(decodedCourseId, submitData);\n        } else {\n          // Create new course\n          apiCall = createNewCourse(submitData);\n        }\n\n        // Wait for both minimum time and API response\n        const [response] = await Promise.all([apiCall, minLoadingTime]);\n        if (response.success) {\n          console.log('Course operation successful:', response.data);\n          // Navigate to courses page after successful operation\n          navigate('/admin/courses');\n        } else {\n          var _response$data3;\n          setIsSubmitting(false);\n          const errorMsg = courseId && decodedCourseId ? 'Failed to update course' : 'Failed to create course';\n          alert(errorMsg + ': ' + (((_response$data3 = response.data) === null || _response$data3 === void 0 ? void 0 : _response$data3.error_msg) || 'Unknown error'));\n        }\n      } catch (error) {\n        setIsSubmitting(false);\n        console.error('Error with course operation:', error);\n        const errorMsg = courseId && decodedCourseId ? 'Error updating course' : 'Error creating course';\n        alert(errorMsg + '. Please try again.');\n      }\n    } else {\n      alert('Please fill in all required fields before submitting.');\n    }\n  };\n  const renderCourseInformationForm = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"course-information\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"courseTitle\",\n          className: \"form-label\",\n          children: \"Course Title *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          className: \"form-control\",\n          id: \"courseTitle\",\n          name: \"courseTitle\",\n          placeholder: \"Enter course title\",\n          value: formData.courseTitle,\n          onChange: handleInputChange,\n          maxLength: 100,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"text-muted\",\n          children: \"Maximum 100 characters allowed. Use a short and meaningful title for better readability.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"courseDescription\",\n          className: \"form-label\",\n          children: \"Course Description *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          className: \"form-control\",\n          id: \"courseDescription\",\n          name: \"courseDescription\",\n          rows: \"4\",\n          placeholder: \"Enter course description\",\n          value: formData.courseDescription,\n          onChange: handleInputChange,\n          maxLength: 1000,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"text-muted\",\n          children: \"Maximum 1000 characters allowed\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-4 mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"courseLevel\",\n          className: \"form-label\",\n          children: \"Course Level *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 417,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          className: \"form-select\",\n          id: \"courseLevel\",\n          name: \"courseLevel\",\n          value: formData.courseLevel,\n          onChange: handleInputChange,\n          required: true,\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            children: \"Select Level\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"beginner\",\n            children: \"Beginner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"intermediate\",\n            children: \"Intermediate\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"advanced\",\n            children: \"Advanced\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all-levels\",\n            children: \"All Levels\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 416,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-4 mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"courseLanguage\",\n          className: \"form-label\",\n          children: \"Course Language *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 435,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          className: \"form-select\",\n          id: \"courseLanguage\",\n          name: \"courseLanguage\",\n          value: formData.courseLanguage,\n          onChange: handleInputChange,\n          required: true,\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            children: \"Select Language\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"english\",\n            children: \"English\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"spanish\",\n            children: \"Spanish\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"french\",\n            children: \"French\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"german\",\n            children: \"German\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 448,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"chinese\",\n            children: \"Chinese\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 449,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"japanese\",\n            children: \"Japanese\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"hindi\",\n            children: \"Hindi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 436,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 434,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-4 mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"courseCategory\",\n          className: \"form-label\",\n          children: \"Course Category *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 456,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          className: \"form-select\",\n          id: \"courseCategory\",\n          name: \"courseCategory\",\n          value: formData.courseCategory,\n          onChange: handleInputChange,\n          required: true,\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            children: \"Select Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 465,\n            columnNumber: 13\n          }, this), courseCategoriesData.categories.map((category, index) => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: category,\n            children: category\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 455,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 383,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 382,\n    columnNumber: 5\n  }, this);\n  const renderCourseMediaForm = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"course-media\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-6 mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"form-label\",\n          children: \"Course Thumbnail *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 482,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: !formData.thumbnailPreview ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                id: \"thumbnail\",\n                className: \"d-none\",\n                accept: \"image/*\",\n                onChange: handleThumbnailChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"thumbnail\",\n                className: \"btn btn-outline-primary mb-3 d-flex flex-column align-items-center gap-2 mx-auto\",\n                style: {\n                  width: 'fit-content',\n                  cursor: 'pointer'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Icon, {\n                  icon: \"fluent:image-add-24-regular\",\n                  width: \"48\",\n                  height: \"48\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 499,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Upload Thumbnail\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-muted\",\n                  children: \"Recommended size: 1280x720px\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 501,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 494,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"position-relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: formData.thumbnailPreview,\n                alt: \"Course Thumbnail\",\n                className: \"img-fluid rounded\",\n                style: {\n                  width: '100%',\n                  height: 'auto'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 506,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn btn-danger d-flex align-items-center justify-content-center p-0 position-absolute\",\n                onClick: () => handleRemoveFile('thumbnail'),\n                type: \"button\",\n                style: {\n                  width: '24px',\n                  height: '24px',\n                  top: '8px',\n                  right: '8px',\n                  minWidth: 'unset',\n                  borderRadius: '4px'\n                },\n                children: /*#__PURE__*/_jsxDEV(Icon, {\n                  icon: \"fluent:delete-24-regular\",\n                  width: \"14\",\n                  height: \"14\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 525,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 483,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 481,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-6 mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"form-label\",\n          children: \"Certificate Type *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 535,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: [/*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"form-select mb-3\",\n              name: \"certificateType\",\n              value: formData.certificateType,\n              onChange: handleInputChange,\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select Certificate Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 545,\n                columnNumber: 17\n              }, this), Array.isArray(certificateData) && certificateData.map(cert => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: cert.id || cert._id,\n                children: cert.template_name || 'Unnamed Certificate'\n              }, cert.id || cert._id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 547,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 538,\n              columnNumber: 15\n            }, this), formData.certificateType && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"certificate-preview p-3 bg-light rounded\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(Icon, {\n                  icon: \"fluent:certificate-24-regular\",\n                  width: \"32\",\n                  height: \"32\",\n                  className: \"text-primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 556,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"mb-1\",\n                    children: [formData.certificateType.charAt(0).toUpperCase() + formData.certificateType.slice(1), \" Certificate\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 558,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: \"Will be issued upon course completion\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 559,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 557,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 555,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 554,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 536,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 534,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 479,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 478,\n    columnNumber: 5\n  }, this);\n  const renderAdditionalInformationForm = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"row\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"col-12 mb-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"Course Tags *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 577,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"row g-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-10\",\n                  children: /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    className: \"form-control rounded-0 rounded-start\",\n                    placeholder: \"Add tags (e.g., 'programming', 'web development')\",\n                    value: tagInput,\n                    onChange: handleTagInputChange,\n                    onKeyDown: handleTagInputKeyDown\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 581,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 580,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"btn btn-primary w-100 rounded-0 rounded-end\",\n                    onClick: handleAddTag,\n                    children: \"Add Tag\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 591,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 590,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 579,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"d-block text-muted mb-1\",\n                  children: \"Press Enter or click Add Tag to add\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 600,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"d-block text-muted\",\n                  children: [\"\\u2022 Minimum 3 characters per tag\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 603,\n                    columnNumber: 21\n                  }, this), \"\\u2022 Maximum 8 tags allowed\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 605,\n                    columnNumber: 21\n                  }, this), \"\\u2022 \", 8 - formData.tags.length, \" tags remaining\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 601,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 599,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex flex-wrap gap-2 mt-3\",\n              children: formData.tags.map((tag, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"badge bg-light text-dark border d-flex align-items-center gap-2 py-2 px-3\",\n                children: [tag, /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"btn btn-link text-danger p-0 d-flex align-items-center\",\n                  onClick: () => handleRemoveTag(tag),\n                  style: {\n                    minWidth: '20px',\n                    height: '20px'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Icon, {\n                    icon: \"fluent:dismiss-24-regular\",\n                    width: \"16\",\n                    height: \"16\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 623,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 617,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 612,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 610,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 576,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"Course Info *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 631,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"row g-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-10\",\n                  children: /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    className: \"form-control rounded-0 rounded-start\",\n                    placeholder: \"Add course information point (e.g., 'Lifetime access to course materials')\",\n                    value: infoInput,\n                    onChange: handleInfoInputChange,\n                    onKeyDown: handleInfoInputKeyDown\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 635,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 634,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"btn btn-primary w-100 rounded-0 rounded-end\",\n                    onClick: handleAddInfo,\n                    children: editingInfo !== null ? 'Update' : 'Add Info'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 645,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 644,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 633,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2\",\n                children: /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"d-block text-muted\",\n                  children: \"Add important information points about your course\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 654,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 653,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 632,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-3\",\n              children: formData.courseInfo.map((info, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center gap-2 p-2 border rounded mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(Icon, {\n                  icon: \"fluent:info-24-regular\",\n                  className: \"text-primary flex-shrink-0\",\n                  width: \"20\",\n                  height: \"20\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 666,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"flex-grow-1\",\n                  children: info\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 672,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    className: \"btn btn-link text-primary p-0 d-flex align-items-center\",\n                    onClick: () => handleEditInfo(index),\n                    style: {\n                      minWidth: '20px',\n                      height: '20px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Icon, {\n                      icon: \"fluent:edit-24-regular\",\n                      width: \"16\",\n                      height: \"16\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 680,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 674,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    className: \"btn btn-link text-danger p-0 d-flex align-items-center\",\n                    onClick: () => handleDeleteInfo(index),\n                    style: {\n                      minWidth: '20px',\n                      height: '20px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Icon, {\n                      icon: \"fluent:delete-24-regular\",\n                      width: \"16\",\n                      height: \"16\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 688,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 682,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 673,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 662,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 660,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 630,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 575,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 574,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 573,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 572,\n    columnNumber: 5\n  }, this);\n  const renderPreviewStep = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"row\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"col-12\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              icon: \"fluent:eye-24-regular\",\n              width: \"20\",\n              height: \"20\",\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 707,\n              columnNumber: 15\n            }, this), \"Course Preview\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 706,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-muted mb-0 mt-2\",\n            children: \"Review all your course information before submitting. If you need to make changes, use the \\\"Previous\\\" button to go back to any step.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 710,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 705,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"text-primary mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                icon: \"fluent:info-24-regular\",\n                width: \"18\",\n                height: \"18\",\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 718,\n                columnNumber: 17\n              }, this), \"Course Information\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 717,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-6 mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label text-muted\",\n                  children: \"Course Title\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 723,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-0 fw-medium\",\n                  children: formData.courseTitle || 'Not specified'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 724,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 722,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-6 mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label text-muted\",\n                  children: \"Course Category\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 727,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-0\",\n                  children: formData.courseCategory || 'Not specified'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 728,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 726,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-12 mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label text-muted\",\n                  children: \"Course Description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 731,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-0\",\n                  children: formData.courseDescription || 'Not specified'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 732,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 730,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-4 mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label text-muted\",\n                  children: \"Level\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 735,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-0 text-capitalize\",\n                  children: formData.courseLevel || 'Not specified'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 736,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 734,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-4 mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label text-muted\",\n                  children: \"Language\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 739,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-0 text-capitalize\",\n                  children: formData.courseLanguage || 'Not specified'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 740,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 738,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-4 mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label text-muted\",\n                  children: \"Certificate\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 743,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-0\",\n                  children: formData.certificateType ? `Certificate ID: ${formData.certificateType}` : 'Not specified'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 744,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 742,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 721,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 716,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 749,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"text-primary mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                icon: \"fluent:image-24-regular\",\n                width: \"18\",\n                height: \"18\",\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 754,\n                columnNumber: 17\n              }, this), \"Course Media\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 753,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"row\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-6 mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label text-muted\",\n                  children: \"Course Thumbnail\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 759,\n                  columnNumber: 19\n                }, this), formData.thumbnailPreview ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: formData.thumbnailPreview,\n                    alt: \"Course Thumbnail\",\n                    className: \"img-fluid rounded border\",\n                    style: {\n                      maxHeight: '150px',\n                      width: 'auto'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 762,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 761,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-0 text-muted\",\n                  children: \"No thumbnail uploaded\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 770,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 758,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 757,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 752,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 776,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"text-primary mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                icon: \"fluent:tag-24-regular\",\n                width: \"18\",\n                height: \"18\",\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 781,\n                columnNumber: 17\n              }, this), \"Additional Information\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 780,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-6 mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label text-muted\",\n                  children: \"Tags\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 786,\n                  columnNumber: 19\n                }, this), formData.tags.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex flex-wrap gap-2\",\n                  children: formData.tags.map((tag, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"badge bg-light text-dark border\",\n                    children: tag\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 790,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 788,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-0 text-muted\",\n                  children: \"No tags added\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 796,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 785,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-6 mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label text-muted\",\n                  children: \"Course Information Points\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 800,\n                  columnNumber: 19\n                }, this), formData.courseInfo.length > 0 ? /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"list-unstyled mb-0\",\n                  children: formData.courseInfo.map((info, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"mb-1\",\n                    children: [/*#__PURE__*/_jsxDEV(Icon, {\n                      icon: \"fluent:checkmark-circle-24-regular\",\n                      width: \"16\",\n                      height: \"16\",\n                      className: \"text-success me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 805,\n                      columnNumber: 27\n                    }, this), info]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 804,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 802,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-0 text-muted\",\n                  children: \"No course information added\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 811,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 799,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 784,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 779,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 817,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"text-primary mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                icon: \"fluent:payment-24-regular\",\n                width: \"18\",\n                height: \"18\",\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 822,\n                columnNumber: 17\n              }, this), \"Pricing Information\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 821,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-3 mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label text-muted\",\n                  children: \"Course Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 827,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-0\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `badge ${formData.courseType === 'free' ? 'bg-success' : 'bg-primary'}`,\n                    children: formData.courseType === 'free' ? 'Free Course' : 'Paid Course'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 829,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 828,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 826,\n                columnNumber: 17\n              }, this), formData.courseType === 'paid' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-3 mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label text-muted\",\n                    children: \"Currency\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 837,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mb-0\",\n                    children: formData.currency\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 838,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 836,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-3 mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label text-muted\",\n                    children: \"Price\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 841,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mb-0 fw-medium\",\n                    children: [formData.currency === 'USD' && '$', formData.currency === 'EUR' && '€', formData.currency === 'GBP' && '£', formData.currency === 'INR' && '₹', formData.currency === 'SGD' && 'S$', formData.price]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 842,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 840,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 825,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 820,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 714,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 704,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 703,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 702,\n    columnNumber: 5\n  }, this);\n  const renderPricingStep = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"row\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"col-12 mb-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"Course Type *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 868,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-check\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  className: \"form-check-input\",\n                  id: \"free\",\n                  name: \"courseType\",\n                  value: \"free\",\n                  checked: formData.courseType === 'free',\n                  onChange: e => setFormData(prev => ({\n                    ...prev,\n                    courseType: e.target.value,\n                    price: ''\n                    // Removed currency override to keep selected currency\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 871,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-check-label\",\n                  htmlFor: \"free\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Free Course\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 886,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 887,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: \"No payment required for enrollment\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 888,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 885,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 870,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-check\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  className: \"form-check-input\",\n                  id: \"paid\",\n                  name: \"courseType\",\n                  value: \"paid\",\n                  checked: formData.courseType === 'paid',\n                  onChange: e => setFormData(prev => ({\n                    ...prev,\n                    courseType: e.target.value\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 892,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-check-label\",\n                  htmlFor: \"paid\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Paid Course\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 905,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 906,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: \"Students must purchase to access content\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 907,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 904,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 891,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 869,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 867,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-3 p-2 bg-light rounded\",\n            children: /*#__PURE__*/_jsxDEV(\"small\", {\n              className: \"text-muted\",\n              children: [\"Debug: Selected Currency = \\\"\", formData.currency, \"\\\" | Domain = \\\"\", domain, \"\\\"\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 915,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 914,\n            columnNumber: 13\n          }, this), formData.courseType === 'paid' && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [domain === 'lms.tpi.sg' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Select Currency\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 925,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"form-select\",\n                value: formData.currency,\n                onChange: e => setFormData(prev => ({\n                  ...prev,\n                  currency: e.target.value\n                })),\n                children: /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"SGD\",\n                  children: \"SGD (S$)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 936,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 926,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                className: \"text-muted\",\n                children: \"Note: Only SGD is supported on this Platform.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 938,\n                columnNumber: 9\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 924,\n              columnNumber: 7\n            }, this), domain === 'lms.nxgenvarsity.com' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Select Currency\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 947,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"form-select\",\n                value: formData.currency,\n                onChange: e => setFormData(prev => ({\n                  ...prev,\n                  currency: e.target.value\n                })),\n                children: /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"INR\",\n                  children: \"INR (\\u20B9)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 958,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 948,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                className: \"text-muted\",\n                children: \"Note: Only INR is supported on this domain.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 960,\n                columnNumber: 9\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 946,\n              columnNumber: 7\n            }, this), domain === 'lms.creatorfoundation.in' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Select Currency\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 969,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"form-select\",\n                value: formData.currency,\n                onChange: e => setFormData(prev => ({\n                  ...prev,\n                  currency: e.target.value\n                })),\n                children: /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"INR\",\n                  children: \"INR (\\u20B9)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 980,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 970,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                className: \"text-muted\",\n                children: \"Note: Only INR is supported on this domain.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 982,\n                columnNumber: 9\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 968,\n              columnNumber: 7\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Course Price *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 990,\n                columnNumber: 7\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"input-group-text\",\n                  children: [formData.currency === 'SGD' && 'S$', formData.currency === 'INR' && '₹']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 992,\n                  columnNumber: 9\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  className: \"form-control\",\n                  placeholder: \"Enter course price\",\n                  value: formData.price,\n                  onChange: e => setFormData(prev => ({\n                    ...prev,\n                    price: e.target.value\n                  })),\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 996,\n                  columnNumber: 9\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 991,\n                columnNumber: 7\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 989,\n              columnNumber: 5\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 865,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 864,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 863,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 862,\n    columnNumber: 5\n  }, this);\n  const renderStepContent = () => {\n    switch (currentStep) {\n      case 1:\n        return renderCourseInformationForm();\n      case 2:\n        return renderCourseMediaForm();\n      case 3:\n        return renderAdditionalInformationForm();\n      case 4:\n        return renderPricingStep();\n      case 5:\n        return renderPreviewStep();\n      default:\n        return null;\n    }\n  };\n\n  // Loading overlay component for submission\n  const SubmissionLoadingOverlay = () => {\n    const isEditMode = courseId && decodedCourseId;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center\",\n      style: {\n        backgroundColor: 'rgba(0, 0, 0, 0.7)',\n        zIndex: 9999\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center text-white\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner-border text-primary mb-3\",\n          role: \"status\",\n          style: {\n            width: '3rem',\n            height: '3rem'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"visually-hidden\",\n            children: \"Loading...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1048,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1047,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n          className: \"mb-2\",\n          children: isEditMode ? 'Updating Your Course...' : 'Creating Your Course...'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1050,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mb-0\",\n          children: \"Please wait while we process your course information.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1051,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1046,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1041,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Loading overlay component for fetching course details\n  const CourseLoadingOverlay = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center\",\n    style: {\n      backgroundColor: 'rgba(0, 0, 0, 0.7)',\n      zIndex: 9999\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center text-white\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner-border text-primary mb-3\",\n        role: \"status\",\n        style: {\n          width: '3rem',\n          height: '3rem'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"visually-hidden\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1066,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1065,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n        className: \"mb-2\",\n        children: \"Loading Course Details...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1068,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mb-0\",\n        children: \"Please wait while we fetch the course information.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1069,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1064,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1059,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: [isSubmitting && /*#__PURE__*/_jsxDEV(SubmissionLoadingOverlay, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1077,\n      columnNumber: 24\n    }, this), isLoadingCourse && /*#__PURE__*/_jsxDEV(CourseLoadingOverlay, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1078,\n      columnNumber: 27\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stepper-card\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stepper-wrapper\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stepper-line-bg\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1084,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stepper-line-progress\",\n          style: {\n            width: `${(currentStep - 1) / (steps.length - 1) * 100}%`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1086,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stepper-content\",\n          children: steps.map((step, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stepper-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `stepper-circle ${currentStep > index + 1 ? 'completed' : currentStep === index + 1 ? 'active' : ''}`,\n              children: step.number\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1100,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `stepper-label ${currentStep === index + 1 ? 'active' : ''}`,\n              children: step.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1108,\n              columnNumber: 17\n            }, this)]\n          }, step.number, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1096,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1094,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1082,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1081,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stepper-card\",\n      children: renderStepContent()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1118,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stepper-card\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stepper-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn-previous\",\n          onClick: handlePrevious,\n          disabled: currentStep === 1 || isSubmitting,\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            icon: \"fluent:arrow-left-24-regular\",\n            width: \"20\",\n            height: \"20\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1130,\n            columnNumber: 13\n          }, this), \"Previous\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1125,\n          columnNumber: 11\n        }, this), currentStep === steps.length ? /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn-next\",\n          onClick: handleSubmit,\n          disabled: !isCurrentStepValid() || isSubmitting,\n          children: isSubmitting ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner-border spinner-border-sm me-2\",\n              role: \"status\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"visually-hidden\",\n                children: \"Loading...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1142,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1141,\n              columnNumber: 19\n            }, this), courseId && decodedCourseId ? 'Updating Course...' : 'Creating Course...']\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              icon: \"fluent:checkmark-24-regular\",\n              width: \"20\",\n              height: \"20\",\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1148,\n              columnNumber: 19\n            }, this), courseId && decodedCourseId ? 'Update Course' : 'Submit Course']\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1134,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn-next\",\n          onClick: handleNext,\n          disabled: !isCurrentStepValid(),\n          children: [currentStep === steps.length - 1 ? 'Preview' : 'Next', /*#__PURE__*/_jsxDEV(Icon, {\n            icon: \"fluent:arrow-right-24-regular\",\n            width: \"20\",\n            height: \"20\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1160,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1154,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1124,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1123,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1075,\n    columnNumber: 5\n  }, this);\n}\n_s(CreateCourse, \"b0lzQkcmWTv6OHKCvtSLfioYSy8=\", false, function () {\n  return [useLocation, useNavigate, useParams];\n});\n_c = CreateCourse;\nexport default CreateCourse;\nvar _c;\n$RefreshReg$(_c, \"CreateCourse\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Icon", "useLocation", "useParams", "useNavigate", "decodeData", "getCourseCertificate", "createNewCourse", "getCourseDetailsById", "updateCourseDetails", "courseCategoriesData", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "domain", "_process$env$REACT_AP", "process", "env", "REACT_APP_DOMAIN_URL", "replace", "trim", "CreateCourse", "_s", "_location$state", "_location$state2", "location", "navigate", "courseId", "decodedCourseId", "isEditing", "state", "initialCourseData", "courseData", "currentStep", "setCurrentStep", "isSubmitting", "setIsSubmitting", "isLoadingCourse", "setIsLoadingCourse", "formData", "setFormData", "courseTitle", "courseDescription", "courseLevel", "courseLanguage", "courseCategory", "thumbnail", "thumbnailPreview", "certificateType", "courseType", "currency", "price", "tags", "courseInfo", "fetchCourseDetails", "console", "log", "response", "course_id", "success", "data", "course", "_course$certificate_t", "course_name", "course_desc", "levels", "course_language", "course_category", "banner_image", "certificate_template_id", "toString", "course_type", "course_price", "Array", "isArray", "course_info", "_response$data", "_response$data2", "error", "error_msg", "alert", "certificateData", "setCertificateData", "fetchCertificateData", "templates", "tagInput", "setTagInput", "infoInput", "setInfoInput", "editingInfo", "setEditingInfo", "handleTagInputChange", "e", "target", "value", "handleInfoInputChange", "handleInputChange", "name", "prevState", "handleThumbnailChange", "file", "files", "URL", "createObjectURL", "handleRemoveFile", "type", "handleAddTag", "preventDefault", "tag", "includes", "length", "prev", "handleRemoveTag", "tagToRemove", "filter", "handleTagInputKeyDown", "key", "handleAddInfo", "map", "info", "index", "handleEditInfo", "handleDeleteInfo", "_", "i", "handleInfoInputKeyDown", "steps", "number", "title", "validateStep1", "validateStep2", "validateStep3", "validateStep4", "parseFloat", "validateStep5", "isCurrentStepValid", "handleNext", "handlePrevious", "handleSubmit", "minLoadingTime", "Promise", "resolve", "setTimeout", "submitData", "FormData", "append", "JSON", "stringify", "Object", "entries", "apiCall", "all", "_response$data3", "errorMsg", "renderCourseInformationForm", "className", "children", "htmlFor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "id", "placeholder", "onChange", "max<PERSON><PERSON><PERSON>", "required", "rows", "categories", "category", "renderCourseMediaForm", "accept", "style", "width", "cursor", "icon", "height", "src", "alt", "onClick", "top", "right", "min<PERSON><PERSON><PERSON>", "borderRadius", "cert", "_id", "template_name", "char<PERSON>t", "toUpperCase", "slice", "renderAdditionalInformationForm", "onKeyDown", "renderPreviewStep", "maxHeight", "renderPricingStep", "checked", "renderStepContent", "SubmissionLoadingOverlay", "isEditMode", "backgroundColor", "zIndex", "role", "CourseLoadingOverlay", "step", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/NEW_LMS_FIXING/FRONT/src/pages/admin/courses/CreateCourse.jsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\r\nimport { Icon } from '@iconify/react';\r\nimport { useLocation, useParams, useNavigate } from 'react-router-dom';\r\nimport './CreateCourse.css';\r\nimport { decodeData } from '../../../utils/encodeAndEncode';\r\nimport { getCourseCertificate, createNewCourse, getCourseDetailsById, updateCourseDetails } from '../../../services/adminService';\r\nimport courseCategoriesData from '../../../utils/courseCategories.json';\r\nconst domain = process.env.REACT_APP_DOMAIN_URL?.replace(/\"/g, '').trim();\r\n\r\nfunction CreateCourse() {\r\n  const location = useLocation();\r\n  const navigate = useNavigate();\r\n  const { courseId } = useParams();\r\n  // Only try to decode if courseId exists\r\n  const decodedCourseId = courseId ? decodeData(courseId) : null;\r\n  // console.log('Course ID:', courseId);\r\n  // console.log('Decoded Course ID:', decodedCourseId);\r\n\r\n  const isEditing = location.state?.isEditing;\r\n  const initialCourseData = location.state?.courseData;\r\n\r\n  const [currentStep, setCurrentStep] = useState(1);\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [isLoadingCourse, setIsLoadingCourse] = useState(false);\r\n  const [formData, setFormData] = useState({\r\n    courseTitle: '',\r\n    courseDescription: '',\r\n    courseLevel: 'intermediate',\r\n    courseLanguage: 'english',\r\n    courseCategory: '',\r\n    thumbnail: null,\r\n    thumbnailPreview: '',\r\n    certificateType: '',\r\n    courseType: 'free',\r\n    currency: 'SGD',\r\n    price: '',\r\n    tags: [],\r\n    courseInfo: []\r\n  });\r\n\r\n  useEffect(() => {\r\n    if (isEditing && initialCourseData) {\r\n      setFormData(initialCourseData);\r\n    }\r\n  }, [isEditing, initialCourseData]);\r\n\r\n  // Fetch course details if courseId is available\r\n  useEffect(() => {\r\n    const fetchCourseDetails = async () => {\r\n      if (courseId && decodedCourseId) {\r\n        setIsLoadingCourse(true);\r\n        try {\r\n          console.log('Fetching course details for ID:', decodedCourseId);\r\n          const response = await getCourseDetailsById({ course_id: decodedCourseId });\r\n\r\n          console.log('get Course Details Response:', response);\r\n\r\n          if (response.success && response.data.course) {\r\n            const course = response.data.course;\r\n            console.log('Course details fetched:', course);\r\n\r\n            // Map API response to form data structure\r\n            setFormData({\r\n              courseTitle: course.course_name || '',\r\n              courseDescription: course.course_desc || '',\r\n              courseLevel: course.levels || '',\r\n              courseLanguage: course.course_language || '',\r\n              courseCategory: course.course_category || '',\r\n              thumbnail: null, // Will be handled separately if needed\r\n              thumbnailPreview: course.banner_image || '',\r\n              certificateType: course.certificate_template_id?.toString() || '',\r\n              courseType: course.course_type || 'free',\r\n              currency: course.currency || 'USD',\r\n              price: course.course_price || '',\r\n              tags: Array.isArray(course.tags) ? course.tags : [],\r\n              courseInfo: Array.isArray(course.course_info) ? course.course_info : []\r\n            });\r\n\r\n            console.log('Form data populated from API');\r\n          } else {\r\n            console.error('Failed to fetch course details:', response.data?.error_msg);\r\n            alert('Failed to load course details: ' + (response.data?.error_msg || 'Unknown error'));\r\n          }\r\n        } catch (error) {\r\n          console.error('Error fetching course details:', error);\r\n          alert('Error loading course details. Please try again.');\r\n        } finally {\r\n          setIsLoadingCourse(false);\r\n        }\r\n      }\r\n    };\r\n\r\n    fetchCourseDetails();\r\n  }, [courseId, decodedCourseId]);\r\n\r\n  const [certificateData, setCertificateData] = useState([]);\r\n\r\n  const fetchCertificateData = async () => {\r\n    try {\r\n      const response = await getCourseCertificate();\r\n      // console.log('Certificate Data:', response);\r\n      if (response.success) {\r\n        setCertificateData(response.data.templates);\r\n      } else {\r\n        // console.error('Invalid certificate data format:', response);\r\n        setCertificateData([]); // Reset to empty array if invalid data\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching certificate data:', error);\r\n      setCertificateData([]); // Reset to empty array on error\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchCertificateData();\r\n  }, []);\r\n\r\n  const [tagInput, setTagInput] = useState('');\r\n  const [infoInput, setInfoInput] = useState('');\r\n  const [editingInfo, setEditingInfo] = useState(null);\r\n\r\n  // Memoized handlers for tag and info inputs\r\n  const handleTagInputChange = useCallback((e) => {\r\n    setTagInput(e.target.value);\r\n  }, []);\r\n\r\n  const handleInfoInputChange = useCallback((e) => {\r\n    setInfoInput(e.target.value);\r\n  }, []);\r\n\r\n  const handleInputChange = useCallback((e) => {\r\n    const { name, value } = e.target;\r\n    setFormData(prevState => ({\r\n      ...prevState,\r\n      [name]: value\r\n    }));\r\n  }, []);\r\n\r\n  const handleThumbnailChange = (e) => {\r\n    const file = e.target.files[0];\r\n    if (file) {\r\n      setFormData(prevState => ({\r\n        ...prevState,\r\n        thumbnail: file,\r\n        thumbnailPreview: URL.createObjectURL(file)\r\n      }));\r\n    }\r\n  };\r\n\r\n  const handleRemoveFile = (type) => {\r\n    if (type === 'thumbnail') {\r\n      setFormData(prevState => ({\r\n        ...prevState,\r\n        thumbnail: null,\r\n        thumbnailPreview: ''\r\n      }));\r\n    }\r\n  };\r\n\r\n  const handleAddTag = useCallback((e) => {\r\n    e.preventDefault();\r\n    const tag = tagInput.trim();\r\n    if (tag && !formData.tags.includes(tag)) {\r\n      if (tag.length < 3) {\r\n        alert('Tag must be at least 3 characters long');\r\n        return;\r\n      }\r\n      if (formData.tags.length >= 8) {\r\n        alert('Maximum 8 tags allowed');\r\n        return;\r\n      }\r\n      setFormData(prev => ({\r\n        ...prev,\r\n        tags: [...prev.tags, tag]\r\n      }));\r\n      setTagInput('');\r\n    }\r\n  }, [tagInput, formData.tags]);\r\n\r\n  const handleRemoveTag = (tagToRemove) => {\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      tags: prev.tags.filter(tag => tag !== tagToRemove)\r\n    }));\r\n  };\r\n\r\n  const handleTagInputKeyDown = (e) => {\r\n    if (e.key === 'Enter') {\r\n      e.preventDefault();\r\n      handleAddTag(e);\r\n    }\r\n  };\r\n\r\n  const handleAddInfo = useCallback((e) => {\r\n    e.preventDefault();\r\n    if (infoInput.trim()) {\r\n      if (editingInfo !== null) {\r\n        setFormData(prev => ({\r\n          ...prev,\r\n          courseInfo: prev.courseInfo.map((info, index) =>\r\n            index === editingInfo ? infoInput.trim() : info\r\n          )\r\n        }));\r\n        setEditingInfo(null);\r\n      } else {\r\n        setFormData(prev => ({\r\n          ...prev,\r\n          courseInfo: [...prev.courseInfo, infoInput.trim()]\r\n        }));\r\n      }\r\n      setInfoInput('');\r\n    }\r\n  }, [infoInput, editingInfo]);\r\n\r\n  const handleEditInfo = (index) => {\r\n    setInfoInput(formData.courseInfo[index]);\r\n    setEditingInfo(index);\r\n  };\r\n\r\n  const handleDeleteInfo = (index) => {\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      courseInfo: prev.courseInfo.filter((_, i) => i !== index)\r\n    }));\r\n  };\r\n\r\n  const handleInfoInputKeyDown = (e) => {\r\n    if (e.key === 'Enter') {\r\n      e.preventDefault();\r\n      handleAddInfo(e);\r\n    }\r\n  };\r\n\r\n  const steps = [\r\n    { number: '01', title: 'Course Information' },\r\n    { number: '02', title: 'Course Media' },\r\n    { number: '03', title: 'Additional information' },\r\n    { number: '04', title: 'Pricing' },\r\n    { number: '05', title: 'Preview & Submit' }\r\n  ];\r\n\r\n  // Validation functions for each step\r\n  const validateStep1 = () => {\r\n    return formData.courseTitle.trim() !== '' &&\r\n           formData.courseDescription.trim() !== '' &&\r\n           formData.courseLevel !== '' &&\r\n           formData.courseLanguage !== '' &&\r\n           formData.courseCategory !== '';\r\n  };\r\n\r\n  const validateStep2 = () => {\r\n    // For editing mode, allow progression if thumbnail exists (either new upload or existing image)\r\n    if (courseId && decodedCourseId) {\r\n      return (formData.thumbnail !== null || formData.thumbnailPreview !== '') &&\r\n             formData.certificateType !== '';\r\n    }\r\n    // For new course creation, require new thumbnail upload\r\n    return formData.thumbnail !== null && formData.certificateType !== '';\r\n  };\r\n\r\n  const validateStep3 = () => {\r\n    // For editing mode, allow empty tags and courseInfo if course already exists\r\n    if (courseId && decodedCourseId) {\r\n      return true; // Allow progression in edit mode even with empty arrays\r\n    }\r\n    // For new course creation, require at least one tag and one course info\r\n    return formData.tags.length > 0 && formData.courseInfo.length > 0;\r\n  };\r\n\r\n  const validateStep4 = () => {\r\n    if (formData.courseType === 'free') {\r\n      return true;\r\n    }\r\n    return formData.price !== '' && parseFloat(formData.price) > 0;\r\n  };\r\n\r\n  const validateStep5 = () => {\r\n    // Preview step - all previous steps must be valid\r\n    return validateStep1() && validateStep2() && validateStep3() && validateStep4();\r\n  };\r\n\r\n  const isCurrentStepValid = () => {\r\n    switch (currentStep) {\r\n      case 1:\r\n        return validateStep1();\r\n      case 2:\r\n        return validateStep2();\r\n      case 3:\r\n        return validateStep3();\r\n      case 4:\r\n        return validateStep4();\r\n      case 5:\r\n        return validateStep5();\r\n      default:\r\n        return false;\r\n    }\r\n  };\r\n\r\n  const handleNext = () => {\r\n    if (currentStep < steps.length) {\r\n      setCurrentStep(currentStep + 1);\r\n    }\r\n  };\r\n\r\n  const handlePrevious = () => {\r\n    if (currentStep > 1) {\r\n      setCurrentStep(currentStep - 1);\r\n    }\r\n  };\r\n\r\n  const handleSubmit = async () => {\r\n    if (isCurrentStepValid()) {\r\n      setIsSubmitting(true);\r\n\r\n      try {\r\n        // Start the minimum 3-second timer\r\n        const minLoadingTime = new Promise(resolve => setTimeout(resolve, 3000));\r\n\r\n        // Create FormData object for file upload\r\n        const submitData = new FormData();\r\n\r\n        // Add all form fields to FormData\r\n        submitData.append('courseTitle', formData.courseTitle);\r\n        submitData.append('courseDescription', formData.courseDescription);\r\n        submitData.append('courseLevel', formData.courseLevel);\r\n        submitData.append('courseLanguage', formData.courseLanguage);\r\n        submitData.append('courseCategory', formData.courseCategory);\r\n        submitData.append('certificateType', formData.certificateType);\r\n        submitData.append('courseType', formData.courseType);\r\n        submitData.append('currency', formData.currency);\r\n        submitData.append('price', formData.price);\r\n        submitData.append('tags', JSON.stringify(formData.tags));\r\n        submitData.append('courseInfo', JSON.stringify(formData.courseInfo));\r\n\r\n        // add loop print the data \r\n        for (const [key, value] of Object.entries(formData)) {\r\n          console.log(`${key}: ${value}`);\r\n        }\r\n\r\n        // Add thumbnail file if exists (for new upload)\r\n        if (formData.thumbnail) {\r\n          submitData.append('banner_image', formData.thumbnail);\r\n        }\r\n\r\n        console.log('Submitting course data...');\r\n        console.log('Is editing mode:', courseId && decodedCourseId);\r\n\r\n        // Choose API call based on edit mode\r\n        let apiCall;\r\n        if (courseId && decodedCourseId) {\r\n          // Update existing course\r\n          apiCall = updateCourseDetails(decodedCourseId, submitData);\r\n        } else {\r\n          // Create new course\r\n          apiCall = createNewCourse(submitData);\r\n        }\r\n\r\n        // Wait for both minimum time and API response\r\n        const [response] = await Promise.all([apiCall, minLoadingTime]);\r\n\r\n        if (response.success) {\r\n          console.log('Course operation successful:', response.data);\r\n          // Navigate to courses page after successful operation\r\n          navigate('/admin/courses');\r\n        } else {\r\n          setIsSubmitting(false);\r\n          const errorMsg = courseId && decodedCourseId ? 'Failed to update course' : 'Failed to create course';\r\n          alert(errorMsg + ': ' + (response.data?.error_msg || 'Unknown error'));\r\n        }\r\n      } catch (error) {\r\n        setIsSubmitting(false);\r\n        console.error('Error with course operation:', error);\r\n        const errorMsg = courseId && decodedCourseId ? 'Error updating course' : 'Error creating course';\r\n        alert(errorMsg + '. Please try again.');\r\n      }\r\n    } else {\r\n      alert('Please fill in all required fields before submitting.');\r\n    }\r\n  };\r\n\r\n  const renderCourseInformationForm = () => (\r\n    <div className=\"course-information\">\r\n      <div className=\"row\">\r\n        <div className=\"col-12 mb-3\">\r\n          <label htmlFor=\"courseTitle\" className=\"form-label\">Course Title *</label>\r\n          <input\r\n            type=\"text\"\r\n            className=\"form-control\"\r\n            id=\"courseTitle\"\r\n            name=\"courseTitle\"\r\n            placeholder=\"Enter course title\"\r\n            value={formData.courseTitle}\r\n            onChange={handleInputChange}\r\n            maxLength={100}\r\n            required\r\n          />\r\n          <small className=\"text-muted\">Maximum 100 characters allowed. Use a short and meaningful title for better readability.</small>\r\n        </div>\r\n\r\n        <div className=\"col-12 mb-3\">\r\n          <label htmlFor=\"courseDescription\" className=\"form-label\">Course Description *</label>\r\n          <textarea\r\n            className=\"form-control\"\r\n            id=\"courseDescription\"\r\n            name=\"courseDescription\"\r\n            rows=\"4\"\r\n            placeholder=\"Enter course description\"\r\n            value={formData.courseDescription}\r\n            onChange={handleInputChange}\r\n            maxLength={1000}\r\n            required\r\n          ></textarea>\r\n          <small className=\"text-muted\">Maximum 1000 characters allowed</small>\r\n        </div>\r\n\r\n        <div className=\"col-md-4 mb-3\">\r\n          <label htmlFor=\"courseLevel\" className=\"form-label\">Course Level *</label>\r\n          <select\r\n            className=\"form-select\"\r\n            id=\"courseLevel\"\r\n            name=\"courseLevel\"\r\n            value={formData.courseLevel}\r\n            onChange={handleInputChange}\r\n            required\r\n          >\r\n            <option value=\"\">Select Level</option>\r\n            <option value=\"beginner\">Beginner</option>\r\n            <option value=\"intermediate\">Intermediate</option>\r\n            <option value=\"advanced\">Advanced</option>\r\n            <option value=\"all-levels\">All Levels</option>\r\n          </select>\r\n        </div>\r\n\r\n        <div className=\"col-md-4 mb-3\">\r\n          <label htmlFor=\"courseLanguage\" className=\"form-label\">Course Language *</label>\r\n          <select\r\n            className=\"form-select\"\r\n            id=\"courseLanguage\"\r\n            name=\"courseLanguage\"\r\n            value={formData.courseLanguage}\r\n            onChange={handleInputChange}\r\n            required\r\n          >\r\n            <option value=\"\">Select Language</option>\r\n            <option value=\"english\">English</option>\r\n            <option value=\"spanish\">Spanish</option>\r\n            <option value=\"french\">French</option>\r\n            <option value=\"german\">German</option>\r\n            <option value=\"chinese\">Chinese</option>\r\n            <option value=\"japanese\">Japanese</option>\r\n            <option value=\"hindi\">Hindi</option>\r\n          </select>\r\n        </div>\r\n\r\n        <div className=\"col-md-4 mb-3\">\r\n          <label htmlFor=\"courseCategory\" className=\"form-label\">Course Category *</label>\r\n          <select\r\n            className=\"form-select\"\r\n            id=\"courseCategory\"\r\n            name=\"courseCategory\"\r\n            value={formData.courseCategory}\r\n            onChange={handleInputChange}\r\n            required\r\n          >\r\n            <option value=\"\">Select Category</option>\r\n            {courseCategoriesData.categories.map((category, index) => (\r\n              <option key={index} value={category}>\r\n                {category}\r\n              </option>\r\n            ))}\r\n          </select>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const renderCourseMediaForm = () => (\r\n    <div className=\"course-media\">\r\n      <div className=\"row\">\r\n        {/* Thumbnail Upload */}\r\n        <div className=\"col-md-6 mb-4\">\r\n          <label className=\"form-label\">Course Thumbnail *</label>\r\n          <div className=\"card\">\r\n            <div className=\"card-body\">\r\n              {!formData.thumbnailPreview ? (\r\n                <div className=\"text-center p-4\">\r\n                  <input\r\n                    type=\"file\"\r\n                    id=\"thumbnail\"\r\n                    className=\"d-none\"\r\n                    accept=\"image/*\"\r\n                    onChange={handleThumbnailChange}\r\n                  />\r\n                  <label \r\n                    htmlFor=\"thumbnail\" \r\n                    className=\"btn btn-outline-primary mb-3 d-flex flex-column align-items-center gap-2 mx-auto\"\r\n                    style={{ width: 'fit-content', cursor: 'pointer' }}\r\n                  >\r\n                    <Icon icon=\"fluent:image-add-24-regular\" width=\"48\" height=\"48\" />\r\n                    <span>Upload Thumbnail</span>\r\n                    <small className=\"text-muted\">Recommended size: 1280x720px</small>\r\n                  </label>\r\n                </div>\r\n              ) : (\r\n                <div className=\"position-relative\">\r\n                  <img\r\n                    src={formData.thumbnailPreview}\r\n                    alt=\"Course Thumbnail\"\r\n                    className=\"img-fluid rounded\"\r\n                    style={{ width: '100%', height: 'auto' }}\r\n                  />\r\n                  <button\r\n                    className=\"btn btn-danger d-flex align-items-center justify-content-center p-0 position-absolute\"\r\n                    onClick={() => handleRemoveFile('thumbnail')}\r\n                    type=\"button\"\r\n                    style={{ \r\n                      width: '24px', \r\n                      height: '24px', \r\n                      top: '8px', \r\n                      right: '8px',\r\n                      minWidth: 'unset',\r\n                      borderRadius: '4px'\r\n                    }}\r\n                  >\r\n                    <Icon icon=\"fluent:delete-24-regular\" width=\"14\" height=\"14\" />\r\n                  </button>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Certificate Selection */}\r\n        <div className=\"col-md-6 mb-4\">\r\n          <label className=\"form-label\">Certificate Type *</label>\r\n          <div className=\"card\">\r\n            <div className=\"card-body\">\r\n              <select\r\n                className=\"form-select mb-3\"\r\n                name=\"certificateType\"\r\n                value={formData.certificateType}\r\n                onChange={handleInputChange}\r\n                required\r\n              >\r\n                <option value=\"\">Select Certificate Type</option>\r\n                {Array.isArray(certificateData) && certificateData.map((cert) => (\r\n                  <option key={cert.id || cert._id} value={cert.id || cert._id}>\r\n                    {cert.template_name || 'Unnamed Certificate'}\r\n                  </option>\r\n                ))}\r\n              </select>\r\n\r\n              {formData.certificateType && (\r\n                <div className=\"certificate-preview p-3 bg-light rounded\">\r\n                  <div className=\"d-flex align-items-center gap-3\">\r\n                    <Icon icon=\"fluent:certificate-24-regular\" width=\"32\" height=\"32\" className=\"text-primary\" />\r\n                    <div>\r\n                      <h6 className=\"mb-1\">{formData.certificateType.charAt(0).toUpperCase() + formData.certificateType.slice(1)} Certificate</h6>\r\n                      <small className=\"text-muted\">Will be issued upon course completion</small>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const renderAdditionalInformationForm = () => (\r\n    <div className=\"row\">\r\n      <div className=\"col-12 mb-4\">\r\n        <div className=\"card\">\r\n          <div className=\"card-body\">\r\n            <div className=\"mb-4\">\r\n              <label className=\"form-label\">Course Tags *</label>\r\n              <div className=\"mb-2\">\r\n                <div className=\"row g-2\">\r\n                  <div className=\"col-10\">\r\n                    <input\r\n                      type=\"text\"\r\n                      className=\"form-control rounded-0 rounded-start\"\r\n                      placeholder=\"Add tags (e.g., 'programming', 'web development')\"\r\n                      value={tagInput}\r\n                      onChange={handleTagInputChange}\r\n                      onKeyDown={handleTagInputKeyDown}\r\n                    />\r\n                  </div>\r\n                  <div className=\"col-2\">\r\n                    <button \r\n                      className=\"btn btn-primary w-100 rounded-0 rounded-end\"\r\n                      onClick={handleAddTag}\r\n                    >\r\n                      Add Tag\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n                <div className=\"mt-2\">\r\n                  <small className=\"d-block text-muted mb-1\">Press Enter or click Add Tag to add</small>\r\n                  <small className=\"d-block text-muted\">\r\n                    • Minimum 3 characters per tag\r\n                    <br />\r\n                    • Maximum 8 tags allowed\r\n                    <br />\r\n                    • {8 - formData.tags.length} tags remaining\r\n                  </small>\r\n                </div>\r\n              </div>\r\n              <div className=\"d-flex flex-wrap gap-2 mt-3\">\r\n                {formData.tags.map((tag, index) => (\r\n                  <div \r\n                    key={index}\r\n                    className=\"badge bg-light text-dark border d-flex align-items-center gap-2 py-2 px-3\"\r\n                  >\r\n                    {tag}\r\n                    <button\r\n                      type=\"button\"\r\n                      className=\"btn btn-link text-danger p-0 d-flex align-items-center\"\r\n                      onClick={() => handleRemoveTag(tag)}\r\n                      style={{ minWidth: '20px', height: '20px' }}\r\n                    >\r\n                      <Icon icon=\"fluent:dismiss-24-regular\" width=\"16\" height=\"16\" />\r\n                    </button>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"mb-4\">\r\n              <label className=\"form-label\">Course Info *</label>\r\n              <div className=\"mb-2\">\r\n                <div className=\"row g-2\">\r\n                  <div className=\"col-10\">\r\n                    <input\r\n                      type=\"text\"\r\n                      className=\"form-control rounded-0 rounded-start\"\r\n                      placeholder=\"Add course information point (e.g., 'Lifetime access to course materials')\"\r\n                      value={infoInput}\r\n                      onChange={handleInfoInputChange}\r\n                      onKeyDown={handleInfoInputKeyDown}\r\n                    />\r\n                  </div>\r\n                  <div className=\"col-2\">\r\n                    <button\r\n                      className=\"btn btn-primary w-100 rounded-0 rounded-end\"\r\n                      onClick={handleAddInfo}\r\n                    >\r\n                      {editingInfo !== null ? 'Update' : 'Add Info'}\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n                <div className=\"mt-2\">\r\n                  <small className=\"d-block text-muted\">\r\n                    Add important information points about your course\r\n                  </small>\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"mt-3\">\r\n                {formData.courseInfo.map((info, index) => (\r\n                  <div\r\n                    key={index}\r\n                    className=\"d-flex align-items-center gap-2 p-2 border rounded mb-2\"\r\n                  >\r\n                    <Icon\r\n                      icon=\"fluent:info-24-regular\"\r\n                      className=\"text-primary flex-shrink-0\"\r\n                      width=\"20\"\r\n                      height=\"20\"\r\n                    />\r\n                    <span className=\"flex-grow-1\">{info}</span>\r\n                    <div className=\"d-flex gap-2\">\r\n                      <button\r\n                        type=\"button\"\r\n                        className=\"btn btn-link text-primary p-0 d-flex align-items-center\"\r\n                        onClick={() => handleEditInfo(index)}\r\n                        style={{ minWidth: '20px', height: '20px' }}\r\n                      >\r\n                        <Icon icon=\"fluent:edit-24-regular\" width=\"16\" height=\"16\" />\r\n                      </button>\r\n                      <button\r\n                        type=\"button\"\r\n                        className=\"btn btn-link text-danger p-0 d-flex align-items-center\"\r\n                        onClick={() => handleDeleteInfo(index)}\r\n                        style={{ minWidth: '20px', height: '20px' }}\r\n                      >\r\n                        <Icon icon=\"fluent:delete-24-regular\" width=\"16\" height=\"16\" />\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const renderPreviewStep = () => (\r\n    <div className=\"row\">\r\n      <div className=\"col-12\">\r\n        <div className=\"card\">\r\n          <div className=\"card-header\">\r\n            <h5 className=\"mb-0\">\r\n              <Icon icon=\"fluent:eye-24-regular\" width=\"20\" height=\"20\" className=\"me-2\" />\r\n              Course Preview\r\n            </h5>\r\n            <p className=\"text-muted mb-0 mt-2\">\r\n              Review all your course information before submitting. If you need to make changes, use the \"Previous\" button to go back to any step.\r\n            </p>\r\n          </div>\r\n          <div className=\"card-body\">\r\n            {/* Course Information Section */}\r\n            <div className=\"mb-4\">\r\n              <h6 className=\"text-primary mb-3\">\r\n                <Icon icon=\"fluent:info-24-regular\" width=\"18\" height=\"18\" className=\"me-2\" />\r\n                Course Information\r\n              </h6>\r\n              <div className=\"row\">\r\n                <div className=\"col-md-6 mb-3\">\r\n                  <label className=\"form-label text-muted\">Course Title</label>\r\n                  <p className=\"mb-0 fw-medium\">{formData.courseTitle || 'Not specified'}</p>\r\n                </div>\r\n                <div className=\"col-md-6 mb-3\">\r\n                  <label className=\"form-label text-muted\">Course Category</label>\r\n                  <p className=\"mb-0\">{formData.courseCategory || 'Not specified'}</p>\r\n                </div>\r\n                <div className=\"col-12 mb-3\">\r\n                  <label className=\"form-label text-muted\">Course Description</label>\r\n                  <p className=\"mb-0\">{formData.courseDescription || 'Not specified'}</p>\r\n                </div>\r\n                <div className=\"col-md-4 mb-3\">\r\n                  <label className=\"form-label text-muted\">Level</label>\r\n                  <p className=\"mb-0 text-capitalize\">{formData.courseLevel || 'Not specified'}</p>\r\n                </div>\r\n                <div className=\"col-md-4 mb-3\">\r\n                  <label className=\"form-label text-muted\">Language</label>\r\n                  <p className=\"mb-0 text-capitalize\">{formData.courseLanguage || 'Not specified'}</p>\r\n                </div>\r\n                <div className=\"col-md-4 mb-3\">\r\n                  <label className=\"form-label text-muted\">Certificate</label>\r\n                  <p className=\"mb-0\">{formData.certificateType ? `Certificate ID: ${formData.certificateType}` : 'Not specified'}</p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <hr />\r\n\r\n            {/* Course Media Section */}\r\n            <div className=\"mb-4\">\r\n              <h6 className=\"text-primary mb-3\">\r\n                <Icon icon=\"fluent:image-24-regular\" width=\"18\" height=\"18\" className=\"me-2\" />\r\n                Course Media\r\n              </h6>\r\n              <div className=\"row\">\r\n                <div className=\"col-md-6 mb-3\">\r\n                  <label className=\"form-label text-muted\">Course Thumbnail</label>\r\n                  {formData.thumbnailPreview ? (\r\n                    <div>\r\n                      <img\r\n                        src={formData.thumbnailPreview}\r\n                        alt=\"Course Thumbnail\"\r\n                        className=\"img-fluid rounded border\"\r\n                        style={{ maxHeight: '150px', width: 'auto' }}\r\n                      />\r\n                    </div>\r\n                  ) : (\r\n                    <p className=\"mb-0 text-muted\">No thumbnail uploaded</p>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <hr />\r\n\r\n            {/* Additional Information Section */}\r\n            <div className=\"mb-4\">\r\n              <h6 className=\"text-primary mb-3\">\r\n                <Icon icon=\"fluent:tag-24-regular\" width=\"18\" height=\"18\" className=\"me-2\" />\r\n                Additional Information\r\n              </h6>\r\n              <div className=\"row\">\r\n                <div className=\"col-md-6 mb-3\">\r\n                  <label className=\"form-label text-muted\">Tags</label>\r\n                  {formData.tags.length > 0 ? (\r\n                    <div className=\"d-flex flex-wrap gap-2\">\r\n                      {formData.tags.map((tag, index) => (\r\n                        <span key={index} className=\"badge bg-light text-dark border\">\r\n                          {tag}\r\n                        </span>\r\n                      ))}\r\n                    </div>\r\n                  ) : (\r\n                    <p className=\"mb-0 text-muted\">No tags added</p>\r\n                  )}\r\n                </div>\r\n                <div className=\"col-md-6 mb-3\">\r\n                  <label className=\"form-label text-muted\">Course Information Points</label>\r\n                  {formData.courseInfo.length > 0 ? (\r\n                    <ul className=\"list-unstyled mb-0\">\r\n                      {formData.courseInfo.map((info, index) => (\r\n                        <li key={index} className=\"mb-1\">\r\n                          <Icon icon=\"fluent:checkmark-circle-24-regular\" width=\"16\" height=\"16\" className=\"text-success me-2\" />\r\n                          {info}\r\n                        </li>\r\n                      ))}\r\n                    </ul>\r\n                  ) : (\r\n                    <p className=\"mb-0 text-muted\">No course information added</p>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            <hr />\r\n\r\n            {/* Pricing Section */}\r\n            <div className=\"mb-4\">\r\n              <h6 className=\"text-primary mb-3\">\r\n                <Icon icon=\"fluent:payment-24-regular\" width=\"18\" height=\"18\" className=\"me-2\" />\r\n                Pricing Information\r\n              </h6>\r\n              <div className=\"row\">\r\n                <div className=\"col-md-3 mb-3\">\r\n                  <label className=\"form-label text-muted\">Course Type</label>\r\n                  <p className=\"mb-0\">\r\n                    <span className={`badge ${formData.courseType === 'free' ? 'bg-success' : 'bg-primary'}`}>\r\n                      {formData.courseType === 'free' ? 'Free Course' : 'Paid Course'}\r\n                    </span>\r\n                  </p>\r\n                </div>\r\n                {formData.courseType === 'paid' && (\r\n                  <>\r\n                    <div className=\"col-md-3 mb-3\">\r\n                      <label className=\"form-label text-muted\">Currency</label>\r\n                      <p className=\"mb-0\">{formData.currency}</p>\r\n                    </div>\r\n                    <div className=\"col-md-3 mb-3\">\r\n                      <label className=\"form-label text-muted\">Price</label>\r\n                      <p className=\"mb-0 fw-medium\">\r\n                        {formData.currency === 'USD' && '$'}\r\n                        {formData.currency === 'EUR' && '€'}\r\n                        {formData.currency === 'GBP' && '£'}\r\n                        {formData.currency === 'INR' && '₹'}\r\n                        {formData.currency === 'SGD' && 'S$'}\r\n                        {formData.price}\r\n                      </p>\r\n                    </div>\r\n                  </>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const renderPricingStep = () => (\r\n    <div className=\"row\">\r\n      <div className=\"col-12 mb-4\">\r\n        <div className=\"card\">\r\n          <div className=\"card-body\">\r\n  \r\n            <div className=\"mb-4\">\r\n              <label className=\"form-label\">Course Type *</label>\r\n              <div className=\"d-flex gap-4\">\r\n                <div className=\"form-check\">\r\n                  <input\r\n                    type=\"radio\"\r\n                    className=\"form-check-input\"\r\n                    id=\"free\"\r\n                    name=\"courseType\"\r\n                    value=\"free\"\r\n                    checked={formData.courseType === 'free'}\r\n                    onChange={(e) => setFormData(prev => ({\r\n                      ...prev,\r\n                      courseType: e.target.value,\r\n                      price: ''\r\n                      // Removed currency override to keep selected currency\r\n                    }))}\r\n                  />\r\n                  <label className=\"form-check-label\" htmlFor=\"free\">\r\n                    <strong>Free Course</strong>\r\n                    <br />\r\n                    <small className=\"text-muted\">No payment required for enrollment</small>\r\n                  </label>\r\n                </div>\r\n                <div className=\"form-check\">\r\n                  <input\r\n                    type=\"radio\"\r\n                    className=\"form-check-input\"\r\n                    id=\"paid\"\r\n                    name=\"courseType\"\r\n                    value=\"paid\"\r\n                    checked={formData.courseType === 'paid'}\r\n                    onChange={(e) => setFormData(prev => ({\r\n                      ...prev,\r\n                      courseType: e.target.value\r\n                    }))}\r\n                  />\r\n                  <label className=\"form-check-label\" htmlFor=\"paid\">\r\n                    <strong>Paid Course</strong>\r\n                    <br />\r\n                    <small className=\"text-muted\">Students must purchase to access content</small>\r\n                  </label>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Debug: Show current selected currency */}\r\n            <div className=\"mb-3 p-2 bg-light rounded\">\r\n              <small className=\"text-muted\">\r\n                Debug: Selected Currency = \"{formData.currency}\" | Domain = \"{domain}\"\r\n              </small>\r\n            </div>\r\n\r\n            {formData.courseType === 'paid' && (\r\n  <>\r\n    {/* SGD for TPI Domain */}\r\n    {domain === 'lms.tpi.sg' && (\r\n      <div className=\"mb-3\">\r\n        <label className=\"form-label\">Select Currency</label>\r\n        <select\r\n          className=\"form-select\"\r\n          value={formData.currency}\r\n          onChange={(e) =>\r\n            setFormData((prev) => ({\r\n              ...prev,\r\n              currency: e.target.value\r\n            }))\r\n          }\r\n        >\r\n          <option value=\"SGD\">SGD (S$)</option>\r\n        </select>\r\n        <small className=\"text-muted\">\r\n          Note: Only SGD is supported on this Platform.\r\n        </small>\r\n      </div>\r\n    )}\r\n\r\n    {/* INR for NxGen Varsity */}\r\n    {domain === 'lms.nxgenvarsity.com' && (\r\n      <div className=\"mb-3\">\r\n        <label className=\"form-label\">Select Currency</label>\r\n        <select\r\n          className=\"form-select\"\r\n          value={formData.currency}\r\n          onChange={(e) =>\r\n            setFormData((prev) => ({\r\n              ...prev,\r\n              currency: e.target.value\r\n            }))\r\n          }\r\n        >\r\n          <option value=\"INR\">INR (₹)</option>\r\n        </select>\r\n        <small className=\"text-muted\">\r\n          Note: Only INR is supported on this domain.\r\n        </small>\r\n      </div>\r\n    )}\r\n\r\n    {/* INR for Creator Foundation */}\r\n    {domain === 'lms.creatorfoundation.in' && (\r\n      <div className=\"mb-3\">\r\n        <label className=\"form-label\">Select Currency</label>\r\n        <select\r\n          className=\"form-select\"\r\n          value={formData.currency}\r\n          onChange={(e) =>\r\n            setFormData((prev) => ({\r\n              ...prev,\r\n              currency: e.target.value\r\n            }))\r\n          }\r\n        >\r\n          <option value=\"INR\">INR (₹)</option>\r\n        </select>\r\n        <small className=\"text-muted\">\r\n          Note: Only INR is supported on this domain.\r\n        </small>\r\n      </div>\r\n    )}\r\n\r\n    {/* Course Price Input */}\r\n    <div className=\"mb-3\">\r\n      <label className=\"form-label\">Course Price *</label>\r\n      <div className=\"input-group\">\r\n        <span className=\"input-group-text\">\r\n          {formData.currency === 'SGD' && 'S$'}\r\n          {formData.currency === 'INR' && '₹'}\r\n        </span>\r\n        <input\r\n          type=\"number\"\r\n          className=\"form-control\"\r\n          placeholder=\"Enter course price\"\r\n          value={formData.price}\r\n          onChange={(e) =>\r\n            setFormData((prev) => ({\r\n              ...prev,\r\n              price: e.target.value\r\n            }))\r\n          }\r\n          required\r\n        />\r\n      </div>\r\n    </div>\r\n  </>\r\n)}\r\n\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const renderStepContent = () => {\r\n    switch (currentStep) {\r\n      case 1:\r\n        return renderCourseInformationForm();\r\n      case 2:\r\n        return renderCourseMediaForm();\r\n      case 3:\r\n        return renderAdditionalInformationForm();\r\n      case 4:\r\n        return renderPricingStep();\r\n      case 5:\r\n        return renderPreviewStep();\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  // Loading overlay component for submission\r\n  const SubmissionLoadingOverlay = () => {\r\n    const isEditMode = courseId && decodedCourseId;\r\n    return (\r\n      <div className=\"position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center\"\r\n           style={{\r\n             backgroundColor: 'rgba(0, 0, 0, 0.7)',\r\n             zIndex: 9999\r\n           }}>\r\n        <div className=\"text-center text-white\">\r\n          <div className=\"spinner-border text-primary mb-3\" role=\"status\" style={{ width: '3rem', height: '3rem' }}>\r\n            <span className=\"visually-hidden\">Loading...</span>\r\n          </div>\r\n          <h5 className=\"mb-2\">{isEditMode ? 'Updating Your Course...' : 'Creating Your Course...'}</h5>\r\n          <p className=\"mb-0\">Please wait while we process your course information.</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // Loading overlay component for fetching course details\r\n  const CourseLoadingOverlay = () => (\r\n    <div className=\"position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center\"\r\n         style={{\r\n           backgroundColor: 'rgba(0, 0, 0, 0.7)',\r\n           zIndex: 9999\r\n         }}>\r\n      <div className=\"text-center text-white\">\r\n        <div className=\"spinner-border text-primary mb-3\" role=\"status\" style={{ width: '3rem', height: '3rem' }}>\r\n          <span className=\"visually-hidden\">Loading...</span>\r\n        </div>\r\n        <h5 className=\"mb-2\">Loading Course Details...</h5>\r\n        <p className=\"mb-0\">Please wait while we fetch the course information.</p>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <div className=\"container\">\r\n      {/* Loading Overlays */}\r\n      {isSubmitting && <SubmissionLoadingOverlay />}\r\n      {isLoadingCourse && <CourseLoadingOverlay />}\r\n\r\n      {/* Steps Header */}\r\n      <div className=\"stepper-card\">\r\n        <div className=\"stepper-wrapper\">\r\n          {/* Background Line */}\r\n          <div className=\"stepper-line-bg\"></div>\r\n          {/* Progress Line */}\r\n          <div \r\n            className=\"stepper-line-progress\"\r\n            style={{ \r\n              width: `${((currentStep - 1) / (steps.length - 1)) * 100}%`\r\n            }}\r\n          ></div>\r\n          \r\n          {/* Step Indicators */}\r\n          <div className=\"stepper-content\">\r\n            {steps.map((step, index) => (\r\n              <div \r\n                key={step.number} \r\n                className=\"stepper-item\"\r\n              >\r\n                <div \r\n                  className={`stepper-circle ${\r\n                    currentStep > index + 1 ? 'completed' :\r\n                    currentStep === index + 1 ? 'active' : ''\r\n                  }`}\r\n                >\r\n                  {step.number}\r\n                </div>\r\n                <div className={`stepper-label ${currentStep === index + 1 ? 'active' : ''}`}>\r\n                  {step.title}\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Content Area */}\r\n      <div className=\"stepper-card\">\r\n        {renderStepContent()}\r\n      </div>\r\n\r\n      {/* Navigation Buttons */}\r\n      <div className=\"stepper-card\">\r\n        <div className=\"stepper-content\">\r\n          <button\r\n            className=\"btn-previous\"\r\n            onClick={handlePrevious}\r\n            disabled={currentStep === 1 || isSubmitting}\r\n          >\r\n            <Icon icon=\"fluent:arrow-left-24-regular\" width=\"20\" height=\"20\" />\r\n            Previous\r\n          </button>\r\n          {currentStep === steps.length ? (\r\n            <button\r\n              className=\"btn-next\"\r\n              onClick={handleSubmit}\r\n              disabled={!isCurrentStepValid() || isSubmitting}\r\n            >\r\n              {isSubmitting ? (\r\n                <>\r\n                  <div className=\"spinner-border spinner-border-sm me-2\" role=\"status\">\r\n                    <span className=\"visually-hidden\">Loading...</span>\r\n                  </div>\r\n                  {courseId && decodedCourseId ? 'Updating Course...' : 'Creating Course...'}\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <Icon icon=\"fluent:checkmark-24-regular\" width=\"20\" height=\"20\" className=\"me-2\" />\r\n                  {courseId && decodedCourseId ? 'Update Course' : 'Submit Course'}\r\n                </>\r\n              )}\r\n            </button>\r\n          ) : (\r\n            <button\r\n              className=\"btn-next\"\r\n              onClick={handleNext}\r\n              disabled={!isCurrentStepValid()}\r\n            >\r\n              {currentStep === steps.length - 1 ? 'Preview' : 'Next'}\r\n              <Icon icon=\"fluent:arrow-right-24-regular\" width=\"20\" height=\"20\" />\r\n            </button>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default CreateCourse;\r\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,IAAI,QAAQ,gBAAgB;AACrC,SAASC,WAAW,EAAEC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACtE,OAAO,oBAAoB;AAC3B,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,oBAAoB,EAAEC,eAAe,EAAEC,oBAAoB,EAAEC,mBAAmB,QAAQ,gCAAgC;AACjI,OAAOC,oBAAoB,MAAM,sCAAsC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACxE,MAAMC,MAAM,IAAAC,qBAAA,GAAGC,OAAO,CAACC,GAAG,CAACC,oBAAoB,cAAAH,qBAAA,uBAAhCA,qBAAA,CAAkCI,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC;AAEzE,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,gBAAA;EACtB,MAAMC,QAAQ,GAAGxB,WAAW,CAAC,CAAC;EAC9B,MAAMyB,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEwB;EAAS,CAAC,GAAGzB,SAAS,CAAC,CAAC;EAChC;EACA,MAAM0B,eAAe,GAAGD,QAAQ,GAAGvB,UAAU,CAACuB,QAAQ,CAAC,GAAG,IAAI;EAC9D;EACA;;EAEA,MAAME,SAAS,IAAAN,eAAA,GAAGE,QAAQ,CAACK,KAAK,cAAAP,eAAA,uBAAdA,eAAA,CAAgBM,SAAS;EAC3C,MAAME,iBAAiB,IAAAP,gBAAA,GAAGC,QAAQ,CAACK,KAAK,cAAAN,gBAAA,uBAAdA,gBAAA,CAAgBQ,UAAU;EAEpD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGrC,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACsC,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACwC,eAAe,EAAEC,kBAAkB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC0C,QAAQ,EAAEC,WAAW,CAAC,GAAG3C,QAAQ,CAAC;IACvC4C,WAAW,EAAE,EAAE;IACfC,iBAAiB,EAAE,EAAE;IACrBC,WAAW,EAAE,cAAc;IAC3BC,cAAc,EAAE,SAAS;IACzBC,cAAc,EAAE,EAAE;IAClBC,SAAS,EAAE,IAAI;IACfC,gBAAgB,EAAE,EAAE;IACpBC,eAAe,EAAE,EAAE;IACnBC,UAAU,EAAE,MAAM;IAClBC,QAAQ,EAAE,KAAK;IACfC,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,EAAE;IACRC,UAAU,EAAE;EACd,CAAC,CAAC;EAEFvD,SAAS,CAAC,MAAM;IACd,IAAI+B,SAAS,IAAIE,iBAAiB,EAAE;MAClCS,WAAW,CAACT,iBAAiB,CAAC;IAChC;EACF,CAAC,EAAE,CAACF,SAAS,EAAEE,iBAAiB,CAAC,CAAC;;EAElC;EACAjC,SAAS,CAAC,MAAM;IACd,MAAMwD,kBAAkB,GAAG,MAAAA,CAAA,KAAY;MACrC,IAAI3B,QAAQ,IAAIC,eAAe,EAAE;QAC/BU,kBAAkB,CAAC,IAAI,CAAC;QACxB,IAAI;UACFiB,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE5B,eAAe,CAAC;UAC/D,MAAM6B,QAAQ,GAAG,MAAMlD,oBAAoB,CAAC;YAAEmD,SAAS,EAAE9B;UAAgB,CAAC,CAAC;UAE3E2B,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEC,QAAQ,CAAC;UAErD,IAAIA,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,CAACC,MAAM,EAAE;YAAA,IAAAC,qBAAA;YAC5C,MAAMD,MAAM,GAAGJ,QAAQ,CAACG,IAAI,CAACC,MAAM;YACnCN,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEK,MAAM,CAAC;;YAE9C;YACArB,WAAW,CAAC;cACVC,WAAW,EAAEoB,MAAM,CAACE,WAAW,IAAI,EAAE;cACrCrB,iBAAiB,EAAEmB,MAAM,CAACG,WAAW,IAAI,EAAE;cAC3CrB,WAAW,EAAEkB,MAAM,CAACI,MAAM,IAAI,EAAE;cAChCrB,cAAc,EAAEiB,MAAM,CAACK,eAAe,IAAI,EAAE;cAC5CrB,cAAc,EAAEgB,MAAM,CAACM,eAAe,IAAI,EAAE;cAC5CrB,SAAS,EAAE,IAAI;cAAE;cACjBC,gBAAgB,EAAEc,MAAM,CAACO,YAAY,IAAI,EAAE;cAC3CpB,eAAe,EAAE,EAAAc,qBAAA,GAAAD,MAAM,CAACQ,uBAAuB,cAAAP,qBAAA,uBAA9BA,qBAAA,CAAgCQ,QAAQ,CAAC,CAAC,KAAI,EAAE;cACjErB,UAAU,EAAEY,MAAM,CAACU,WAAW,IAAI,MAAM;cACxCrB,QAAQ,EAAEW,MAAM,CAACX,QAAQ,IAAI,KAAK;cAClCC,KAAK,EAAEU,MAAM,CAACW,YAAY,IAAI,EAAE;cAChCpB,IAAI,EAAEqB,KAAK,CAACC,OAAO,CAACb,MAAM,CAACT,IAAI,CAAC,GAAGS,MAAM,CAACT,IAAI,GAAG,EAAE;cACnDC,UAAU,EAAEoB,KAAK,CAACC,OAAO,CAACb,MAAM,CAACc,WAAW,CAAC,GAAGd,MAAM,CAACc,WAAW,GAAG;YACvE,CAAC,CAAC;YAEFpB,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;UAC7C,CAAC,MAAM;YAAA,IAAAoB,cAAA,EAAAC,eAAA;YACLtB,OAAO,CAACuB,KAAK,CAAC,iCAAiC,GAAAF,cAAA,GAAEnB,QAAQ,CAACG,IAAI,cAAAgB,cAAA,uBAAbA,cAAA,CAAeG,SAAS,CAAC;YAC1EC,KAAK,CAAC,iCAAiC,IAAI,EAAAH,eAAA,GAAApB,QAAQ,CAACG,IAAI,cAAAiB,eAAA,uBAAbA,eAAA,CAAeE,SAAS,KAAI,eAAe,CAAC,CAAC;UAC1F;QACF,CAAC,CAAC,OAAOD,KAAK,EAAE;UACdvB,OAAO,CAACuB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;UACtDE,KAAK,CAAC,iDAAiD,CAAC;QAC1D,CAAC,SAAS;UACR1C,kBAAkB,CAAC,KAAK,CAAC;QAC3B;MACF;IACF,CAAC;IAEDgB,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAAC3B,QAAQ,EAAEC,eAAe,CAAC,CAAC;EAE/B,MAAM,CAACqD,eAAe,EAAEC,kBAAkB,CAAC,GAAGrF,QAAQ,CAAC,EAAE,CAAC;EAE1D,MAAMsF,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,MAAM1B,QAAQ,GAAG,MAAMpD,oBAAoB,CAAC,CAAC;MAC7C;MACA,IAAIoD,QAAQ,CAACE,OAAO,EAAE;QACpBuB,kBAAkB,CAACzB,QAAQ,CAACG,IAAI,CAACwB,SAAS,CAAC;MAC7C,CAAC,MAAM;QACL;QACAF,kBAAkB,CAAC,EAAE,CAAC,CAAC,CAAC;MAC1B;IACF,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdvB,OAAO,CAACuB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxDI,kBAAkB,CAAC,EAAE,CAAC,CAAC,CAAC;IAC1B;EACF,CAAC;EAEDpF,SAAS,CAAC,MAAM;IACdqF,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM,CAACE,QAAQ,EAAEC,WAAW,CAAC,GAAGzF,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC0F,SAAS,EAAEC,YAAY,CAAC,GAAG3F,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC4F,WAAW,EAAEC,cAAc,CAAC,GAAG7F,QAAQ,CAAC,IAAI,CAAC;;EAEpD;EACA,MAAM8F,oBAAoB,GAAG5F,WAAW,CAAE6F,CAAC,IAAK;IAC9CN,WAAW,CAACM,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC7B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,qBAAqB,GAAGhG,WAAW,CAAE6F,CAAC,IAAK;IAC/CJ,YAAY,CAACI,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC9B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAME,iBAAiB,GAAGjG,WAAW,CAAE6F,CAAC,IAAK;IAC3C,MAAM;MAAEK,IAAI;MAAEH;IAAM,CAAC,GAAGF,CAAC,CAACC,MAAM;IAChCrD,WAAW,CAAC0D,SAAS,KAAK;MACxB,GAAGA,SAAS;MACZ,CAACD,IAAI,GAAGH;IACV,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,qBAAqB,GAAIP,CAAC,IAAK;IACnC,MAAMQ,IAAI,GAAGR,CAAC,CAACC,MAAM,CAACQ,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAID,IAAI,EAAE;MACR5D,WAAW,CAAC0D,SAAS,KAAK;QACxB,GAAGA,SAAS;QACZpD,SAAS,EAAEsD,IAAI;QACfrD,gBAAgB,EAAEuD,GAAG,CAACC,eAAe,CAACH,IAAI;MAC5C,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMI,gBAAgB,GAAIC,IAAI,IAAK;IACjC,IAAIA,IAAI,KAAK,WAAW,EAAE;MACxBjE,WAAW,CAAC0D,SAAS,KAAK;QACxB,GAAGA,SAAS;QACZpD,SAAS,EAAE,IAAI;QACfC,gBAAgB,EAAE;MACpB,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAM2D,YAAY,GAAG3G,WAAW,CAAE6F,CAAC,IAAK;IACtCA,CAAC,CAACe,cAAc,CAAC,CAAC;IAClB,MAAMC,GAAG,GAAGvB,QAAQ,CAACjE,IAAI,CAAC,CAAC;IAC3B,IAAIwF,GAAG,IAAI,CAACrE,QAAQ,CAACa,IAAI,CAACyD,QAAQ,CAACD,GAAG,CAAC,EAAE;MACvC,IAAIA,GAAG,CAACE,MAAM,GAAG,CAAC,EAAE;QAClB9B,KAAK,CAAC,wCAAwC,CAAC;QAC/C;MACF;MACA,IAAIzC,QAAQ,CAACa,IAAI,CAAC0D,MAAM,IAAI,CAAC,EAAE;QAC7B9B,KAAK,CAAC,wBAAwB,CAAC;QAC/B;MACF;MACAxC,WAAW,CAACuE,IAAI,KAAK;QACnB,GAAGA,IAAI;QACP3D,IAAI,EAAE,CAAC,GAAG2D,IAAI,CAAC3D,IAAI,EAAEwD,GAAG;MAC1B,CAAC,CAAC,CAAC;MACHtB,WAAW,CAAC,EAAE,CAAC;IACjB;EACF,CAAC,EAAE,CAACD,QAAQ,EAAE9C,QAAQ,CAACa,IAAI,CAAC,CAAC;EAE7B,MAAM4D,eAAe,GAAIC,WAAW,IAAK;IACvCzE,WAAW,CAACuE,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP3D,IAAI,EAAE2D,IAAI,CAAC3D,IAAI,CAAC8D,MAAM,CAACN,GAAG,IAAIA,GAAG,KAAKK,WAAW;IACnD,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,qBAAqB,GAAIvB,CAAC,IAAK;IACnC,IAAIA,CAAC,CAACwB,GAAG,KAAK,OAAO,EAAE;MACrBxB,CAAC,CAACe,cAAc,CAAC,CAAC;MAClBD,YAAY,CAACd,CAAC,CAAC;IACjB;EACF,CAAC;EAED,MAAMyB,aAAa,GAAGtH,WAAW,CAAE6F,CAAC,IAAK;IACvCA,CAAC,CAACe,cAAc,CAAC,CAAC;IAClB,IAAIpB,SAAS,CAACnE,IAAI,CAAC,CAAC,EAAE;MACpB,IAAIqE,WAAW,KAAK,IAAI,EAAE;QACxBjD,WAAW,CAACuE,IAAI,KAAK;UACnB,GAAGA,IAAI;UACP1D,UAAU,EAAE0D,IAAI,CAAC1D,UAAU,CAACiE,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAC1CA,KAAK,KAAK/B,WAAW,GAAGF,SAAS,CAACnE,IAAI,CAAC,CAAC,GAAGmG,IAC7C;QACF,CAAC,CAAC,CAAC;QACH7B,cAAc,CAAC,IAAI,CAAC;MACtB,CAAC,MAAM;QACLlD,WAAW,CAACuE,IAAI,KAAK;UACnB,GAAGA,IAAI;UACP1D,UAAU,EAAE,CAAC,GAAG0D,IAAI,CAAC1D,UAAU,EAAEkC,SAAS,CAACnE,IAAI,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;MACL;MACAoE,YAAY,CAAC,EAAE,CAAC;IAClB;EACF,CAAC,EAAE,CAACD,SAAS,EAAEE,WAAW,CAAC,CAAC;EAE5B,MAAMgC,cAAc,GAAID,KAAK,IAAK;IAChChC,YAAY,CAACjD,QAAQ,CAACc,UAAU,CAACmE,KAAK,CAAC,CAAC;IACxC9B,cAAc,CAAC8B,KAAK,CAAC;EACvB,CAAC;EAED,MAAME,gBAAgB,GAAIF,KAAK,IAAK;IAClChF,WAAW,CAACuE,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP1D,UAAU,EAAE0D,IAAI,CAAC1D,UAAU,CAAC6D,MAAM,CAAC,CAACS,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKJ,KAAK;IAC1D,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMK,sBAAsB,GAAIjC,CAAC,IAAK;IACpC,IAAIA,CAAC,CAACwB,GAAG,KAAK,OAAO,EAAE;MACrBxB,CAAC,CAACe,cAAc,CAAC,CAAC;MAClBU,aAAa,CAACzB,CAAC,CAAC;IAClB;EACF,CAAC;EAED,MAAMkC,KAAK,GAAG,CACZ;IAAEC,MAAM,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAqB,CAAC,EAC7C;IAAED,MAAM,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAe,CAAC,EACvC;IAAED,MAAM,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAyB,CAAC,EACjD;IAAED,MAAM,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAU,CAAC,EAClC;IAAED,MAAM,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAmB,CAAC,CAC5C;;EAED;EACA,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B,OAAO1F,QAAQ,CAACE,WAAW,CAACrB,IAAI,CAAC,CAAC,KAAK,EAAE,IAClCmB,QAAQ,CAACG,iBAAiB,CAACtB,IAAI,CAAC,CAAC,KAAK,EAAE,IACxCmB,QAAQ,CAACI,WAAW,KAAK,EAAE,IAC3BJ,QAAQ,CAACK,cAAc,KAAK,EAAE,IAC9BL,QAAQ,CAACM,cAAc,KAAK,EAAE;EACvC,CAAC;EAED,MAAMqF,aAAa,GAAGA,CAAA,KAAM;IAC1B;IACA,IAAIvG,QAAQ,IAAIC,eAAe,EAAE;MAC/B,OAAO,CAACW,QAAQ,CAACO,SAAS,KAAK,IAAI,IAAIP,QAAQ,CAACQ,gBAAgB,KAAK,EAAE,KAChER,QAAQ,CAACS,eAAe,KAAK,EAAE;IACxC;IACA;IACA,OAAOT,QAAQ,CAACO,SAAS,KAAK,IAAI,IAAIP,QAAQ,CAACS,eAAe,KAAK,EAAE;EACvE,CAAC;EAED,MAAMmF,aAAa,GAAGA,CAAA,KAAM;IAC1B;IACA,IAAIxG,QAAQ,IAAIC,eAAe,EAAE;MAC/B,OAAO,IAAI,CAAC,CAAC;IACf;IACA;IACA,OAAOW,QAAQ,CAACa,IAAI,CAAC0D,MAAM,GAAG,CAAC,IAAIvE,QAAQ,CAACc,UAAU,CAACyD,MAAM,GAAG,CAAC;EACnE,CAAC;EAED,MAAMsB,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI7F,QAAQ,CAACU,UAAU,KAAK,MAAM,EAAE;MAClC,OAAO,IAAI;IACb;IACA,OAAOV,QAAQ,CAACY,KAAK,KAAK,EAAE,IAAIkF,UAAU,CAAC9F,QAAQ,CAACY,KAAK,CAAC,GAAG,CAAC;EAChE,CAAC;EAED,MAAMmF,aAAa,GAAGA,CAAA,KAAM;IAC1B;IACA,OAAOL,aAAa,CAAC,CAAC,IAAIC,aAAa,CAAC,CAAC,IAAIC,aAAa,CAAC,CAAC,IAAIC,aAAa,CAAC,CAAC;EACjF,CAAC;EAED,MAAMG,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,QAAQtG,WAAW;MACjB,KAAK,CAAC;QACJ,OAAOgG,aAAa,CAAC,CAAC;MACxB,KAAK,CAAC;QACJ,OAAOC,aAAa,CAAC,CAAC;MACxB,KAAK,CAAC;QACJ,OAAOC,aAAa,CAAC,CAAC;MACxB,KAAK,CAAC;QACJ,OAAOC,aAAa,CAAC,CAAC;MACxB,KAAK,CAAC;QACJ,OAAOE,aAAa,CAAC,CAAC;MACxB;QACE,OAAO,KAAK;IAChB;EACF,CAAC;EAED,MAAME,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIvG,WAAW,GAAG6F,KAAK,CAAChB,MAAM,EAAE;MAC9B5E,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;IACjC;EACF,CAAC;EAED,MAAMwG,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIxG,WAAW,GAAG,CAAC,EAAE;MACnBC,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;IACjC;EACF,CAAC;EAED,MAAMyG,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAIH,kBAAkB,CAAC,CAAC,EAAE;MACxBnG,eAAe,CAAC,IAAI,CAAC;MAErB,IAAI;QACF;QACA,MAAMuG,cAAc,GAAG,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;QAExE;QACA,MAAME,UAAU,GAAG,IAAIC,QAAQ,CAAC,CAAC;;QAEjC;QACAD,UAAU,CAACE,MAAM,CAAC,aAAa,EAAE1G,QAAQ,CAACE,WAAW,CAAC;QACtDsG,UAAU,CAACE,MAAM,CAAC,mBAAmB,EAAE1G,QAAQ,CAACG,iBAAiB,CAAC;QAClEqG,UAAU,CAACE,MAAM,CAAC,aAAa,EAAE1G,QAAQ,CAACI,WAAW,CAAC;QACtDoG,UAAU,CAACE,MAAM,CAAC,gBAAgB,EAAE1G,QAAQ,CAACK,cAAc,CAAC;QAC5DmG,UAAU,CAACE,MAAM,CAAC,gBAAgB,EAAE1G,QAAQ,CAACM,cAAc,CAAC;QAC5DkG,UAAU,CAACE,MAAM,CAAC,iBAAiB,EAAE1G,QAAQ,CAACS,eAAe,CAAC;QAC9D+F,UAAU,CAACE,MAAM,CAAC,YAAY,EAAE1G,QAAQ,CAACU,UAAU,CAAC;QACpD8F,UAAU,CAACE,MAAM,CAAC,UAAU,EAAE1G,QAAQ,CAACW,QAAQ,CAAC;QAChD6F,UAAU,CAACE,MAAM,CAAC,OAAO,EAAE1G,QAAQ,CAACY,KAAK,CAAC;QAC1C4F,UAAU,CAACE,MAAM,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAAC5G,QAAQ,CAACa,IAAI,CAAC,CAAC;QACxD2F,UAAU,CAACE,MAAM,CAAC,YAAY,EAAEC,IAAI,CAACC,SAAS,CAAC5G,QAAQ,CAACc,UAAU,CAAC,CAAC;;QAEpE;QACA,KAAK,MAAM,CAAC+D,GAAG,EAAEtB,KAAK,CAAC,IAAIsD,MAAM,CAACC,OAAO,CAAC9G,QAAQ,CAAC,EAAE;UACnDgB,OAAO,CAACC,GAAG,CAAC,GAAG4D,GAAG,KAAKtB,KAAK,EAAE,CAAC;QACjC;;QAEA;QACA,IAAIvD,QAAQ,CAACO,SAAS,EAAE;UACtBiG,UAAU,CAACE,MAAM,CAAC,cAAc,EAAE1G,QAAQ,CAACO,SAAS,CAAC;QACvD;QAEAS,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxCD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE7B,QAAQ,IAAIC,eAAe,CAAC;;QAE5D;QACA,IAAI0H,OAAO;QACX,IAAI3H,QAAQ,IAAIC,eAAe,EAAE;UAC/B;UACA0H,OAAO,GAAG9I,mBAAmB,CAACoB,eAAe,EAAEmH,UAAU,CAAC;QAC5D,CAAC,MAAM;UACL;UACAO,OAAO,GAAGhJ,eAAe,CAACyI,UAAU,CAAC;QACvC;;QAEA;QACA,MAAM,CAACtF,QAAQ,CAAC,GAAG,MAAMmF,OAAO,CAACW,GAAG,CAAC,CAACD,OAAO,EAAEX,cAAc,CAAC,CAAC;QAE/D,IAAIlF,QAAQ,CAACE,OAAO,EAAE;UACpBJ,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEC,QAAQ,CAACG,IAAI,CAAC;UAC1D;UACAlC,QAAQ,CAAC,gBAAgB,CAAC;QAC5B,CAAC,MAAM;UAAA,IAAA8H,eAAA;UACLpH,eAAe,CAAC,KAAK,CAAC;UACtB,MAAMqH,QAAQ,GAAG9H,QAAQ,IAAIC,eAAe,GAAG,yBAAyB,GAAG,yBAAyB;UACpGoD,KAAK,CAACyE,QAAQ,GAAG,IAAI,IAAI,EAAAD,eAAA,GAAA/F,QAAQ,CAACG,IAAI,cAAA4F,eAAA,uBAAbA,eAAA,CAAezE,SAAS,KAAI,eAAe,CAAC,CAAC;QACxE;MACF,CAAC,CAAC,OAAOD,KAAK,EAAE;QACd1C,eAAe,CAAC,KAAK,CAAC;QACtBmB,OAAO,CAACuB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD,MAAM2E,QAAQ,GAAG9H,QAAQ,IAAIC,eAAe,GAAG,uBAAuB,GAAG,uBAAuB;QAChGoD,KAAK,CAACyE,QAAQ,GAAG,qBAAqB,CAAC;MACzC;IACF,CAAC,MAAM;MACLzE,KAAK,CAAC,uDAAuD,CAAC;IAChE;EACF,CAAC;EAED,MAAM0E,2BAA2B,GAAGA,CAAA,kBAClC/I,OAAA;IAAKgJ,SAAS,EAAC,oBAAoB;IAAAC,QAAA,eACjCjJ,OAAA;MAAKgJ,SAAS,EAAC,KAAK;MAAAC,QAAA,gBAClBjJ,OAAA;QAAKgJ,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BjJ,OAAA;UAAOkJ,OAAO,EAAC,aAAa;UAACF,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1EtJ,OAAA;UACE8F,IAAI,EAAC,MAAM;UACXkD,SAAS,EAAC,cAAc;UACxBO,EAAE,EAAC,aAAa;UAChBjE,IAAI,EAAC,aAAa;UAClBkE,WAAW,EAAC,oBAAoB;UAChCrE,KAAK,EAAEvD,QAAQ,CAACE,WAAY;UAC5B2H,QAAQ,EAAEpE,iBAAkB;UAC5BqE,SAAS,EAAE,GAAI;UACfC,QAAQ;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFtJ,OAAA;UAAOgJ,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAwF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3H,CAAC,eAENtJ,OAAA;QAAKgJ,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BjJ,OAAA;UAAOkJ,OAAO,EAAC,mBAAmB;UAACF,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAoB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACtFtJ,OAAA;UACEgJ,SAAS,EAAC,cAAc;UACxBO,EAAE,EAAC,mBAAmB;UACtBjE,IAAI,EAAC,mBAAmB;UACxBsE,IAAI,EAAC,GAAG;UACRJ,WAAW,EAAC,0BAA0B;UACtCrE,KAAK,EAAEvD,QAAQ,CAACG,iBAAkB;UAClC0H,QAAQ,EAAEpE,iBAAkB;UAC5BqE,SAAS,EAAE,IAAK;UAChBC,QAAQ;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACZtJ,OAAA;UAAOgJ,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAA+B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC,eAENtJ,OAAA;QAAKgJ,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BjJ,OAAA;UAAOkJ,OAAO,EAAC,aAAa;UAACF,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1EtJ,OAAA;UACEgJ,SAAS,EAAC,aAAa;UACvBO,EAAE,EAAC,aAAa;UAChBjE,IAAI,EAAC,aAAa;UAClBH,KAAK,EAAEvD,QAAQ,CAACI,WAAY;UAC5ByH,QAAQ,EAAEpE,iBAAkB;UAC5BsE,QAAQ;UAAAV,QAAA,gBAERjJ,OAAA;YAAQmF,KAAK,EAAC,EAAE;YAAA8D,QAAA,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtCtJ,OAAA;YAAQmF,KAAK,EAAC,UAAU;YAAA8D,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1CtJ,OAAA;YAAQmF,KAAK,EAAC,cAAc;YAAA8D,QAAA,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAClDtJ,OAAA;YAAQmF,KAAK,EAAC,UAAU;YAAA8D,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1CtJ,OAAA;YAAQmF,KAAK,EAAC,YAAY;YAAA8D,QAAA,EAAC;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENtJ,OAAA;QAAKgJ,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BjJ,OAAA;UAAOkJ,OAAO,EAAC,gBAAgB;UAACF,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAChFtJ,OAAA;UACEgJ,SAAS,EAAC,aAAa;UACvBO,EAAE,EAAC,gBAAgB;UACnBjE,IAAI,EAAC,gBAAgB;UACrBH,KAAK,EAAEvD,QAAQ,CAACK,cAAe;UAC/BwH,QAAQ,EAAEpE,iBAAkB;UAC5BsE,QAAQ;UAAAV,QAAA,gBAERjJ,OAAA;YAAQmF,KAAK,EAAC,EAAE;YAAA8D,QAAA,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACzCtJ,OAAA;YAAQmF,KAAK,EAAC,SAAS;YAAA8D,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxCtJ,OAAA;YAAQmF,KAAK,EAAC,SAAS;YAAA8D,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxCtJ,OAAA;YAAQmF,KAAK,EAAC,QAAQ;YAAA8D,QAAA,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtCtJ,OAAA;YAAQmF,KAAK,EAAC,QAAQ;YAAA8D,QAAA,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtCtJ,OAAA;YAAQmF,KAAK,EAAC,SAAS;YAAA8D,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxCtJ,OAAA;YAAQmF,KAAK,EAAC,UAAU;YAAA8D,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1CtJ,OAAA;YAAQmF,KAAK,EAAC,OAAO;YAAA8D,QAAA,EAAC;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENtJ,OAAA;QAAKgJ,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BjJ,OAAA;UAAOkJ,OAAO,EAAC,gBAAgB;UAACF,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAChFtJ,OAAA;UACEgJ,SAAS,EAAC,aAAa;UACvBO,EAAE,EAAC,gBAAgB;UACnBjE,IAAI,EAAC,gBAAgB;UACrBH,KAAK,EAAEvD,QAAQ,CAACM,cAAe;UAC/BuH,QAAQ,EAAEpE,iBAAkB;UAC5BsE,QAAQ;UAAAV,QAAA,gBAERjJ,OAAA;YAAQmF,KAAK,EAAC,EAAE;YAAA8D,QAAA,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACxCxJ,oBAAoB,CAAC+J,UAAU,CAAClD,GAAG,CAAC,CAACmD,QAAQ,EAAEjD,KAAK,kBACnD7G,OAAA;YAAoBmF,KAAK,EAAE2E,QAAS;YAAAb,QAAA,EACjCa;UAAQ,GADEjD,KAAK;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEV,CACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMS,qBAAqB,GAAGA,CAAA,kBAC5B/J,OAAA;IAAKgJ,SAAS,EAAC,cAAc;IAAAC,QAAA,eAC3BjJ,OAAA;MAAKgJ,SAAS,EAAC,KAAK;MAAAC,QAAA,gBAElBjJ,OAAA;QAAKgJ,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BjJ,OAAA;UAAOgJ,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAkB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxDtJ,OAAA;UAAKgJ,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBjJ,OAAA;YAAKgJ,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvB,CAACrH,QAAQ,CAACQ,gBAAgB,gBACzBpC,OAAA;cAAKgJ,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BjJ,OAAA;gBACE8F,IAAI,EAAC,MAAM;gBACXyD,EAAE,EAAC,WAAW;gBACdP,SAAS,EAAC,QAAQ;gBAClBgB,MAAM,EAAC,SAAS;gBAChBP,QAAQ,EAAEjE;cAAsB;gBAAA2D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACFtJ,OAAA;gBACEkJ,OAAO,EAAC,WAAW;gBACnBF,SAAS,EAAC,kFAAkF;gBAC5FiB,KAAK,EAAE;kBAAEC,KAAK,EAAE,aAAa;kBAAEC,MAAM,EAAE;gBAAU,CAAE;gBAAAlB,QAAA,gBAEnDjJ,OAAA,CAACX,IAAI;kBAAC+K,IAAI,EAAC,6BAA6B;kBAACF,KAAK,EAAC,IAAI;kBAACG,MAAM,EAAC;gBAAI;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAClEtJ,OAAA;kBAAAiJ,QAAA,EAAM;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC7BtJ,OAAA;kBAAOgJ,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAA4B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,gBAENtJ,OAAA;cAAKgJ,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCjJ,OAAA;gBACEsK,GAAG,EAAE1I,QAAQ,CAACQ,gBAAiB;gBAC/BmI,GAAG,EAAC,kBAAkB;gBACtBvB,SAAS,EAAC,mBAAmB;gBAC7BiB,KAAK,EAAE;kBAAEC,KAAK,EAAE,MAAM;kBAAEG,MAAM,EAAE;gBAAO;cAAE;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eACFtJ,OAAA;gBACEgJ,SAAS,EAAC,uFAAuF;gBACjGwB,OAAO,EAAEA,CAAA,KAAM3E,gBAAgB,CAAC,WAAW,CAAE;gBAC7CC,IAAI,EAAC,QAAQ;gBACbmE,KAAK,EAAE;kBACLC,KAAK,EAAE,MAAM;kBACbG,MAAM,EAAE,MAAM;kBACdI,GAAG,EAAE,KAAK;kBACVC,KAAK,EAAE,KAAK;kBACZC,QAAQ,EAAE,OAAO;kBACjBC,YAAY,EAAE;gBAChB,CAAE;gBAAA3B,QAAA,eAEFjJ,OAAA,CAACX,IAAI;kBAAC+K,IAAI,EAAC,0BAA0B;kBAACF,KAAK,EAAC,IAAI;kBAACG,MAAM,EAAC;gBAAI;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtJ,OAAA;QAAKgJ,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BjJ,OAAA;UAAOgJ,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAkB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxDtJ,OAAA;UAAKgJ,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBjJ,OAAA;YAAKgJ,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBjJ,OAAA;cACEgJ,SAAS,EAAC,kBAAkB;cAC5B1D,IAAI,EAAC,iBAAiB;cACtBH,KAAK,EAAEvD,QAAQ,CAACS,eAAgB;cAChCoH,QAAQ,EAAEpE,iBAAkB;cAC5BsE,QAAQ;cAAAV,QAAA,gBAERjJ,OAAA;gBAAQmF,KAAK,EAAC,EAAE;gBAAA8D,QAAA,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAChDxF,KAAK,CAACC,OAAO,CAACO,eAAe,CAAC,IAAIA,eAAe,CAACqC,GAAG,CAAEkE,IAAI,iBAC1D7K,OAAA;gBAAkCmF,KAAK,EAAE0F,IAAI,CAACtB,EAAE,IAAIsB,IAAI,CAACC,GAAI;gBAAA7B,QAAA,EAC1D4B,IAAI,CAACE,aAAa,IAAI;cAAqB,GADjCF,IAAI,CAACtB,EAAE,IAAIsB,IAAI,CAACC,GAAG;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAExB,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,EAER1H,QAAQ,CAACS,eAAe,iBACvBrC,OAAA;cAAKgJ,SAAS,EAAC,0CAA0C;cAAAC,QAAA,eACvDjJ,OAAA;gBAAKgJ,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,gBAC9CjJ,OAAA,CAACX,IAAI;kBAAC+K,IAAI,EAAC,+BAA+B;kBAACF,KAAK,EAAC,IAAI;kBAACG,MAAM,EAAC,IAAI;kBAACrB,SAAS,EAAC;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC7FtJ,OAAA;kBAAAiJ,QAAA,gBACEjJ,OAAA;oBAAIgJ,SAAS,EAAC,MAAM;oBAAAC,QAAA,GAAErH,QAAQ,CAACS,eAAe,CAAC2I,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGrJ,QAAQ,CAACS,eAAe,CAAC6I,KAAK,CAAC,CAAC,CAAC,EAAC,cAAY;kBAAA;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5HtJ,OAAA;oBAAOgJ,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAqC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAM6B,+BAA+B,GAAGA,CAAA,kBACtCnL,OAAA;IAAKgJ,SAAS,EAAC,KAAK;IAAAC,QAAA,eAClBjJ,OAAA;MAAKgJ,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1BjJ,OAAA;QAAKgJ,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBjJ,OAAA;UAAKgJ,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBjJ,OAAA;YAAKgJ,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBjJ,OAAA;cAAOgJ,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnDtJ,OAAA;cAAKgJ,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBjJ,OAAA;gBAAKgJ,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBACtBjJ,OAAA;kBAAKgJ,SAAS,EAAC,QAAQ;kBAAAC,QAAA,eACrBjJ,OAAA;oBACE8F,IAAI,EAAC,MAAM;oBACXkD,SAAS,EAAC,sCAAsC;oBAChDQ,WAAW,EAAC,mDAAmD;oBAC/DrE,KAAK,EAAET,QAAS;oBAChB+E,QAAQ,EAAEzE,oBAAqB;oBAC/BoG,SAAS,EAAE5E;kBAAsB;oBAAA2C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNtJ,OAAA;kBAAKgJ,SAAS,EAAC,OAAO;kBAAAC,QAAA,eACpBjJ,OAAA;oBACEgJ,SAAS,EAAC,6CAA6C;oBACvDwB,OAAO,EAAEzE,YAAa;oBAAAkD,QAAA,EACvB;kBAED;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNtJ,OAAA;gBAAKgJ,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBjJ,OAAA;kBAAOgJ,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAC;gBAAmC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtFtJ,OAAA;kBAAOgJ,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,GAAC,qCAEpC,eAAAjJ,OAAA;oBAAAmJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,iCAEN,eAAAtJ,OAAA;oBAAAmJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,WACJ,EAAC,CAAC,GAAG1H,QAAQ,CAACa,IAAI,CAAC0D,MAAM,EAAC,iBAC9B;gBAAA;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNtJ,OAAA;cAAKgJ,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EACzCrH,QAAQ,CAACa,IAAI,CAACkE,GAAG,CAAC,CAACV,GAAG,EAAEY,KAAK,kBAC5B7G,OAAA;gBAEEgJ,SAAS,EAAC,2EAA2E;gBAAAC,QAAA,GAEpFhD,GAAG,eACJjG,OAAA;kBACE8F,IAAI,EAAC,QAAQ;kBACbkD,SAAS,EAAC,wDAAwD;kBAClEwB,OAAO,EAAEA,CAAA,KAAMnE,eAAe,CAACJ,GAAG,CAAE;kBACpCgE,KAAK,EAAE;oBAAEU,QAAQ,EAAE,MAAM;oBAAEN,MAAM,EAAE;kBAAO,CAAE;kBAAApB,QAAA,eAE5CjJ,OAAA,CAACX,IAAI;oBAAC+K,IAAI,EAAC,2BAA2B;oBAACF,KAAK,EAAC,IAAI;oBAACG,MAAM,EAAC;kBAAI;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CAAC;cAAA,GAXJzC,KAAK;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAYP,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtJ,OAAA;YAAKgJ,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBjJ,OAAA;cAAOgJ,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnDtJ,OAAA;cAAKgJ,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBjJ,OAAA;gBAAKgJ,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBACtBjJ,OAAA;kBAAKgJ,SAAS,EAAC,QAAQ;kBAAAC,QAAA,eACrBjJ,OAAA;oBACE8F,IAAI,EAAC,MAAM;oBACXkD,SAAS,EAAC,sCAAsC;oBAChDQ,WAAW,EAAC,4EAA4E;oBACxFrE,KAAK,EAAEP,SAAU;oBACjB6E,QAAQ,EAAErE,qBAAsB;oBAChCgG,SAAS,EAAElE;kBAAuB;oBAAAiC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNtJ,OAAA;kBAAKgJ,SAAS,EAAC,OAAO;kBAAAC,QAAA,eACpBjJ,OAAA;oBACEgJ,SAAS,EAAC,6CAA6C;oBACvDwB,OAAO,EAAE9D,aAAc;oBAAAuC,QAAA,EAEtBnE,WAAW,KAAK,IAAI,GAAG,QAAQ,GAAG;kBAAU;oBAAAqE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNtJ,OAAA;gBAAKgJ,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnBjJ,OAAA;kBAAOgJ,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAC;gBAEtC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENtJ,OAAA;cAAKgJ,SAAS,EAAC,MAAM;cAAAC,QAAA,EAClBrH,QAAQ,CAACc,UAAU,CAACiE,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACnC7G,OAAA;gBAEEgJ,SAAS,EAAC,yDAAyD;gBAAAC,QAAA,gBAEnEjJ,OAAA,CAACX,IAAI;kBACH+K,IAAI,EAAC,wBAAwB;kBAC7BpB,SAAS,EAAC,4BAA4B;kBACtCkB,KAAK,EAAC,IAAI;kBACVG,MAAM,EAAC;gBAAI;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACFtJ,OAAA;kBAAMgJ,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAErC;gBAAI;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3CtJ,OAAA;kBAAKgJ,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3BjJ,OAAA;oBACE8F,IAAI,EAAC,QAAQ;oBACbkD,SAAS,EAAC,yDAAyD;oBACnEwB,OAAO,EAAEA,CAAA,KAAM1D,cAAc,CAACD,KAAK,CAAE;oBACrCoD,KAAK,EAAE;sBAAEU,QAAQ,EAAE,MAAM;sBAAEN,MAAM,EAAE;oBAAO,CAAE;oBAAApB,QAAA,eAE5CjJ,OAAA,CAACX,IAAI;sBAAC+K,IAAI,EAAC,wBAAwB;sBAACF,KAAK,EAAC,IAAI;sBAACG,MAAM,EAAC;oBAAI;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD,CAAC,eACTtJ,OAAA;oBACE8F,IAAI,EAAC,QAAQ;oBACbkD,SAAS,EAAC,wDAAwD;oBAClEwB,OAAO,EAAEA,CAAA,KAAMzD,gBAAgB,CAACF,KAAK,CAAE;oBACvCoD,KAAK,EAAE;sBAAEU,QAAQ,EAAE,MAAM;sBAAEN,MAAM,EAAE;oBAAO,CAAE;oBAAApB,QAAA,eAE5CjJ,OAAA,CAACX,IAAI;sBAAC+K,IAAI,EAAC,0BAA0B;sBAACF,KAAK,EAAC,IAAI;sBAACG,MAAM,EAAC;oBAAI;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA,GA3BDzC,KAAK;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA4BP,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAM+B,iBAAiB,GAAGA,CAAA,kBACxBrL,OAAA;IAAKgJ,SAAS,EAAC,KAAK;IAAAC,QAAA,eAClBjJ,OAAA;MAAKgJ,SAAS,EAAC,QAAQ;MAAAC,QAAA,eACrBjJ,OAAA;QAAKgJ,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBjJ,OAAA;UAAKgJ,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BjJ,OAAA;YAAIgJ,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAClBjJ,OAAA,CAACX,IAAI;cAAC+K,IAAI,EAAC,uBAAuB;cAACF,KAAK,EAAC,IAAI;cAACG,MAAM,EAAC,IAAI;cAACrB,SAAS,EAAC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,kBAE/E;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLtJ,OAAA;YAAGgJ,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAC;UAEpC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNtJ,OAAA;UAAKgJ,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAExBjJ,OAAA;YAAKgJ,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBjJ,OAAA;cAAIgJ,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC/BjJ,OAAA,CAACX,IAAI;gBAAC+K,IAAI,EAAC,wBAAwB;gBAACF,KAAK,EAAC,IAAI;gBAACG,MAAM,EAAC,IAAI;gBAACrB,SAAS,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,sBAEhF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLtJ,OAAA;cAAKgJ,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClBjJ,OAAA;gBAAKgJ,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BjJ,OAAA;kBAAOgJ,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7DtJ,OAAA;kBAAGgJ,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAErH,QAAQ,CAACE,WAAW,IAAI;gBAAe;kBAAAqH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE,CAAC,eACNtJ,OAAA;gBAAKgJ,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BjJ,OAAA;kBAAOgJ,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAChEtJ,OAAA;kBAAGgJ,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAErH,QAAQ,CAACM,cAAc,IAAI;gBAAe;kBAAAiH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC,eACNtJ,OAAA;gBAAKgJ,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BjJ,OAAA;kBAAOgJ,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnEtJ,OAAA;kBAAGgJ,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAErH,QAAQ,CAACG,iBAAiB,IAAI;gBAAe;kBAAAoH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC,eACNtJ,OAAA;gBAAKgJ,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BjJ,OAAA;kBAAOgJ,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtDtJ,OAAA;kBAAGgJ,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,EAAErH,QAAQ,CAACI,WAAW,IAAI;gBAAe;kBAAAmH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC,eACNtJ,OAAA;gBAAKgJ,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BjJ,OAAA;kBAAOgJ,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzDtJ,OAAA;kBAAGgJ,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,EAAErH,QAAQ,CAACK,cAAc,IAAI;gBAAe;kBAAAkH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF,CAAC,eACNtJ,OAAA;gBAAKgJ,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BjJ,OAAA;kBAAOgJ,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5DtJ,OAAA;kBAAGgJ,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAErH,QAAQ,CAACS,eAAe,GAAG,mBAAmBT,QAAQ,CAACS,eAAe,EAAE,GAAG;gBAAe;kBAAA8G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtJ,OAAA;YAAAmJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAGNtJ,OAAA;YAAKgJ,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBjJ,OAAA;cAAIgJ,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC/BjJ,OAAA,CAACX,IAAI;gBAAC+K,IAAI,EAAC,yBAAyB;gBAACF,KAAK,EAAC,IAAI;gBAACG,MAAM,EAAC,IAAI;gBAACrB,SAAS,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEjF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLtJ,OAAA;cAAKgJ,SAAS,EAAC,KAAK;cAAAC,QAAA,eAClBjJ,OAAA;gBAAKgJ,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BjJ,OAAA;kBAAOgJ,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EAChE1H,QAAQ,CAACQ,gBAAgB,gBACxBpC,OAAA;kBAAAiJ,QAAA,eACEjJ,OAAA;oBACEsK,GAAG,EAAE1I,QAAQ,CAACQ,gBAAiB;oBAC/BmI,GAAG,EAAC,kBAAkB;oBACtBvB,SAAS,EAAC,0BAA0B;oBACpCiB,KAAK,EAAE;sBAAEqB,SAAS,EAAE,OAAO;sBAAEpB,KAAK,EAAE;oBAAO;kBAAE;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,gBAENtJ,OAAA;kBAAGgJ,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CACxD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtJ,OAAA;YAAAmJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAGNtJ,OAAA;YAAKgJ,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBjJ,OAAA;cAAIgJ,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC/BjJ,OAAA,CAACX,IAAI;gBAAC+K,IAAI,EAAC,uBAAuB;gBAACF,KAAK,EAAC,IAAI;gBAACG,MAAM,EAAC,IAAI;gBAACrB,SAAS,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,0BAE/E;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLtJ,OAAA;cAAKgJ,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClBjJ,OAAA;gBAAKgJ,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BjJ,OAAA;kBAAOgJ,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EACpD1H,QAAQ,CAACa,IAAI,CAAC0D,MAAM,GAAG,CAAC,gBACvBnG,OAAA;kBAAKgJ,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,EACpCrH,QAAQ,CAACa,IAAI,CAACkE,GAAG,CAAC,CAACV,GAAG,EAAEY,KAAK,kBAC5B7G,OAAA;oBAAkBgJ,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAC1DhD;kBAAG,GADKY,KAAK;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEV,CACP;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,gBAENtJ,OAAA;kBAAGgJ,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAChD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNtJ,OAAA;gBAAKgJ,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BjJ,OAAA;kBAAOgJ,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAyB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EACzE1H,QAAQ,CAACc,UAAU,CAACyD,MAAM,GAAG,CAAC,gBAC7BnG,OAAA;kBAAIgJ,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAC/BrH,QAAQ,CAACc,UAAU,CAACiE,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACnC7G,OAAA;oBAAgBgJ,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBAC9BjJ,OAAA,CAACX,IAAI;sBAAC+K,IAAI,EAAC,oCAAoC;sBAACF,KAAK,EAAC,IAAI;sBAACG,MAAM,EAAC,IAAI;sBAACrB,SAAS,EAAC;oBAAmB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACtG1C,IAAI;kBAAA,GAFEC,KAAK;oBAAAsC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAGV,CACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,gBAELtJ,OAAA;kBAAGgJ,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAA2B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAC9D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtJ,OAAA;YAAAmJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAGNtJ,OAAA;YAAKgJ,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBjJ,OAAA;cAAIgJ,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC/BjJ,OAAA,CAACX,IAAI;gBAAC+K,IAAI,EAAC,2BAA2B;gBAACF,KAAK,EAAC,IAAI;gBAACG,MAAM,EAAC,IAAI;gBAACrB,SAAS,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,uBAEnF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLtJ,OAAA;cAAKgJ,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClBjJ,OAAA;gBAAKgJ,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BjJ,OAAA;kBAAOgJ,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5DtJ,OAAA;kBAAGgJ,SAAS,EAAC,MAAM;kBAAAC,QAAA,eACjBjJ,OAAA;oBAAMgJ,SAAS,EAAE,SAASpH,QAAQ,CAACU,UAAU,KAAK,MAAM,GAAG,YAAY,GAAG,YAAY,EAAG;oBAAA2G,QAAA,EACtFrH,QAAQ,CAACU,UAAU,KAAK,MAAM,GAAG,aAAa,GAAG;kBAAa;oBAAA6G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,EACL1H,QAAQ,CAACU,UAAU,KAAK,MAAM,iBAC7BtC,OAAA,CAAAE,SAAA;gBAAA+I,QAAA,gBACEjJ,OAAA;kBAAKgJ,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BjJ,OAAA;oBAAOgJ,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACzDtJ,OAAA;oBAAGgJ,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAErH,QAAQ,CAACW;kBAAQ;oBAAA4G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACNtJ,OAAA;kBAAKgJ,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BjJ,OAAA;oBAAOgJ,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtDtJ,OAAA;oBAAGgJ,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,GAC1BrH,QAAQ,CAACW,QAAQ,KAAK,KAAK,IAAI,GAAG,EAClCX,QAAQ,CAACW,QAAQ,KAAK,KAAK,IAAI,GAAG,EAClCX,QAAQ,CAACW,QAAQ,KAAK,KAAK,IAAI,GAAG,EAClCX,QAAQ,CAACW,QAAQ,KAAK,KAAK,IAAI,GAAG,EAClCX,QAAQ,CAACW,QAAQ,KAAK,KAAK,IAAI,IAAI,EACnCX,QAAQ,CAACY,KAAK;kBAAA;oBAAA2G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA,eACN,CACH;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMiC,iBAAiB,GAAGA,CAAA,kBACxBvL,OAAA;IAAKgJ,SAAS,EAAC,KAAK;IAAAC,QAAA,eAClBjJ,OAAA;MAAKgJ,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1BjJ,OAAA;QAAKgJ,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBjJ,OAAA;UAAKgJ,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAExBjJ,OAAA;YAAKgJ,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBjJ,OAAA;cAAOgJ,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnDtJ,OAAA;cAAKgJ,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BjJ,OAAA;gBAAKgJ,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjJ,OAAA;kBACE8F,IAAI,EAAC,OAAO;kBACZkD,SAAS,EAAC,kBAAkB;kBAC5BO,EAAE,EAAC,MAAM;kBACTjE,IAAI,EAAC,YAAY;kBACjBH,KAAK,EAAC,MAAM;kBACZqG,OAAO,EAAE5J,QAAQ,CAACU,UAAU,KAAK,MAAO;kBACxCmH,QAAQ,EAAGxE,CAAC,IAAKpD,WAAW,CAACuE,IAAI,KAAK;oBACpC,GAAGA,IAAI;oBACP9D,UAAU,EAAE2C,CAAC,CAACC,MAAM,CAACC,KAAK;oBAC1B3C,KAAK,EAAE;oBACP;kBACF,CAAC,CAAC;gBAAE;kBAAA2G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACFtJ,OAAA;kBAAOgJ,SAAS,EAAC,kBAAkB;kBAACE,OAAO,EAAC,MAAM;kBAAAD,QAAA,gBAChDjJ,OAAA;oBAAAiJ,QAAA,EAAQ;kBAAW;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5BtJ,OAAA;oBAAAmJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNtJ,OAAA;oBAAOgJ,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAkC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNtJ,OAAA;gBAAKgJ,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjJ,OAAA;kBACE8F,IAAI,EAAC,OAAO;kBACZkD,SAAS,EAAC,kBAAkB;kBAC5BO,EAAE,EAAC,MAAM;kBACTjE,IAAI,EAAC,YAAY;kBACjBH,KAAK,EAAC,MAAM;kBACZqG,OAAO,EAAE5J,QAAQ,CAACU,UAAU,KAAK,MAAO;kBACxCmH,QAAQ,EAAGxE,CAAC,IAAKpD,WAAW,CAACuE,IAAI,KAAK;oBACpC,GAAGA,IAAI;oBACP9D,UAAU,EAAE2C,CAAC,CAACC,MAAM,CAACC;kBACvB,CAAC,CAAC;gBAAE;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACFtJ,OAAA;kBAAOgJ,SAAS,EAAC,kBAAkB;kBAACE,OAAO,EAAC,MAAM;kBAAAD,QAAA,gBAChDjJ,OAAA;oBAAAiJ,QAAA,EAAQ;kBAAW;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5BtJ,OAAA;oBAAAmJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNtJ,OAAA;oBAAOgJ,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAwC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNtJ,OAAA;YAAKgJ,SAAS,EAAC,2BAA2B;YAAAC,QAAA,eACxCjJ,OAAA;cAAOgJ,SAAS,EAAC,YAAY;cAAAC,QAAA,GAAC,+BACA,EAACrH,QAAQ,CAACW,QAAQ,EAAC,kBAAc,EAACpC,MAAM,EAAC,IACvE;YAAA;cAAAgJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,EAEL1H,QAAQ,CAACU,UAAU,KAAK,MAAM,iBACzCtC,OAAA,CAAAE,SAAA;YAAA+I,QAAA,GAEG9I,MAAM,KAAK,YAAY,iBACtBH,OAAA;cAAKgJ,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBjJ,OAAA;gBAAOgJ,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrDtJ,OAAA;gBACEgJ,SAAS,EAAC,aAAa;gBACvB7D,KAAK,EAAEvD,QAAQ,CAACW,QAAS;gBACzBkH,QAAQ,EAAGxE,CAAC,IACVpD,WAAW,CAAEuE,IAAI,KAAM;kBACrB,GAAGA,IAAI;kBACP7D,QAAQ,EAAE0C,CAAC,CAACC,MAAM,CAACC;gBACrB,CAAC,CAAC,CACH;gBAAA8D,QAAA,eAEDjJ,OAAA;kBAAQmF,KAAK,EAAC,KAAK;kBAAA8D,QAAA,EAAC;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eACTtJ,OAAA;gBAAOgJ,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAE9B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACN,EAGAnJ,MAAM,KAAK,sBAAsB,iBAChCH,OAAA;cAAKgJ,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBjJ,OAAA;gBAAOgJ,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrDtJ,OAAA;gBACEgJ,SAAS,EAAC,aAAa;gBACvB7D,KAAK,EAAEvD,QAAQ,CAACW,QAAS;gBACzBkH,QAAQ,EAAGxE,CAAC,IACVpD,WAAW,CAAEuE,IAAI,KAAM;kBACrB,GAAGA,IAAI;kBACP7D,QAAQ,EAAE0C,CAAC,CAACC,MAAM,CAACC;gBACrB,CAAC,CAAC,CACH;gBAAA8D,QAAA,eAEDjJ,OAAA;kBAAQmF,KAAK,EAAC,KAAK;kBAAA8D,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eACTtJ,OAAA;gBAAOgJ,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAE9B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACN,EAGAnJ,MAAM,KAAK,0BAA0B,iBACpCH,OAAA;cAAKgJ,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBjJ,OAAA;gBAAOgJ,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrDtJ,OAAA;gBACEgJ,SAAS,EAAC,aAAa;gBACvB7D,KAAK,EAAEvD,QAAQ,CAACW,QAAS;gBACzBkH,QAAQ,EAAGxE,CAAC,IACVpD,WAAW,CAAEuE,IAAI,KAAM;kBACrB,GAAGA,IAAI;kBACP7D,QAAQ,EAAE0C,CAAC,CAACC,MAAM,CAACC;gBACrB,CAAC,CAAC,CACH;gBAAA8D,QAAA,eAEDjJ,OAAA;kBAAQmF,KAAK,EAAC,KAAK;kBAAA8D,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eACTtJ,OAAA;gBAAOgJ,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAE9B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACN,eAGDtJ,OAAA;cAAKgJ,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBjJ,OAAA;gBAAOgJ,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpDtJ,OAAA;gBAAKgJ,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BjJ,OAAA;kBAAMgJ,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,GAC/BrH,QAAQ,CAACW,QAAQ,KAAK,KAAK,IAAI,IAAI,EACnCX,QAAQ,CAACW,QAAQ,KAAK,KAAK,IAAI,GAAG;gBAAA;kBAAA4G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eACPtJ,OAAA;kBACE8F,IAAI,EAAC,QAAQ;kBACbkD,SAAS,EAAC,cAAc;kBACxBQ,WAAW,EAAC,oBAAoB;kBAChCrE,KAAK,EAAEvD,QAAQ,CAACY,KAAM;kBACtBiH,QAAQ,EAAGxE,CAAC,IACVpD,WAAW,CAAEuE,IAAI,KAAM;oBACrB,GAAGA,IAAI;oBACP5D,KAAK,EAAEyC,CAAC,CAACC,MAAM,CAACC;kBAClB,CAAC,CAAC,CACH;kBACDwE,QAAQ;gBAAA;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,eACN,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEc;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMmC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,QAAQnK,WAAW;MACjB,KAAK,CAAC;QACJ,OAAOyH,2BAA2B,CAAC,CAAC;MACtC,KAAK,CAAC;QACJ,OAAOgB,qBAAqB,CAAC,CAAC;MAChC,KAAK,CAAC;QACJ,OAAOoB,+BAA+B,CAAC,CAAC;MAC1C,KAAK,CAAC;QACJ,OAAOI,iBAAiB,CAAC,CAAC;MAC5B,KAAK,CAAC;QACJ,OAAOF,iBAAiB,CAAC,CAAC;MAC5B;QACE,OAAO,IAAI;IACf;EACF,CAAC;;EAED;EACA,MAAMK,wBAAwB,GAAGA,CAAA,KAAM;IACrC,MAAMC,UAAU,GAAG3K,QAAQ,IAAIC,eAAe;IAC9C,oBACEjB,OAAA;MAAKgJ,SAAS,EAAC,2FAA2F;MACrGiB,KAAK,EAAE;QACL2B,eAAe,EAAE,oBAAoB;QACrCC,MAAM,EAAE;MACV,CAAE;MAAA5C,QAAA,eACLjJ,OAAA;QAAKgJ,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrCjJ,OAAA;UAAKgJ,SAAS,EAAC,kCAAkC;UAAC8C,IAAI,EAAC,QAAQ;UAAC7B,KAAK,EAAE;YAAEC,KAAK,EAAE,MAAM;YAAEG,MAAM,EAAE;UAAO,CAAE;UAAApB,QAAA,eACvGjJ,OAAA;YAAMgJ,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eACNtJ,OAAA;UAAIgJ,SAAS,EAAC,MAAM;UAAAC,QAAA,EAAE0C,UAAU,GAAG,yBAAyB,GAAG;QAAyB;UAAAxC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC9FtJ,OAAA;UAAGgJ,SAAS,EAAC,MAAM;UAAAC,QAAA,EAAC;QAAqD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;;EAED;EACA,MAAMyC,oBAAoB,GAAGA,CAAA,kBAC3B/L,OAAA;IAAKgJ,SAAS,EAAC,2FAA2F;IACrGiB,KAAK,EAAE;MACL2B,eAAe,EAAE,oBAAoB;MACrCC,MAAM,EAAE;IACV,CAAE;IAAA5C,QAAA,eACLjJ,OAAA;MAAKgJ,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACrCjJ,OAAA;QAAKgJ,SAAS,EAAC,kCAAkC;QAAC8C,IAAI,EAAC,QAAQ;QAAC7B,KAAK,EAAE;UAAEC,KAAK,EAAE,MAAM;UAAEG,MAAM,EAAE;QAAO,CAAE;QAAApB,QAAA,eACvGjJ,OAAA;UAAMgJ,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAU;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC,eACNtJ,OAAA;QAAIgJ,SAAS,EAAC,MAAM;QAAAC,QAAA,EAAC;MAAyB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnDtJ,OAAA;QAAGgJ,SAAS,EAAC,MAAM;QAAAC,QAAA,EAAC;MAAkD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,oBACEtJ,OAAA;IAAKgJ,SAAS,EAAC,WAAW;IAAAC,QAAA,GAEvBzH,YAAY,iBAAIxB,OAAA,CAAC0L,wBAAwB;MAAAvC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAC5C5H,eAAe,iBAAI1B,OAAA,CAAC+L,oBAAoB;MAAA5C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAG5CtJ,OAAA;MAAKgJ,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC3BjJ,OAAA;QAAKgJ,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAE9BjJ,OAAA;UAAKgJ,SAAS,EAAC;QAAiB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAEvCtJ,OAAA;UACEgJ,SAAS,EAAC,uBAAuB;UACjCiB,KAAK,EAAE;YACLC,KAAK,EAAE,GAAI,CAAC5I,WAAW,GAAG,CAAC,KAAK6F,KAAK,CAAChB,MAAM,GAAG,CAAC,CAAC,GAAI,GAAG;UAC1D;QAAE;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGPtJ,OAAA;UAAKgJ,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAC7B9B,KAAK,CAACR,GAAG,CAAC,CAACqF,IAAI,EAAEnF,KAAK,kBACrB7G,OAAA;YAEEgJ,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAExBjJ,OAAA;cACEgJ,SAAS,EAAE,kBACT1H,WAAW,GAAGuF,KAAK,GAAG,CAAC,GAAG,WAAW,GACrCvF,WAAW,KAAKuF,KAAK,GAAG,CAAC,GAAG,QAAQ,GAAG,EAAE,EACxC;cAAAoC,QAAA,EAEF+C,IAAI,CAAC5E;YAAM;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACNtJ,OAAA;cAAKgJ,SAAS,EAAE,iBAAiB1H,WAAW,KAAKuF,KAAK,GAAG,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;cAAAoC,QAAA,EAC1E+C,IAAI,CAAC3E;YAAK;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA,GAbD0C,IAAI,CAAC5E,MAAM;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcb,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtJ,OAAA;MAAKgJ,SAAS,EAAC,cAAc;MAAAC,QAAA,EAC1BwC,iBAAiB,CAAC;IAAC;MAAAtC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,eAGNtJ,OAAA;MAAKgJ,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC3BjJ,OAAA;QAAKgJ,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BjJ,OAAA;UACEgJ,SAAS,EAAC,cAAc;UACxBwB,OAAO,EAAE1C,cAAe;UACxBmE,QAAQ,EAAE3K,WAAW,KAAK,CAAC,IAAIE,YAAa;UAAAyH,QAAA,gBAE5CjJ,OAAA,CAACX,IAAI;YAAC+K,IAAI,EAAC,8BAA8B;YAACF,KAAK,EAAC,IAAI;YAACG,MAAM,EAAC;UAAI;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,YAErE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACRhI,WAAW,KAAK6F,KAAK,CAAChB,MAAM,gBAC3BnG,OAAA;UACEgJ,SAAS,EAAC,UAAU;UACpBwB,OAAO,EAAEzC,YAAa;UACtBkE,QAAQ,EAAE,CAACrE,kBAAkB,CAAC,CAAC,IAAIpG,YAAa;UAAAyH,QAAA,EAE/CzH,YAAY,gBACXxB,OAAA,CAAAE,SAAA;YAAA+I,QAAA,gBACEjJ,OAAA;cAAKgJ,SAAS,EAAC,uCAAuC;cAAC8C,IAAI,EAAC,QAAQ;cAAA7C,QAAA,eAClEjJ,OAAA;gBAAMgJ,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,EACLtI,QAAQ,IAAIC,eAAe,GAAG,oBAAoB,GAAG,oBAAoB;UAAA,eAC1E,CAAC,gBAEHjB,OAAA,CAAAE,SAAA;YAAA+I,QAAA,gBACEjJ,OAAA,CAACX,IAAI;cAAC+K,IAAI,EAAC,6BAA6B;cAACF,KAAK,EAAC,IAAI;cAACG,MAAM,EAAC,IAAI;cAACrB,SAAS,EAAC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAClFtI,QAAQ,IAAIC,eAAe,GAAG,eAAe,GAAG,eAAe;UAAA,eAChE;QACH;UAAAkI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,gBAETtJ,OAAA;UACEgJ,SAAS,EAAC,UAAU;UACpBwB,OAAO,EAAE3C,UAAW;UACpBoE,QAAQ,EAAE,CAACrE,kBAAkB,CAAC,CAAE;UAAAqB,QAAA,GAE/B3H,WAAW,KAAK6F,KAAK,CAAChB,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,MAAM,eACtDnG,OAAA,CAACX,IAAI;YAAC+K,IAAI,EAAC,+BAA+B;YAACF,KAAK,EAAC,IAAI;YAACG,MAAM,EAAC;UAAI;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEH,CAAC;AAEV;AAAC3I,EAAA,CAtoCQD,YAAY;EAAA,QACFpB,WAAW,EACXE,WAAW,EACPD,SAAS;AAAA;AAAA2M,EAAA,GAHvBxL,YAAY;AAwoCrB,eAAeA,YAAY;AAAC,IAAAwL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}