{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NEW_LMS_FIXING\\\\FRONT\\\\src\\\\pages\\\\admin\\\\courses\\\\CreateCourse.jsx\",\n  _process$env$REACT_AP,\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Icon } from '@iconify/react';\nimport { useLocation, useParams, useNavigate } from 'react-router-dom';\nimport './CreateCourse.css';\nimport { decodeData } from '../../../utils/encodeAndEncode';\nimport { getCourseCertificate, createNewCourse, getCourseDetailsById, updateCourseDetails } from '../../../services/adminService';\nimport courseCategoriesData from '../../../utils/courseCategories.json';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst domain = (_process$env$REACT_AP = process.env.REACT_APP_DOMAIN_URL) === null || _process$env$REACT_AP === void 0 ? void 0 : _process$env$REACT_AP.trim();\nconsole.log('Domain from env:', process.env.REACT_APP_DOMAIN_URL);\nconsole.log('Domain type:', typeof domain);\nconsole.log('Domain length:', domain === null || domain === void 0 ? void 0 : domain.length);\nconsole.log('Domain trimmed:', domain);\nconsole.log('Exact comparison:', domain === 'lms.tpi.sg');\nconsole.log('Domain char codes:', domain === null || domain === void 0 ? void 0 : domain.split('').map(c => c.charCodeAt(0)));\n// alert(domain);\n\nfunction CreateCourse() {\n  _s();\n  var _location$state, _location$state2;\n  const location = useLocation();\n  const navigate = useNavigate();\n  const {\n    courseId\n  } = useParams();\n  // Only try to decode if courseId exists\n  const decodedCourseId = courseId ? decodeData(courseId) : null;\n  // console.log('Course ID:', courseId);\n  // console.log('Decoded Course ID:', decodedCourseId);\n\n  const isEditing = (_location$state = location.state) === null || _location$state === void 0 ? void 0 : _location$state.isEditing;\n  const initialCourseData = (_location$state2 = location.state) === null || _location$state2 === void 0 ? void 0 : _location$state2.courseData;\n  const [currentStep, setCurrentStep] = useState(1);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [isLoadingCourse, setIsLoadingCourse] = useState(false);\n  const [formData, setFormData] = useState({\n    courseTitle: '',\n    courseDescription: '',\n    courseLevel: 'intermediate',\n    courseLanguage: 'english',\n    courseCategory: '',\n    thumbnail: null,\n    thumbnailPreview: '',\n    certificateType: '',\n    courseType: 'free',\n    currency: domain === 'lms.tpi.sg' ? 'SGD' : domain === 'lms.nxgenvarsity.com' || domain === 'lms.creatorfoundation.in' ? 'INR' : 'INR',\n    price: '',\n    tags: [],\n    courseInfo: []\n  });\n  useEffect(() => {\n    if (isEditing && initialCourseData) {\n      setFormData(initialCourseData);\n    }\n  }, [isEditing, initialCourseData]);\n\n  // Fetch course details if courseId is available\n  useEffect(() => {\n    const fetchCourseDetails = async () => {\n      if (courseId && decodedCourseId) {\n        setIsLoadingCourse(true);\n        try {\n          console.log('Fetching course details for ID:', decodedCourseId);\n          const response = await getCourseDetailsById({\n            course_id: decodedCourseId\n          });\n          console.log('get Course Details Response:', response);\n          if (response.success && response.data.course) {\n            var _course$certificate_t;\n            const course = response.data.course;\n            console.log('Course details fetched:', course);\n\n            // Map API response to form data structure\n            setFormData({\n              courseTitle: course.course_name || '',\n              courseDescription: course.course_desc || '',\n              courseLevel: course.levels || '',\n              courseLanguage: course.course_language || '',\n              courseCategory: course.course_category || '',\n              thumbnail: null,\n              // Will be handled separately if needed\n              thumbnailPreview: course.banner_image || '',\n              certificateType: ((_course$certificate_t = course.certificate_template_id) === null || _course$certificate_t === void 0 ? void 0 : _course$certificate_t.toString()) || '',\n              courseType: course.course_type || 'free',\n              currency: course.currency || (domain === 'lms.tpi.sg' ? 'SGD' : domain === 'lms.nxgenvarsity.com' || domain === 'lms.creatorfoundation.in' ? 'INR' : 'INR'),\n              price: course.course_price || '',\n              tags: Array.isArray(course.tags) ? course.tags : [],\n              courseInfo: Array.isArray(course.course_info) ? course.course_info : []\n            });\n            console.log('Form data populated from API');\n          } else {\n            var _response$data, _response$data2;\n            console.error('Failed to fetch course details:', (_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.error_msg);\n            alert('Failed to load course details: ' + (((_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.error_msg) || 'Unknown error'));\n          }\n        } catch (error) {\n          console.error('Error fetching course details:', error);\n          alert('Error loading course details. Please try again.');\n        } finally {\n          setIsLoadingCourse(false);\n        }\n      }\n    };\n    fetchCourseDetails();\n  }, [courseId, decodedCourseId]);\n  const [certificateData, setCertificateData] = useState([]);\n  const fetchCertificateData = async () => {\n    try {\n      const response = await getCourseCertificate();\n      // console.log('Certificate Data:', response);\n      if (response.success) {\n        setCertificateData(response.data.templates);\n      } else {\n        // console.error('Invalid certificate data format:', response);\n        setCertificateData([]); // Reset to empty array if invalid data\n      }\n    } catch (error) {\n      console.error('Error fetching certificate data:', error);\n      setCertificateData([]); // Reset to empty array on error\n    }\n  };\n  useEffect(() => {\n    fetchCertificateData();\n  }, []);\n  const [tagInput, setTagInput] = useState('');\n  const [infoInput, setInfoInput] = useState('');\n  const [editingInfo, setEditingInfo] = useState(null);\n\n  // Memoized handlers for tag and info inputs\n  const handleTagInputChange = useCallback(e => {\n    setTagInput(e.target.value);\n  }, []);\n  const handleInfoInputChange = useCallback(e => {\n    setInfoInput(e.target.value);\n  }, []);\n  const handleInputChange = useCallback(e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prevState => ({\n      ...prevState,\n      [name]: value\n    }));\n  }, []);\n  const handleThumbnailChange = e => {\n    const file = e.target.files[0];\n    if (file) {\n      setFormData(prevState => ({\n        ...prevState,\n        thumbnail: file,\n        thumbnailPreview: URL.createObjectURL(file)\n      }));\n    }\n  };\n  const handleRemoveFile = type => {\n    if (type === 'thumbnail') {\n      setFormData(prevState => ({\n        ...prevState,\n        thumbnail: null,\n        thumbnailPreview: ''\n      }));\n    }\n  };\n  const handleAddTag = useCallback(e => {\n    e.preventDefault();\n    const tag = tagInput.trim();\n    if (tag && !formData.tags.includes(tag)) {\n      if (tag.length < 3) {\n        alert('Tag must be at least 3 characters long');\n        return;\n      }\n      if (formData.tags.length >= 8) {\n        alert('Maximum 8 tags allowed');\n        return;\n      }\n      setFormData(prev => ({\n        ...prev,\n        tags: [...prev.tags, tag]\n      }));\n      setTagInput('');\n    }\n  }, [tagInput, formData.tags]);\n  const handleRemoveTag = tagToRemove => {\n    setFormData(prev => ({\n      ...prev,\n      tags: prev.tags.filter(tag => tag !== tagToRemove)\n    }));\n  };\n  const handleTagInputKeyDown = e => {\n    if (e.key === 'Enter') {\n      e.preventDefault();\n      handleAddTag(e);\n    }\n  };\n  const handleAddInfo = useCallback(e => {\n    e.preventDefault();\n    if (infoInput.trim()) {\n      if (editingInfo !== null) {\n        setFormData(prev => ({\n          ...prev,\n          courseInfo: prev.courseInfo.map((info, index) => index === editingInfo ? infoInput.trim() : info)\n        }));\n        setEditingInfo(null);\n      } else {\n        setFormData(prev => ({\n          ...prev,\n          courseInfo: [...prev.courseInfo, infoInput.trim()]\n        }));\n      }\n      setInfoInput('');\n    }\n  }, [infoInput, editingInfo]);\n  const handleEditInfo = index => {\n    setInfoInput(formData.courseInfo[index]);\n    setEditingInfo(index);\n  };\n  const handleDeleteInfo = index => {\n    setFormData(prev => ({\n      ...prev,\n      courseInfo: prev.courseInfo.filter((_, i) => i !== index)\n    }));\n  };\n  const handleInfoInputKeyDown = e => {\n    if (e.key === 'Enter') {\n      e.preventDefault();\n      handleAddInfo(e);\n    }\n  };\n  const steps = [{\n    number: '01',\n    title: 'Course Information'\n  }, {\n    number: '02',\n    title: 'Course Media'\n  }, {\n    number: '03',\n    title: 'Additional information'\n  }, {\n    number: '04',\n    title: 'Pricing'\n  }, {\n    number: '05',\n    title: 'Preview & Submit'\n  }];\n\n  // Validation functions for each step\n  const validateStep1 = () => {\n    return formData.courseTitle.trim() !== '' && formData.courseDescription.trim() !== '' && formData.courseLevel !== '' && formData.courseLanguage !== '' && formData.courseCategory !== '';\n  };\n  const validateStep2 = () => {\n    // For editing mode, allow progression if thumbnail exists (either new upload or existing image)\n    if (courseId && decodedCourseId) {\n      return (formData.thumbnail !== null || formData.thumbnailPreview !== '') && formData.certificateType !== '';\n    }\n    // For new course creation, require new thumbnail upload\n    return formData.thumbnail !== null && formData.certificateType !== '';\n  };\n  const validateStep3 = () => {\n    // For editing mode, allow empty tags and courseInfo if course already exists\n    if (courseId && decodedCourseId) {\n      return true; // Allow progression in edit mode even with empty arrays\n    }\n    // For new course creation, require at least one tag and one course info\n    return formData.tags.length > 0 && formData.courseInfo.length > 0;\n  };\n  const validateStep4 = () => {\n    if (formData.courseType === 'free') {\n      return true;\n    }\n    return formData.price !== '' && parseFloat(formData.price) > 0;\n  };\n  const validateStep5 = () => {\n    // Preview step - all previous steps must be valid\n    return validateStep1() && validateStep2() && validateStep3() && validateStep4();\n  };\n  const isCurrentStepValid = () => {\n    switch (currentStep) {\n      case 1:\n        return validateStep1();\n      case 2:\n        return validateStep2();\n      case 3:\n        return validateStep3();\n      case 4:\n        return validateStep4();\n      case 5:\n        return validateStep5();\n      default:\n        return false;\n    }\n  };\n  const handleNext = () => {\n    if (currentStep < steps.length) {\n      setCurrentStep(currentStep + 1);\n    }\n  };\n  const handlePrevious = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n  const handleSubmit = async () => {\n    if (isCurrentStepValid()) {\n      setIsSubmitting(true);\n      try {\n        // Start the minimum 3-second timer\n        const minLoadingTime = new Promise(resolve => setTimeout(resolve, 3000));\n\n        // Create FormData object for file upload\n        const submitData = new FormData();\n\n        // Add all form fields to FormData\n        submitData.append('courseTitle', formData.courseTitle);\n        submitData.append('courseDescription', formData.courseDescription);\n        submitData.append('courseLevel', formData.courseLevel);\n        submitData.append('courseLanguage', formData.courseLanguage);\n        submitData.append('courseCategory', formData.courseCategory);\n        submitData.append('certificateType', formData.certificateType);\n        submitData.append('courseType', formData.courseType);\n        submitData.append('currency', formData.currency);\n        submitData.append('price', formData.price);\n        submitData.append('tags', JSON.stringify(formData.tags));\n        submitData.append('courseInfo', JSON.stringify(formData.courseInfo));\n        console.log('Submit Data:', submitData);\n\n        // Add thumbnail file if exists (for new upload)\n        if (formData.thumbnail) {\n          submitData.append('banner_image', formData.thumbnail);\n        }\n        console.log('Submitting course data...');\n        console.log('Is editing mode:', courseId && decodedCourseId);\n\n        // Choose API call based on edit mode\n        let apiCall;\n        if (courseId && decodedCourseId) {\n          // Update existing course\n          apiCall = updateCourseDetails(decodedCourseId, submitData);\n        } else {\n          // Create new course\n          apiCall = createNewCourse(submitData);\n        }\n\n        // Wait for both minimum time and API response\n        const [response] = await Promise.all([apiCall, minLoadingTime]);\n        if (response.success) {\n          console.log('Course operation successful:', response.data);\n          // Navigate to courses page after successful operation\n          navigate('/admin/courses');\n        } else {\n          var _response$data3;\n          setIsSubmitting(false);\n          const errorMsg = courseId && decodedCourseId ? 'Failed to update course' : 'Failed to create course';\n          alert(errorMsg + ': ' + (((_response$data3 = response.data) === null || _response$data3 === void 0 ? void 0 : _response$data3.error_msg) || 'Unknown error'));\n        }\n      } catch (error) {\n        setIsSubmitting(false);\n        console.error('Error with course operation:', error);\n        const errorMsg = courseId && decodedCourseId ? 'Error updating course' : 'Error creating course';\n        alert(errorMsg + '. Please try again.');\n      }\n    } else {\n      alert('Please fill in all required fields before submitting.');\n    }\n  };\n  const renderCourseInformationForm = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"course-information\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"courseTitle\",\n          className: \"form-label\",\n          children: \"Course Title *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          className: \"form-control\",\n          id: \"courseTitle\",\n          name: \"courseTitle\",\n          placeholder: \"Enter course title\",\n          value: formData.courseTitle,\n          onChange: handleInputChange,\n          maxLength: 100,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"text-muted\",\n          children: \"Maximum 100 characters allowed. Use a short and meaningful title for better readability.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-12 mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"courseDescription\",\n          className: \"form-label\",\n          children: \"Course Description *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          className: \"form-control\",\n          id: \"courseDescription\",\n          name: \"courseDescription\",\n          rows: \"4\",\n          placeholder: \"Enter course description\",\n          value: formData.courseDescription,\n          onChange: handleInputChange,\n          maxLength: 1000,\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"text-muted\",\n          children: \"Maximum 1000 characters allowed\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 417,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 404,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-4 mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"courseLevel\",\n          className: \"form-label\",\n          children: \"Course Level *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          className: \"form-select\",\n          id: \"courseLevel\",\n          name: \"courseLevel\",\n          value: formData.courseLevel,\n          onChange: handleInputChange,\n          required: true,\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            children: \"Select Level\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"beginner\",\n            children: \"Beginner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"intermediate\",\n            children: \"Intermediate\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"advanced\",\n            children: \"Advanced\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"all-levels\",\n            children: \"All Levels\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-4 mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"courseLanguage\",\n          className: \"form-label\",\n          children: \"Course Language *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          className: \"form-select\",\n          id: \"courseLanguage\",\n          name: \"courseLanguage\",\n          value: formData.courseLanguage,\n          onChange: handleInputChange,\n          required: true,\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            children: \"Select Language\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 448,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"english\",\n            children: \"English\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 449,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"spanish\",\n            children: \"Spanish\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"french\",\n            children: \"French\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"german\",\n            children: \"German\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"chinese\",\n            children: \"Chinese\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"japanese\",\n            children: \"Japanese\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"hindi\",\n            children: \"Hindi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 438,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-4 mb-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"courseCategory\",\n          className: \"form-label\",\n          children: \"Course Category *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 460,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          className: \"form-select\",\n          id: \"courseCategory\",\n          name: \"courseCategory\",\n          value: formData.courseCategory,\n          onChange: handleInputChange,\n          required: true,\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"\",\n            children: \"Select Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 13\n          }, this), courseCategoriesData.categories.map((category, index) => /*#__PURE__*/_jsxDEV(\"option\", {\n            value: category,\n            children: category\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 471,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 461,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 459,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 387,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 386,\n    columnNumber: 5\n  }, this);\n  const renderCourseMediaForm = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"course-media\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-6 mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"form-label\",\n          children: \"Course Thumbnail *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 486,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: !formData.thumbnailPreview ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"file\",\n                id: \"thumbnail\",\n                className: \"d-none\",\n                accept: \"image/*\",\n                onChange: handleThumbnailChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 491,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"thumbnail\",\n                className: \"btn btn-outline-primary mb-3 d-flex flex-column align-items-center gap-2 mx-auto\",\n                style: {\n                  width: 'fit-content',\n                  cursor: 'pointer'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Icon, {\n                  icon: \"fluent:image-add-24-regular\",\n                  width: \"48\",\n                  height: \"48\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 503,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Upload Thumbnail\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 504,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-muted\",\n                  children: \"Recommended size: 1280x720px\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 505,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"position-relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: formData.thumbnailPreview,\n                alt: \"Course Thumbnail\",\n                className: \"img-fluid rounded\",\n                style: {\n                  width: '100%',\n                  height: 'auto'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 510,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"btn btn-danger d-flex align-items-center justify-content-center p-0 position-absolute\",\n                onClick: () => handleRemoveFile('thumbnail'),\n                type: \"button\",\n                style: {\n                  width: '24px',\n                  height: '24px',\n                  top: '8px',\n                  right: '8px',\n                  minWidth: 'unset',\n                  borderRadius: '4px'\n                },\n                children: /*#__PURE__*/_jsxDEV(Icon, {\n                  icon: \"fluent:delete-24-regular\",\n                  width: \"14\",\n                  height: \"14\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 529,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 516,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 488,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 487,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 485,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"col-md-6 mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"form-label\",\n          children: \"Certificate Type *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 539,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-body\",\n            children: [/*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"form-select mb-3\",\n              name: \"certificateType\",\n              value: formData.certificateType,\n              onChange: handleInputChange,\n              required: true,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select Certificate Type\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 17\n              }, this), Array.isArray(certificateData) && certificateData.map(cert => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: cert.id || cert._id,\n                children: cert.template_name || 'Unnamed Certificate'\n              }, cert.id || cert._id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 551,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 542,\n              columnNumber: 15\n            }, this), formData.certificateType && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"certificate-preview p-3 bg-light rounded\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center gap-3\",\n                children: [/*#__PURE__*/_jsxDEV(Icon, {\n                  icon: \"fluent:certificate-24-regular\",\n                  width: \"32\",\n                  height: \"32\",\n                  className: \"text-primary\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 560,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n                    className: \"mb-1\",\n                    children: [formData.certificateType.charAt(0).toUpperCase() + formData.certificateType.slice(1), \" Certificate\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 562,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: \"Will be issued upon course completion\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 563,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 561,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 559,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 558,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 541,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 540,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 538,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 483,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 482,\n    columnNumber: 5\n  }, this);\n  const renderAdditionalInformationForm = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"row\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"col-12 mb-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"Course Tags *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 581,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"row g-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-10\",\n                  children: /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    className: \"form-control rounded-0 rounded-start\",\n                    placeholder: \"Add tags (e.g., 'programming', 'web development')\",\n                    value: tagInput,\n                    onChange: handleTagInputChange,\n                    onKeyDown: handleTagInputKeyDown\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 585,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 584,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"btn btn-primary w-100 rounded-0 rounded-end\",\n                    onClick: handleAddTag,\n                    children: \"Add Tag\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 595,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 594,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 583,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"d-block text-muted mb-1\",\n                  children: \"Press Enter or click Add Tag to add\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 604,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"d-block text-muted\",\n                  children: [\"\\u2022 Minimum 3 characters per tag\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 607,\n                    columnNumber: 21\n                  }, this), \"\\u2022 Maximum 8 tags allowed\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 609,\n                    columnNumber: 21\n                  }, this), \"\\u2022 \", 8 - formData.tags.length, \" tags remaining\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 605,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 603,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 582,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex flex-wrap gap-2 mt-3\",\n              children: formData.tags.map((tag, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"badge bg-light text-dark border d-flex align-items-center gap-2 py-2 px-3\",\n                children: [tag, /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"btn btn-link text-danger p-0 d-flex align-items-center\",\n                  onClick: () => handleRemoveTag(tag),\n                  style: {\n                    minWidth: '20px',\n                    height: '20px'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Icon, {\n                    icon: \"fluent:dismiss-24-regular\",\n                    width: \"16\",\n                    height: \"16\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 627,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 621,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 616,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 614,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 580,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"Course Info *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 635,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"row g-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-10\",\n                  children: /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    className: \"form-control rounded-0 rounded-start\",\n                    placeholder: \"Add course information point (e.g., 'Lifetime access to course materials')\",\n                    value: infoInput,\n                    onChange: handleInfoInputChange,\n                    onKeyDown: handleInfoInputKeyDown\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 639,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 638,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-2\",\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"btn btn-primary w-100 rounded-0 rounded-end\",\n                    onClick: handleAddInfo,\n                    children: editingInfo !== null ? 'Update' : 'Add Info'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 649,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 648,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 637,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2\",\n                children: /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"d-block text-muted\",\n                  children: \"Add important information points about your course\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 658,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 657,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 636,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-3\",\n              children: formData.courseInfo.map((info, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"d-flex align-items-center gap-2 p-2 border rounded mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(Icon, {\n                  icon: \"fluent:info-24-regular\",\n                  className: \"text-primary flex-shrink-0\",\n                  width: \"20\",\n                  height: \"20\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 670,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"flex-grow-1\",\n                  children: info\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 676,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex gap-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    className: \"btn btn-link text-primary p-0 d-flex align-items-center\",\n                    onClick: () => handleEditInfo(index),\n                    style: {\n                      minWidth: '20px',\n                      height: '20px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Icon, {\n                      icon: \"fluent:edit-24-regular\",\n                      width: \"16\",\n                      height: \"16\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 684,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 678,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    className: \"btn btn-link text-danger p-0 d-flex align-items-center\",\n                    onClick: () => handleDeleteInfo(index),\n                    style: {\n                      minWidth: '20px',\n                      height: '20px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Icon, {\n                      icon: \"fluent:delete-24-regular\",\n                      width: \"16\",\n                      height: \"16\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 692,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 686,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 677,\n                  columnNumber: 21\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 666,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 664,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 634,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 579,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 578,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 577,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 576,\n    columnNumber: 5\n  }, this);\n  const renderPreviewStep = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"row\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"col-12\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              icon: \"fluent:eye-24-regular\",\n              width: \"20\",\n              height: \"20\",\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 711,\n              columnNumber: 15\n            }, this), \"Course Preview\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 710,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-muted mb-0 mt-2\",\n            children: \"Review all your course information before submitting. If you need to make changes, use the \\\"Previous\\\" button to go back to any step.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 714,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 709,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"text-primary mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                icon: \"fluent:info-24-regular\",\n                width: \"18\",\n                height: \"18\",\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 722,\n                columnNumber: 17\n              }, this), \"Course Information\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 721,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-6 mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label text-muted\",\n                  children: \"Course Title\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 727,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-0 fw-medium\",\n                  children: formData.courseTitle || 'Not specified'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 728,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 726,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-6 mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label text-muted\",\n                  children: \"Course Category\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 731,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-0\",\n                  children: formData.courseCategory || 'Not specified'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 732,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 730,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-12 mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label text-muted\",\n                  children: \"Course Description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 735,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-0\",\n                  children: formData.courseDescription || 'Not specified'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 736,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 734,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-4 mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label text-muted\",\n                  children: \"Level\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 739,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-0 text-capitalize\",\n                  children: formData.courseLevel || 'Not specified'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 740,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 738,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-4 mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label text-muted\",\n                  children: \"Language\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 743,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-0 text-capitalize\",\n                  children: formData.courseLanguage || 'Not specified'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 744,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 742,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-4 mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label text-muted\",\n                  children: \"Certificate\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 747,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-0\",\n                  children: formData.certificateType ? `Certificate ID: ${formData.certificateType}` : 'Not specified'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 748,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 746,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 725,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 720,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 753,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"text-primary mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                icon: \"fluent:image-24-regular\",\n                width: \"18\",\n                height: \"18\",\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 758,\n                columnNumber: 17\n              }, this), \"Course Media\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 757,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"row\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-6 mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label text-muted\",\n                  children: \"Course Thumbnail\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 763,\n                  columnNumber: 19\n                }, this), formData.thumbnailPreview ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: formData.thumbnailPreview,\n                    alt: \"Course Thumbnail\",\n                    className: \"img-fluid rounded border\",\n                    style: {\n                      maxHeight: '150px',\n                      width: 'auto'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 766,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 765,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-0 text-muted\",\n                  children: \"No thumbnail uploaded\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 774,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 762,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 761,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 756,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 780,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"text-primary mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                icon: \"fluent:tag-24-regular\",\n                width: \"18\",\n                height: \"18\",\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 785,\n                columnNumber: 17\n              }, this), \"Additional Information\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 784,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-6 mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label text-muted\",\n                  children: \"Tags\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 790,\n                  columnNumber: 19\n                }, this), formData.tags.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"d-flex flex-wrap gap-2\",\n                  children: formData.tags.map((tag, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"badge bg-light text-dark border\",\n                    children: tag\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 794,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 792,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-0 text-muted\",\n                  children: \"No tags added\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 800,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 789,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-6 mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label text-muted\",\n                  children: \"Course Information Points\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 804,\n                  columnNumber: 19\n                }, this), formData.courseInfo.length > 0 ? /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"list-unstyled mb-0\",\n                  children: formData.courseInfo.map((info, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                    className: \"mb-1\",\n                    children: [/*#__PURE__*/_jsxDEV(Icon, {\n                      icon: \"fluent:checkmark-circle-24-regular\",\n                      width: \"16\",\n                      height: \"16\",\n                      className: \"text-success me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 809,\n                      columnNumber: 27\n                    }, this), info]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 808,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 806,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-0 text-muted\",\n                  children: \"No course information added\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 815,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 803,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 788,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 783,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 821,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n              className: \"text-primary mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                icon: \"fluent:payment-24-regular\",\n                width: \"18\",\n                height: \"18\",\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 826,\n                columnNumber: 17\n              }, this), \"Pricing Information\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 825,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"col-md-3 mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-label text-muted\",\n                  children: \"Course Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 831,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"mb-0\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `badge ${formData.courseType === 'free' ? 'bg-success' : 'bg-primary'}`,\n                    children: formData.courseType === 'free' ? 'Free Course' : 'Paid Course'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 833,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 832,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 830,\n                columnNumber: 17\n              }, this), formData.courseType === 'paid' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-3 mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label text-muted\",\n                    children: \"Currency\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 841,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mb-0\",\n                    children: formData.currency\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 842,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 840,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"col-md-3 mb-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"form-label text-muted\",\n                    children: \"Price\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 845,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"mb-0 fw-medium\",\n                    children: [formData.currency === 'USD' && '$', formData.currency === 'EUR' && '€', formData.currency === 'GBP' && '£', formData.currency === 'INR' && '₹', formData.currency === 'SGD' && 'S$', formData.price]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 846,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 844,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 829,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 824,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 718,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 708,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 707,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 706,\n    columnNumber: 5\n  }, this);\n  const renderPricingStep = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"row\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"col-12 mb-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-body\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"form-label\",\n              children: \"Course Type *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 872,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"d-flex gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-check\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  className: \"form-check-input\",\n                  id: \"free\",\n                  name: \"courseType\",\n                  value: \"free\",\n                  checked: formData.courseType === 'free',\n                  onChange: e => setFormData(prev => ({\n                    ...prev,\n                    courseType: e.target.value,\n                    price: '',\n                    currency: domain === 'lms.tpi.sg' ? 'SGD' : domain === 'lms.nxgenvarsity.com' || domain === 'lms.creatorfoundation.in' ? 'INR' : 'INR'\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 875,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-check-label\",\n                  htmlFor: \"free\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Free Course\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 890,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 891,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: \"No payment required for enrollment\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 892,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 889,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 874,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-check\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  className: \"form-check-input\",\n                  id: \"paid\",\n                  name: \"courseType\",\n                  value: \"paid\",\n                  checked: formData.courseType === 'paid',\n                  onChange: e => setFormData(prev => ({\n                    ...prev,\n                    courseType: e.target.value\n                  }))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 896,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"form-check-label\",\n                  htmlFor: \"paid\",\n                  children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                    children: \"Paid Course\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 909,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 910,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                    className: \"text-muted\",\n                    children: \"Students must purchase to access content\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 911,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 908,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 895,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 873,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 871,\n            columnNumber: 13\n          }, this), formData.courseType === 'paid' && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Select Currency\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 920,\n                columnNumber: 7\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-muted\",\n                  children: [\"Debug: Domain = \\\"\", domain, \"\\\"\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 922,\n                  columnNumber: 9\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 923,\n                  columnNumber: 9\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"text-muted\",\n                  children: [\"TPI Check: \", domain === 'lms.tpi.sg' ? 'TRUE' : 'FALSE', \" | NxGen Check: \", domain === 'lms.nxgenvarsity.com' ? 'TRUE' : 'FALSE', \" | Creator Check: \", domain === 'lms.creatorfoundation.in' ? 'TRUE' : 'FALSE']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 924,\n                  columnNumber: 9\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 921,\n                columnNumber: 7\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                className: \"form-select\",\n                value: formData.currency,\n                onChange: e => setFormData(prev => ({\n                  ...prev,\n                  currency: e.target.value\n                })),\n                children: domain === 'lms.tpi.sg' ? /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"SGD\",\n                  children: \"SGD (S$)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 941,\n                  columnNumber: 11\n                }, this) : domain === 'lms.nxgenvarsity.com' || domain === 'lms.creatorfoundation.in' ? /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"INR\",\n                  children: \"INR (\\u20B9)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 943,\n                  columnNumber: 11\n                }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"INR\",\n                    children: \"INR (\\u20B9)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 946,\n                    columnNumber: 13\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"USD\",\n                    children: \"USD ($)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 947,\n                    columnNumber: 13\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"EUR\",\n                    children: \"EUR (\\u20AC)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 948,\n                    columnNumber: 13\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"GBP\",\n                    children: \"GBP (\\xA3)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 949,\n                    columnNumber: 13\n                  }, this)]\n                }, void 0, true)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 930,\n                columnNumber: 7\n              }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                className: \"text-muted\",\n                children: domain === 'lms.tpi.sg' ? 'Currency is fixed to SGD for this platform.' : domain === 'lms.nxgenvarsity.com' || domain === 'lms.creatorfoundation.in' ? 'Currency is fixed to INR for this platform.' : 'Select the currency for your course pricing.'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 954,\n                columnNumber: 7\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 919,\n              columnNumber: 5\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"form-label\",\n                children: \"Course Price *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 962,\n                columnNumber: 7\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"input-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"input-group-text\",\n                  children: [formData.currency === 'INR' && '₹', formData.currency === 'SGD' && 'S$', formData.currency === 'USD' && '$', formData.currency === 'EUR' && '€', formData.currency === 'GBP' && '£']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 964,\n                  columnNumber: 9\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  className: \"form-control\",\n                  placeholder: \"Enter course price\",\n                  value: formData.price,\n                  onChange: e => setFormData(prev => ({\n                    ...prev,\n                    price: e.target.value\n                  })),\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 971,\n                  columnNumber: 9\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 963,\n                columnNumber: 7\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 961,\n              columnNumber: 5\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 869,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 868,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 867,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 866,\n    columnNumber: 5\n  }, this);\n  const renderStepContent = () => {\n    switch (currentStep) {\n      case 1:\n        return renderCourseInformationForm();\n      case 2:\n        return renderCourseMediaForm();\n      case 3:\n        return renderAdditionalInformationForm();\n      case 4:\n        return renderPricingStep();\n      case 5:\n        return renderPreviewStep();\n      default:\n        return null;\n    }\n  };\n\n  // Loading overlay component for submission\n  const SubmissionLoadingOverlay = () => {\n    const isEditMode = courseId && decodedCourseId;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center\",\n      style: {\n        backgroundColor: 'rgba(0, 0, 0, 0.7)',\n        zIndex: 9999\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center text-white\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner-border text-primary mb-3\",\n          role: \"status\",\n          style: {\n            width: '3rem',\n            height: '3rem'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"visually-hidden\",\n            children: \"Loading...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1024,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1023,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n          className: \"mb-2\",\n          children: isEditMode ? 'Updating Your Course...' : 'Creating Your Course...'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1026,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mb-0\",\n          children: \"Please wait while we process your course information.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1027,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1022,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1017,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Loading overlay component for fetching course details\n  const CourseLoadingOverlay = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center\",\n    style: {\n      backgroundColor: 'rgba(0, 0, 0, 0.7)',\n      zIndex: 9999\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center text-white\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner-border text-primary mb-3\",\n        role: \"status\",\n        style: {\n          width: '3rem',\n          height: '3rem'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"visually-hidden\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1042,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1041,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n        className: \"mb-2\",\n        children: \"Loading Course Details...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1044,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mb-0\",\n        children: \"Please wait while we fetch the course information.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1045,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1040,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1035,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: [isSubmitting && /*#__PURE__*/_jsxDEV(SubmissionLoadingOverlay, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1053,\n      columnNumber: 24\n    }, this), isLoadingCourse && /*#__PURE__*/_jsxDEV(CourseLoadingOverlay, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1054,\n      columnNumber: 27\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stepper-card\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stepper-wrapper\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stepper-line-bg\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1060,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stepper-line-progress\",\n          style: {\n            width: `${(currentStep - 1) / (steps.length - 1) * 100}%`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1062,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stepper-content\",\n          children: steps.map((step, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stepper-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `stepper-circle ${currentStep > index + 1 ? 'completed' : currentStep === index + 1 ? 'active' : ''}`,\n              children: step.number\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1076,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `stepper-label ${currentStep === index + 1 ? 'active' : ''}`,\n              children: step.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1084,\n              columnNumber: 17\n            }, this)]\n          }, step.number, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1072,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1070,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1058,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1057,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stepper-card\",\n      children: renderStepContent()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1094,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"stepper-card\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stepper-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn-previous\",\n          onClick: handlePrevious,\n          disabled: currentStep === 1 || isSubmitting,\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            icon: \"fluent:arrow-left-24-regular\",\n            width: \"20\",\n            height: \"20\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1106,\n            columnNumber: 13\n          }, this), \"Previous\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1101,\n          columnNumber: 11\n        }, this), currentStep === steps.length ? /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn-next\",\n          onClick: handleSubmit,\n          disabled: !isCurrentStepValid() || isSubmitting,\n          children: isSubmitting ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"spinner-border spinner-border-sm me-2\",\n              role: \"status\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"visually-hidden\",\n                children: \"Loading...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1118,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1117,\n              columnNumber: 19\n            }, this), courseId && decodedCourseId ? 'Updating Course...' : 'Creating Course...']\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              icon: \"fluent:checkmark-24-regular\",\n              width: \"20\",\n              height: \"20\",\n              className: \"me-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1124,\n              columnNumber: 19\n            }, this), courseId && decodedCourseId ? 'Update Course' : 'Submit Course']\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1110,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn-next\",\n          onClick: handleNext,\n          disabled: !isCurrentStepValid(),\n          children: [currentStep === steps.length - 1 ? 'Preview' : 'Next', /*#__PURE__*/_jsxDEV(Icon, {\n            icon: \"fluent:arrow-right-24-regular\",\n            width: \"20\",\n            height: \"20\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1136,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1130,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1100,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1099,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1051,\n    columnNumber: 5\n  }, this);\n}\n_s(CreateCourse, \"mUrVSQGsQFva55ww+ynFU3donVU=\", false, function () {\n  return [useLocation, useNavigate, useParams];\n});\n_c = CreateCourse;\nexport default CreateCourse;\nvar _c;\n$RefreshReg$(_c, \"CreateCourse\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Icon", "useLocation", "useParams", "useNavigate", "decodeData", "getCourseCertificate", "createNewCourse", "getCourseDetailsById", "updateCourseDetails", "courseCategoriesData", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "domain", "_process$env$REACT_AP", "process", "env", "REACT_APP_DOMAIN_URL", "trim", "console", "log", "length", "split", "map", "c", "charCodeAt", "CreateCourse", "_s", "_location$state", "_location$state2", "location", "navigate", "courseId", "decodedCourseId", "isEditing", "state", "initialCourseData", "courseData", "currentStep", "setCurrentStep", "isSubmitting", "setIsSubmitting", "isLoadingCourse", "setIsLoadingCourse", "formData", "setFormData", "courseTitle", "courseDescription", "courseLevel", "courseLanguage", "courseCategory", "thumbnail", "thumbnailPreview", "certificateType", "courseType", "currency", "price", "tags", "courseInfo", "fetchCourseDetails", "response", "course_id", "success", "data", "course", "_course$certificate_t", "course_name", "course_desc", "levels", "course_language", "course_category", "banner_image", "certificate_template_id", "toString", "course_type", "course_price", "Array", "isArray", "course_info", "_response$data", "_response$data2", "error", "error_msg", "alert", "certificateData", "setCertificateData", "fetchCertificateData", "templates", "tagInput", "setTagInput", "infoInput", "setInfoInput", "editingInfo", "setEditingInfo", "handleTagInputChange", "e", "target", "value", "handleInfoInputChange", "handleInputChange", "name", "prevState", "handleThumbnailChange", "file", "files", "URL", "createObjectURL", "handleRemoveFile", "type", "handleAddTag", "preventDefault", "tag", "includes", "prev", "handleRemoveTag", "tagToRemove", "filter", "handleTagInputKeyDown", "key", "handleAddInfo", "info", "index", "handleEditInfo", "handleDeleteInfo", "_", "i", "handleInfoInputKeyDown", "steps", "number", "title", "validateStep1", "validateStep2", "validateStep3", "validateStep4", "parseFloat", "validateStep5", "isCurrentStepValid", "handleNext", "handlePrevious", "handleSubmit", "minLoadingTime", "Promise", "resolve", "setTimeout", "submitData", "FormData", "append", "JSON", "stringify", "apiCall", "all", "_response$data3", "errorMsg", "renderCourseInformationForm", "className", "children", "htmlFor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "id", "placeholder", "onChange", "max<PERSON><PERSON><PERSON>", "required", "rows", "categories", "category", "renderCourseMediaForm", "accept", "style", "width", "cursor", "icon", "height", "src", "alt", "onClick", "top", "right", "min<PERSON><PERSON><PERSON>", "borderRadius", "cert", "_id", "template_name", "char<PERSON>t", "toUpperCase", "slice", "renderAdditionalInformationForm", "onKeyDown", "renderPreviewStep", "maxHeight", "renderPricingStep", "checked", "renderStepContent", "SubmissionLoadingOverlay", "isEditMode", "backgroundColor", "zIndex", "role", "CourseLoadingOverlay", "step", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/NEW_LMS_FIXING/FRONT/src/pages/admin/courses/CreateCourse.jsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { Icon } from '@iconify/react';\nimport { useLocation, useParams, useNavigate } from 'react-router-dom';\nimport './CreateCourse.css';\nimport { decodeData } from '../../../utils/encodeAndEncode';\nimport { getCourseCertificate, createNewCourse, getCourseDetailsById, updateCourseDetails } from '../../../services/adminService';\nimport courseCategoriesData from '../../../utils/courseCategories.json';\nconst domain = process.env.REACT_APP_DOMAIN_URL?.trim();\nconsole.log('Domain from env:', process.env.REACT_APP_DOMAIN_URL);\nconsole.log('Domain type:', typeof domain);\nconsole.log('Domain length:', domain?.length);\nconsole.log('Domain trimmed:', domain);\nconsole.log('Exact comparison:', domain === 'lms.tpi.sg');\nconsole.log('Domain char codes:', domain?.split('').map(c => c.charCodeAt(0)));\n// alert(domain);\n\nfunction CreateCourse() {\n  const location = useLocation();\n  const navigate = useNavigate();\n  const { courseId } = useParams();\n  // Only try to decode if courseId exists\n  const decodedCourseId = courseId ? decodeData(courseId) : null;\n  // console.log('Course ID:', courseId);\n  // console.log('Decoded Course ID:', decodedCourseId);\n\n  const isEditing = location.state?.isEditing;\n  const initialCourseData = location.state?.courseData;\n\n  const [currentStep, setCurrentStep] = useState(1);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [isLoadingCourse, setIsLoadingCourse] = useState(false);\n  const [formData, setFormData] = useState({\n    courseTitle: '',\n    courseDescription: '',\n    courseLevel: 'intermediate',\n    courseLanguage: 'english',\n    courseCategory: '',\n    thumbnail: null,\n    thumbnailPreview: '',\n    certificateType: '',\n    courseType: 'free',\n    currency: domain === 'lms.tpi.sg' ? 'SGD' : (domain === 'lms.nxgenvarsity.com' || domain === 'lms.creatorfoundation.in') ? 'INR' : 'INR',\n    price: '',\n    tags: [],\n    courseInfo: []\n  });\n\n  useEffect(() => {\n    if (isEditing && initialCourseData) {\n      setFormData(initialCourseData);\n    }\n  }, [isEditing, initialCourseData]);\n\n  // Fetch course details if courseId is available\n  useEffect(() => {\n    const fetchCourseDetails = async () => {\n      if (courseId && decodedCourseId) {\n        setIsLoadingCourse(true);\n        try {\n          console.log('Fetching course details for ID:', decodedCourseId);\n          const response = await getCourseDetailsById({ course_id: decodedCourseId });\n\n          console.log('get Course Details Response:', response);\n\n          if (response.success && response.data.course) {\n            const course = response.data.course;\n            console.log('Course details fetched:', course);\n\n            // Map API response to form data structure\n            setFormData({\n              courseTitle: course.course_name || '',\n              courseDescription: course.course_desc || '',\n              courseLevel: course.levels || '',\n              courseLanguage: course.course_language || '',\n              courseCategory: course.course_category || '',\n              thumbnail: null, // Will be handled separately if needed\n              thumbnailPreview: course.banner_image || '',\n              certificateType: course.certificate_template_id?.toString() || '',\n              courseType: course.course_type || 'free',\n              currency: course.currency || (domain === 'lms.tpi.sg' ? 'SGD' : (domain === 'lms.nxgenvarsity.com' || domain === 'lms.creatorfoundation.in') ? 'INR' : 'INR'),\n              price: course.course_price || '',\n              tags: Array.isArray(course.tags) ? course.tags : [],\n              courseInfo: Array.isArray(course.course_info) ? course.course_info : []\n            });\n\n            console.log('Form data populated from API');\n          } else {\n            console.error('Failed to fetch course details:', response.data?.error_msg);\n            alert('Failed to load course details: ' + (response.data?.error_msg || 'Unknown error'));\n          }\n        } catch (error) {\n          console.error('Error fetching course details:', error);\n          alert('Error loading course details. Please try again.');\n        } finally {\n          setIsLoadingCourse(false);\n        }\n      }\n    };\n\n    fetchCourseDetails();\n  }, [courseId, decodedCourseId]);\n\n  const [certificateData, setCertificateData] = useState([]);\n\n  const fetchCertificateData = async () => {\n    try {\n      const response = await getCourseCertificate();\n      // console.log('Certificate Data:', response);\n      if (response.success) {\n        setCertificateData(response.data.templates);\n      } else {\n        // console.error('Invalid certificate data format:', response);\n        setCertificateData([]); // Reset to empty array if invalid data\n      }\n    } catch (error) {\n      console.error('Error fetching certificate data:', error);\n      setCertificateData([]); // Reset to empty array on error\n    }\n  };\n\n  useEffect(() => {\n    fetchCertificateData();\n  }, []);\n\n  const [tagInput, setTagInput] = useState('');\n  const [infoInput, setInfoInput] = useState('');\n  const [editingInfo, setEditingInfo] = useState(null);\n\n  // Memoized handlers for tag and info inputs\n  const handleTagInputChange = useCallback((e) => {\n    setTagInput(e.target.value);\n  }, []);\n\n  const handleInfoInputChange = useCallback((e) => {\n    setInfoInput(e.target.value);\n  }, []);\n\n  const handleInputChange = useCallback((e) => {\n    const { name, value } = e.target;\n    setFormData(prevState => ({\n      ...prevState,\n      [name]: value\n    }));\n  }, []);\n\n  const handleThumbnailChange = (e) => {\n    const file = e.target.files[0];\n    if (file) {\n      setFormData(prevState => ({\n        ...prevState,\n        thumbnail: file,\n        thumbnailPreview: URL.createObjectURL(file)\n      }));\n    }\n  };\n\n  const handleRemoveFile = (type) => {\n    if (type === 'thumbnail') {\n      setFormData(prevState => ({\n        ...prevState,\n        thumbnail: null,\n        thumbnailPreview: ''\n      }));\n    }\n  };\n\n  const handleAddTag = useCallback((e) => {\n    e.preventDefault();\n    const tag = tagInput.trim();\n    if (tag && !formData.tags.includes(tag)) {\n      if (tag.length < 3) {\n        alert('Tag must be at least 3 characters long');\n        return;\n      }\n      if (formData.tags.length >= 8) {\n        alert('Maximum 8 tags allowed');\n        return;\n      }\n      setFormData(prev => ({\n        ...prev,\n        tags: [...prev.tags, tag]\n      }));\n      setTagInput('');\n    }\n  }, [tagInput, formData.tags]);\n\n  const handleRemoveTag = (tagToRemove) => {\n    setFormData(prev => ({\n      ...prev,\n      tags: prev.tags.filter(tag => tag !== tagToRemove)\n    }));\n  };\n\n  const handleTagInputKeyDown = (e) => {\n    if (e.key === 'Enter') {\n      e.preventDefault();\n      handleAddTag(e);\n    }\n  };\n\n  const handleAddInfo = useCallback((e) => {\n    e.preventDefault();\n    if (infoInput.trim()) {\n      if (editingInfo !== null) {\n        setFormData(prev => ({\n          ...prev,\n          courseInfo: prev.courseInfo.map((info, index) =>\n            index === editingInfo ? infoInput.trim() : info\n          )\n        }));\n        setEditingInfo(null);\n      } else {\n        setFormData(prev => ({\n          ...prev,\n          courseInfo: [...prev.courseInfo, infoInput.trim()]\n        }));\n      }\n      setInfoInput('');\n    }\n  }, [infoInput, editingInfo]);\n\n  const handleEditInfo = (index) => {\n    setInfoInput(formData.courseInfo[index]);\n    setEditingInfo(index);\n  };\n\n  const handleDeleteInfo = (index) => {\n    setFormData(prev => ({\n      ...prev,\n      courseInfo: prev.courseInfo.filter((_, i) => i !== index)\n    }));\n  };\n\n  const handleInfoInputKeyDown = (e) => {\n    if (e.key === 'Enter') {\n      e.preventDefault();\n      handleAddInfo(e);\n    }\n  };\n\n  const steps = [\n    { number: '01', title: 'Course Information' },\n    { number: '02', title: 'Course Media' },\n    { number: '03', title: 'Additional information' },\n    { number: '04', title: 'Pricing' },\n    { number: '05', title: 'Preview & Submit' }\n  ];\n\n  // Validation functions for each step\n  const validateStep1 = () => {\n    return formData.courseTitle.trim() !== '' &&\n           formData.courseDescription.trim() !== '' &&\n           formData.courseLevel !== '' &&\n           formData.courseLanguage !== '' &&\n           formData.courseCategory !== '';\n  };\n\n  const validateStep2 = () => {\n    // For editing mode, allow progression if thumbnail exists (either new upload or existing image)\n    if (courseId && decodedCourseId) {\n      return (formData.thumbnail !== null || formData.thumbnailPreview !== '') &&\n             formData.certificateType !== '';\n    }\n    // For new course creation, require new thumbnail upload\n    return formData.thumbnail !== null && formData.certificateType !== '';\n  };\n\n  const validateStep3 = () => {\n    // For editing mode, allow empty tags and courseInfo if course already exists\n    if (courseId && decodedCourseId) {\n      return true; // Allow progression in edit mode even with empty arrays\n    }\n    // For new course creation, require at least one tag and one course info\n    return formData.tags.length > 0 && formData.courseInfo.length > 0;\n  };\n\n  const validateStep4 = () => {\n    if (formData.courseType === 'free') {\n      return true;\n    }\n    return formData.price !== '' && parseFloat(formData.price) > 0;\n  };\n\n  const validateStep5 = () => {\n    // Preview step - all previous steps must be valid\n    return validateStep1() && validateStep2() && validateStep3() && validateStep4();\n  };\n\n  const isCurrentStepValid = () => {\n    switch (currentStep) {\n      case 1:\n        return validateStep1();\n      case 2:\n        return validateStep2();\n      case 3:\n        return validateStep3();\n      case 4:\n        return validateStep4();\n      case 5:\n        return validateStep5();\n      default:\n        return false;\n    }\n  };\n\n  const handleNext = () => {\n    if (currentStep < steps.length) {\n      setCurrentStep(currentStep + 1);\n    }\n  };\n\n  const handlePrevious = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n\n  const handleSubmit = async () => {\n    if (isCurrentStepValid()) {\n      setIsSubmitting(true);\n\n      try {\n        // Start the minimum 3-second timer\n        const minLoadingTime = new Promise(resolve => setTimeout(resolve, 3000));\n\n        // Create FormData object for file upload\n        const submitData = new FormData();\n\n        // Add all form fields to FormData\n        submitData.append('courseTitle', formData.courseTitle);\n        submitData.append('courseDescription', formData.courseDescription);\n        submitData.append('courseLevel', formData.courseLevel);\n        submitData.append('courseLanguage', formData.courseLanguage);\n        submitData.append('courseCategory', formData.courseCategory);\n        submitData.append('certificateType', formData.certificateType);\n        submitData.append('courseType', formData.courseType);\n        submitData.append('currency', formData.currency);\n        submitData.append('price', formData.price);\n        submitData.append('tags', JSON.stringify(formData.tags));\n        submitData.append('courseInfo', JSON.stringify(formData.courseInfo));\n        \n        console.log('Submit Data:', submitData);\n\n        // Add thumbnail file if exists (for new upload)\n        if (formData.thumbnail) {\n          submitData.append('banner_image', formData.thumbnail);\n        }\n\n        console.log('Submitting course data...');\n        console.log('Is editing mode:', courseId && decodedCourseId);\n\n        // Choose API call based on edit mode\n        let apiCall;\n        if (courseId && decodedCourseId) {\n          // Update existing course\n          apiCall = updateCourseDetails(decodedCourseId, submitData);\n        } else {\n          // Create new course\n          apiCall = createNewCourse(submitData);\n        }\n\n        // Wait for both minimum time and API response\n        const [response] = await Promise.all([apiCall, minLoadingTime]);\n\n        if (response.success) {\n          console.log('Course operation successful:', response.data);\n          // Navigate to courses page after successful operation\n          navigate('/admin/courses');\n        } else {\n          setIsSubmitting(false);\n          const errorMsg = courseId && decodedCourseId ? 'Failed to update course' : 'Failed to create course';\n          alert(errorMsg + ': ' + (response.data?.error_msg || 'Unknown error'));\n        }\n      } catch (error) {\n        setIsSubmitting(false);\n        console.error('Error with course operation:', error);\n        const errorMsg = courseId && decodedCourseId ? 'Error updating course' : 'Error creating course';\n        alert(errorMsg + '. Please try again.');\n      }\n    } else {\n      alert('Please fill in all required fields before submitting.');\n    }\n  };\n\n  const renderCourseInformationForm = () => (\n    <div className=\"course-information\">\n      <div className=\"row\">\n        <div className=\"col-12 mb-3\">\n          <label htmlFor=\"courseTitle\" className=\"form-label\">Course Title *</label>\n          <input\n            type=\"text\"\n            className=\"form-control\"\n            id=\"courseTitle\"\n            name=\"courseTitle\"\n            placeholder=\"Enter course title\"\n            value={formData.courseTitle}\n            onChange={handleInputChange}\n            maxLength={100}\n            required\n          />\n          <small className=\"text-muted\">Maximum 100 characters allowed. Use a short and meaningful title for better readability.</small>\n        </div>\n\n        <div className=\"col-12 mb-3\">\n          <label htmlFor=\"courseDescription\" className=\"form-label\">Course Description *</label>\n          <textarea\n            className=\"form-control\"\n            id=\"courseDescription\"\n            name=\"courseDescription\"\n            rows=\"4\"\n            placeholder=\"Enter course description\"\n            value={formData.courseDescription}\n            onChange={handleInputChange}\n            maxLength={1000}\n            required\n          ></textarea>\n          <small className=\"text-muted\">Maximum 1000 characters allowed</small>\n        </div>\n\n        <div className=\"col-md-4 mb-3\">\n          <label htmlFor=\"courseLevel\" className=\"form-label\">Course Level *</label>\n          <select\n            className=\"form-select\"\n            id=\"courseLevel\"\n            name=\"courseLevel\"\n            value={formData.courseLevel}\n            onChange={handleInputChange}\n            required\n          >\n            <option value=\"\">Select Level</option>\n            <option value=\"beginner\">Beginner</option>\n            <option value=\"intermediate\">Intermediate</option>\n            <option value=\"advanced\">Advanced</option>\n            <option value=\"all-levels\">All Levels</option>\n          </select>\n        </div>\n\n        <div className=\"col-md-4 mb-3\">\n          <label htmlFor=\"courseLanguage\" className=\"form-label\">Course Language *</label>\n          <select\n            className=\"form-select\"\n            id=\"courseLanguage\"\n            name=\"courseLanguage\"\n            value={formData.courseLanguage}\n            onChange={handleInputChange}\n            required\n          >\n            <option value=\"\">Select Language</option>\n            <option value=\"english\">English</option>\n            <option value=\"spanish\">Spanish</option>\n            <option value=\"french\">French</option>\n            <option value=\"german\">German</option>\n            <option value=\"chinese\">Chinese</option>\n            <option value=\"japanese\">Japanese</option>\n            <option value=\"hindi\">Hindi</option>\n          </select>\n        </div>\n\n        <div className=\"col-md-4 mb-3\">\n          <label htmlFor=\"courseCategory\" className=\"form-label\">Course Category *</label>\n          <select\n            className=\"form-select\"\n            id=\"courseCategory\"\n            name=\"courseCategory\"\n            value={formData.courseCategory}\n            onChange={handleInputChange}\n            required\n          >\n            <option value=\"\">Select Category</option>\n            {courseCategoriesData.categories.map((category, index) => (\n              <option key={index} value={category}>\n                {category}\n              </option>\n            ))}\n          </select>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderCourseMediaForm = () => (\n    <div className=\"course-media\">\n      <div className=\"row\">\n        {/* Thumbnail Upload */}\n        <div className=\"col-md-6 mb-4\">\n          <label className=\"form-label\">Course Thumbnail *</label>\n          <div className=\"card\">\n            <div className=\"card-body\">\n              {!formData.thumbnailPreview ? (\n                <div className=\"text-center p-4\">\n                  <input\n                    type=\"file\"\n                    id=\"thumbnail\"\n                    className=\"d-none\"\n                    accept=\"image/*\"\n                    onChange={handleThumbnailChange}\n                  />\n                  <label \n                    htmlFor=\"thumbnail\" \n                    className=\"btn btn-outline-primary mb-3 d-flex flex-column align-items-center gap-2 mx-auto\"\n                    style={{ width: 'fit-content', cursor: 'pointer' }}\n                  >\n                    <Icon icon=\"fluent:image-add-24-regular\" width=\"48\" height=\"48\" />\n                    <span>Upload Thumbnail</span>\n                    <small className=\"text-muted\">Recommended size: 1280x720px</small>\n                  </label>\n                </div>\n              ) : (\n                <div className=\"position-relative\">\n                  <img\n                    src={formData.thumbnailPreview}\n                    alt=\"Course Thumbnail\"\n                    className=\"img-fluid rounded\"\n                    style={{ width: '100%', height: 'auto' }}\n                  />\n                  <button\n                    className=\"btn btn-danger d-flex align-items-center justify-content-center p-0 position-absolute\"\n                    onClick={() => handleRemoveFile('thumbnail')}\n                    type=\"button\"\n                    style={{ \n                      width: '24px', \n                      height: '24px', \n                      top: '8px', \n                      right: '8px',\n                      minWidth: 'unset',\n                      borderRadius: '4px'\n                    }}\n                  >\n                    <Icon icon=\"fluent:delete-24-regular\" width=\"14\" height=\"14\" />\n                  </button>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Certificate Selection */}\n        <div className=\"col-md-6 mb-4\">\n          <label className=\"form-label\">Certificate Type *</label>\n          <div className=\"card\">\n            <div className=\"card-body\">\n              <select\n                className=\"form-select mb-3\"\n                name=\"certificateType\"\n                value={formData.certificateType}\n                onChange={handleInputChange}\n                required\n              >\n                <option value=\"\">Select Certificate Type</option>\n                {Array.isArray(certificateData) && certificateData.map((cert) => (\n                  <option key={cert.id || cert._id} value={cert.id || cert._id}>\n                    {cert.template_name || 'Unnamed Certificate'}\n                  </option>\n                ))}\n              </select>\n\n              {formData.certificateType && (\n                <div className=\"certificate-preview p-3 bg-light rounded\">\n                  <div className=\"d-flex align-items-center gap-3\">\n                    <Icon icon=\"fluent:certificate-24-regular\" width=\"32\" height=\"32\" className=\"text-primary\" />\n                    <div>\n                      <h6 className=\"mb-1\">{formData.certificateType.charAt(0).toUpperCase() + formData.certificateType.slice(1)} Certificate</h6>\n                      <small className=\"text-muted\">Will be issued upon course completion</small>\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderAdditionalInformationForm = () => (\n    <div className=\"row\">\n      <div className=\"col-12 mb-4\">\n        <div className=\"card\">\n          <div className=\"card-body\">\n            <div className=\"mb-4\">\n              <label className=\"form-label\">Course Tags *</label>\n              <div className=\"mb-2\">\n                <div className=\"row g-2\">\n                  <div className=\"col-10\">\n                    <input\n                      type=\"text\"\n                      className=\"form-control rounded-0 rounded-start\"\n                      placeholder=\"Add tags (e.g., 'programming', 'web development')\"\n                      value={tagInput}\n                      onChange={handleTagInputChange}\n                      onKeyDown={handleTagInputKeyDown}\n                    />\n                  </div>\n                  <div className=\"col-2\">\n                    <button \n                      className=\"btn btn-primary w-100 rounded-0 rounded-end\"\n                      onClick={handleAddTag}\n                    >\n                      Add Tag\n                    </button>\n                  </div>\n                </div>\n                <div className=\"mt-2\">\n                  <small className=\"d-block text-muted mb-1\">Press Enter or click Add Tag to add</small>\n                  <small className=\"d-block text-muted\">\n                    • Minimum 3 characters per tag\n                    <br />\n                    • Maximum 8 tags allowed\n                    <br />\n                    • {8 - formData.tags.length} tags remaining\n                  </small>\n                </div>\n              </div>\n              <div className=\"d-flex flex-wrap gap-2 mt-3\">\n                {formData.tags.map((tag, index) => (\n                  <div \n                    key={index}\n                    className=\"badge bg-light text-dark border d-flex align-items-center gap-2 py-2 px-3\"\n                  >\n                    {tag}\n                    <button\n                      type=\"button\"\n                      className=\"btn btn-link text-danger p-0 d-flex align-items-center\"\n                      onClick={() => handleRemoveTag(tag)}\n                      style={{ minWidth: '20px', height: '20px' }}\n                    >\n                      <Icon icon=\"fluent:dismiss-24-regular\" width=\"16\" height=\"16\" />\n                    </button>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            <div className=\"mb-4\">\n              <label className=\"form-label\">Course Info *</label>\n              <div className=\"mb-2\">\n                <div className=\"row g-2\">\n                  <div className=\"col-10\">\n                    <input\n                      type=\"text\"\n                      className=\"form-control rounded-0 rounded-start\"\n                      placeholder=\"Add course information point (e.g., 'Lifetime access to course materials')\"\n                      value={infoInput}\n                      onChange={handleInfoInputChange}\n                      onKeyDown={handleInfoInputKeyDown}\n                    />\n                  </div>\n                  <div className=\"col-2\">\n                    <button\n                      className=\"btn btn-primary w-100 rounded-0 rounded-end\"\n                      onClick={handleAddInfo}\n                    >\n                      {editingInfo !== null ? 'Update' : 'Add Info'}\n                    </button>\n                  </div>\n                </div>\n                <div className=\"mt-2\">\n                  <small className=\"d-block text-muted\">\n                    Add important information points about your course\n                  </small>\n                </div>\n              </div>\n\n              <div className=\"mt-3\">\n                {formData.courseInfo.map((info, index) => (\n                  <div\n                    key={index}\n                    className=\"d-flex align-items-center gap-2 p-2 border rounded mb-2\"\n                  >\n                    <Icon\n                      icon=\"fluent:info-24-regular\"\n                      className=\"text-primary flex-shrink-0\"\n                      width=\"20\"\n                      height=\"20\"\n                    />\n                    <span className=\"flex-grow-1\">{info}</span>\n                    <div className=\"d-flex gap-2\">\n                      <button\n                        type=\"button\"\n                        className=\"btn btn-link text-primary p-0 d-flex align-items-center\"\n                        onClick={() => handleEditInfo(index)}\n                        style={{ minWidth: '20px', height: '20px' }}\n                      >\n                        <Icon icon=\"fluent:edit-24-regular\" width=\"16\" height=\"16\" />\n                      </button>\n                      <button\n                        type=\"button\"\n                        className=\"btn btn-link text-danger p-0 d-flex align-items-center\"\n                        onClick={() => handleDeleteInfo(index)}\n                        style={{ minWidth: '20px', height: '20px' }}\n                      >\n                        <Icon icon=\"fluent:delete-24-regular\" width=\"16\" height=\"16\" />\n                      </button>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderPreviewStep = () => (\n    <div className=\"row\">\n      <div className=\"col-12\">\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h5 className=\"mb-0\">\n              <Icon icon=\"fluent:eye-24-regular\" width=\"20\" height=\"20\" className=\"me-2\" />\n              Course Preview\n            </h5>\n            <p className=\"text-muted mb-0 mt-2\">\n              Review all your course information before submitting. If you need to make changes, use the \"Previous\" button to go back to any step.\n            </p>\n          </div>\n          <div className=\"card-body\">\n            {/* Course Information Section */}\n            <div className=\"mb-4\">\n              <h6 className=\"text-primary mb-3\">\n                <Icon icon=\"fluent:info-24-regular\" width=\"18\" height=\"18\" className=\"me-2\" />\n                Course Information\n              </h6>\n              <div className=\"row\">\n                <div className=\"col-md-6 mb-3\">\n                  <label className=\"form-label text-muted\">Course Title</label>\n                  <p className=\"mb-0 fw-medium\">{formData.courseTitle || 'Not specified'}</p>\n                </div>\n                <div className=\"col-md-6 mb-3\">\n                  <label className=\"form-label text-muted\">Course Category</label>\n                  <p className=\"mb-0\">{formData.courseCategory || 'Not specified'}</p>\n                </div>\n                <div className=\"col-12 mb-3\">\n                  <label className=\"form-label text-muted\">Course Description</label>\n                  <p className=\"mb-0\">{formData.courseDescription || 'Not specified'}</p>\n                </div>\n                <div className=\"col-md-4 mb-3\">\n                  <label className=\"form-label text-muted\">Level</label>\n                  <p className=\"mb-0 text-capitalize\">{formData.courseLevel || 'Not specified'}</p>\n                </div>\n                <div className=\"col-md-4 mb-3\">\n                  <label className=\"form-label text-muted\">Language</label>\n                  <p className=\"mb-0 text-capitalize\">{formData.courseLanguage || 'Not specified'}</p>\n                </div>\n                <div className=\"col-md-4 mb-3\">\n                  <label className=\"form-label text-muted\">Certificate</label>\n                  <p className=\"mb-0\">{formData.certificateType ? `Certificate ID: ${formData.certificateType}` : 'Not specified'}</p>\n                </div>\n              </div>\n            </div>\n\n            <hr />\n\n            {/* Course Media Section */}\n            <div className=\"mb-4\">\n              <h6 className=\"text-primary mb-3\">\n                <Icon icon=\"fluent:image-24-regular\" width=\"18\" height=\"18\" className=\"me-2\" />\n                Course Media\n              </h6>\n              <div className=\"row\">\n                <div className=\"col-md-6 mb-3\">\n                  <label className=\"form-label text-muted\">Course Thumbnail</label>\n                  {formData.thumbnailPreview ? (\n                    <div>\n                      <img\n                        src={formData.thumbnailPreview}\n                        alt=\"Course Thumbnail\"\n                        className=\"img-fluid rounded border\"\n                        style={{ maxHeight: '150px', width: 'auto' }}\n                      />\n                    </div>\n                  ) : (\n                    <p className=\"mb-0 text-muted\">No thumbnail uploaded</p>\n                  )}\n                </div>\n              </div>\n            </div>\n\n            <hr />\n\n            {/* Additional Information Section */}\n            <div className=\"mb-4\">\n              <h6 className=\"text-primary mb-3\">\n                <Icon icon=\"fluent:tag-24-regular\" width=\"18\" height=\"18\" className=\"me-2\" />\n                Additional Information\n              </h6>\n              <div className=\"row\">\n                <div className=\"col-md-6 mb-3\">\n                  <label className=\"form-label text-muted\">Tags</label>\n                  {formData.tags.length > 0 ? (\n                    <div className=\"d-flex flex-wrap gap-2\">\n                      {formData.tags.map((tag, index) => (\n                        <span key={index} className=\"badge bg-light text-dark border\">\n                          {tag}\n                        </span>\n                      ))}\n                    </div>\n                  ) : (\n                    <p className=\"mb-0 text-muted\">No tags added</p>\n                  )}\n                </div>\n                <div className=\"col-md-6 mb-3\">\n                  <label className=\"form-label text-muted\">Course Information Points</label>\n                  {formData.courseInfo.length > 0 ? (\n                    <ul className=\"list-unstyled mb-0\">\n                      {formData.courseInfo.map((info, index) => (\n                        <li key={index} className=\"mb-1\">\n                          <Icon icon=\"fluent:checkmark-circle-24-regular\" width=\"16\" height=\"16\" className=\"text-success me-2\" />\n                          {info}\n                        </li>\n                      ))}\n                    </ul>\n                  ) : (\n                    <p className=\"mb-0 text-muted\">No course information added</p>\n                  )}\n                </div>\n              </div>\n            </div>\n\n            <hr />\n\n            {/* Pricing Section */}\n            <div className=\"mb-4\">\n              <h6 className=\"text-primary mb-3\">\n                <Icon icon=\"fluent:payment-24-regular\" width=\"18\" height=\"18\" className=\"me-2\" />\n                Pricing Information\n              </h6>\n              <div className=\"row\">\n                <div className=\"col-md-3 mb-3\">\n                  <label className=\"form-label text-muted\">Course Type</label>\n                  <p className=\"mb-0\">\n                    <span className={`badge ${formData.courseType === 'free' ? 'bg-success' : 'bg-primary'}`}>\n                      {formData.courseType === 'free' ? 'Free Course' : 'Paid Course'}\n                    </span>\n                  </p>\n                </div>\n                {formData.courseType === 'paid' && (\n                  <>\n                    <div className=\"col-md-3 mb-3\">\n                      <label className=\"form-label text-muted\">Currency</label>\n                      <p className=\"mb-0\">{formData.currency}</p>\n                    </div>\n                    <div className=\"col-md-3 mb-3\">\n                      <label className=\"form-label text-muted\">Price</label>\n                      <p className=\"mb-0 fw-medium\">\n                        {formData.currency === 'USD' && '$'}\n                        {formData.currency === 'EUR' && '€'}\n                        {formData.currency === 'GBP' && '£'}\n                        {formData.currency === 'INR' && '₹'}\n                        {formData.currency === 'SGD' && 'S$'}\n                        {formData.price}\n                      </p>\n                    </div>\n                  </>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderPricingStep = () => (\n    <div className=\"row\">\n      <div className=\"col-12 mb-4\">\n        <div className=\"card\">\n          <div className=\"card-body\">\n  \n            <div className=\"mb-4\">\n              <label className=\"form-label\">Course Type *</label>\n              <div className=\"d-flex gap-4\">\n                <div className=\"form-check\">\n                  <input\n                    type=\"radio\"\n                    className=\"form-check-input\"\n                    id=\"free\"\n                    name=\"courseType\"\n                    value=\"free\"\n                    checked={formData.courseType === 'free'}\n                    onChange={(e) => setFormData(prev => ({\n                      ...prev,\n                      courseType: e.target.value,\n                      price: '',\n                      currency: domain === 'lms.tpi.sg' ? 'SGD' : (domain === 'lms.nxgenvarsity.com' || domain === 'lms.creatorfoundation.in') ? 'INR' : 'INR'\n                    }))}\n                  />\n                  <label className=\"form-check-label\" htmlFor=\"free\">\n                    <strong>Free Course</strong>\n                    <br />\n                    <small className=\"text-muted\">No payment required for enrollment</small>\n                  </label>\n                </div>\n                <div className=\"form-check\">\n                  <input\n                    type=\"radio\"\n                    className=\"form-check-input\"\n                    id=\"paid\"\n                    name=\"courseType\"\n                    value=\"paid\"\n                    checked={formData.courseType === 'paid'}\n                    onChange={(e) => setFormData(prev => ({\n                      ...prev,\n                      courseType: e.target.value\n                    }))}\n                  />\n                  <label className=\"form-check-label\" htmlFor=\"paid\">\n                    <strong>Paid Course</strong>\n                    <br />\n                    <small className=\"text-muted\">Students must purchase to access content</small>\n                  </label>\n                </div>\n              </div>\n            </div>\n\n            {formData.courseType === 'paid' && (\n  <>\n    <div className=\"mb-3\">\n      <label className=\"form-label\">Select Currency</label>\n      <div className=\"mb-2\">\n        <small className=\"text-muted\">Debug: Domain = \"{domain}\"</small>\n        <br />\n        <small className=\"text-muted\">\n          TPI Check: {domain === 'lms.tpi.sg' ? 'TRUE' : 'FALSE'} | \n          NxGen Check: {domain === 'lms.nxgenvarsity.com' ? 'TRUE' : 'FALSE'} | \n          Creator Check: {domain === 'lms.creatorfoundation.in' ? 'TRUE' : 'FALSE'}\n        </small>\n      </div>\n      <select\n        className=\"form-select\"\n        value={formData.currency}\n        onChange={(e) =>\n          setFormData((prev) => ({\n            ...prev,\n            currency: e.target.value,\n          }))\n        }\n      >\n        {domain === 'lms.tpi.sg' ? (\n          <option value=\"SGD\">SGD (S$)</option>\n        ) : (domain === 'lms.nxgenvarsity.com' || domain === 'lms.creatorfoundation.in') ? (\n          <option value=\"INR\">INR (₹)</option>\n        ) : (\n          <>\n            <option value=\"INR\">INR (₹)</option>\n            <option value=\"USD\">USD ($)</option>\n            <option value=\"EUR\">EUR (€)</option>\n            <option value=\"GBP\">GBP (£)</option>\n          </>\n        )}\n      </select>\n\n      <small className=\"text-muted\">\n        {domain === 'lms.tpi.sg' ? 'Currency is fixed to SGD for this platform.' :\n         (domain === 'lms.nxgenvarsity.com' || domain === 'lms.creatorfoundation.in') ? 'Currency is fixed to INR for this platform.' :\n         'Select the currency for your course pricing.'}\n      </small>\n    </div>\n\n    <div className=\"mb-3\">\n      <label className=\"form-label\">Course Price *</label>\n      <div className=\"input-group\">\n        <span className=\"input-group-text\">\n          {formData.currency === 'INR' && '₹'}\n          {formData.currency === 'SGD' && 'S$'}\n          {formData.currency === 'USD' && '$'}\n          {formData.currency === 'EUR' && '€'}\n          {formData.currency === 'GBP' && '£'}\n        </span>\n        <input\n          type=\"number\"\n          className=\"form-control\"\n          placeholder=\"Enter course price\"\n          value={formData.price}\n          onChange={(e) =>\n            setFormData((prev) => ({\n              ...prev,\n              price: e.target.value,\n            }))\n          }\n          required\n        />\n      </div>\n    </div>\n  </>\n)}\n\n\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  const renderStepContent = () => {\n    switch (currentStep) {\n      case 1:\n        return renderCourseInformationForm();\n      case 2:\n        return renderCourseMediaForm();\n      case 3:\n        return renderAdditionalInformationForm();\n      case 4:\n        return renderPricingStep();\n      case 5:\n        return renderPreviewStep();\n      default:\n        return null;\n    }\n  };\n\n  // Loading overlay component for submission\n  const SubmissionLoadingOverlay = () => {\n    const isEditMode = courseId && decodedCourseId;\n    return (\n      <div className=\"position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center\"\n           style={{\n             backgroundColor: 'rgba(0, 0, 0, 0.7)',\n             zIndex: 9999\n           }}>\n        <div className=\"text-center text-white\">\n          <div className=\"spinner-border text-primary mb-3\" role=\"status\" style={{ width: '3rem', height: '3rem' }}>\n            <span className=\"visually-hidden\">Loading...</span>\n          </div>\n          <h5 className=\"mb-2\">{isEditMode ? 'Updating Your Course...' : 'Creating Your Course...'}</h5>\n          <p className=\"mb-0\">Please wait while we process your course information.</p>\n        </div>\n      </div>\n    );\n  };\n\n  // Loading overlay component for fetching course details\n  const CourseLoadingOverlay = () => (\n    <div className=\"position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center\"\n         style={{\n           backgroundColor: 'rgba(0, 0, 0, 0.7)',\n           zIndex: 9999\n         }}>\n      <div className=\"text-center text-white\">\n        <div className=\"spinner-border text-primary mb-3\" role=\"status\" style={{ width: '3rem', height: '3rem' }}>\n          <span className=\"visually-hidden\">Loading...</span>\n        </div>\n        <h5 className=\"mb-2\">Loading Course Details...</h5>\n        <p className=\"mb-0\">Please wait while we fetch the course information.</p>\n      </div>\n    </div>\n  );\n\n  return (\n    <div className=\"container\">\n      {/* Loading Overlays */}\n      {isSubmitting && <SubmissionLoadingOverlay />}\n      {isLoadingCourse && <CourseLoadingOverlay />}\n\n      {/* Steps Header */}\n      <div className=\"stepper-card\">\n        <div className=\"stepper-wrapper\">\n          {/* Background Line */}\n          <div className=\"stepper-line-bg\"></div>\n          {/* Progress Line */}\n          <div \n            className=\"stepper-line-progress\"\n            style={{ \n              width: `${((currentStep - 1) / (steps.length - 1)) * 100}%`\n            }}\n          ></div>\n          \n          {/* Step Indicators */}\n          <div className=\"stepper-content\">\n            {steps.map((step, index) => (\n              <div \n                key={step.number} \n                className=\"stepper-item\"\n              >\n                <div \n                  className={`stepper-circle ${\n                    currentStep > index + 1 ? 'completed' :\n                    currentStep === index + 1 ? 'active' : ''\n                  }`}\n                >\n                  {step.number}\n                </div>\n                <div className={`stepper-label ${currentStep === index + 1 ? 'active' : ''}`}>\n                  {step.title}\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Content Area */}\n      <div className=\"stepper-card\">\n        {renderStepContent()}\n      </div>\n\n      {/* Navigation Buttons */}\n      <div className=\"stepper-card\">\n        <div className=\"stepper-content\">\n          <button\n            className=\"btn-previous\"\n            onClick={handlePrevious}\n            disabled={currentStep === 1 || isSubmitting}\n          >\n            <Icon icon=\"fluent:arrow-left-24-regular\" width=\"20\" height=\"20\" />\n            Previous\n          </button>\n          {currentStep === steps.length ? (\n            <button\n              className=\"btn-next\"\n              onClick={handleSubmit}\n              disabled={!isCurrentStepValid() || isSubmitting}\n            >\n              {isSubmitting ? (\n                <>\n                  <div className=\"spinner-border spinner-border-sm me-2\" role=\"status\">\n                    <span className=\"visually-hidden\">Loading...</span>\n                  </div>\n                  {courseId && decodedCourseId ? 'Updating Course...' : 'Creating Course...'}\n                </>\n              ) : (\n                <>\n                  <Icon icon=\"fluent:checkmark-24-regular\" width=\"20\" height=\"20\" className=\"me-2\" />\n                  {courseId && decodedCourseId ? 'Update Course' : 'Submit Course'}\n                </>\n              )}\n            </button>\n          ) : (\n            <button\n              className=\"btn-next\"\n              onClick={handleNext}\n              disabled={!isCurrentStepValid()}\n            >\n              {currentStep === steps.length - 1 ? 'Preview' : 'Next'}\n              <Icon icon=\"fluent:arrow-right-24-regular\" width=\"20\" height=\"20\" />\n            </button>\n          )}\n        </div>\n      </div>\n\n    </div>\n  );\n}\n\nexport default CreateCourse;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,IAAI,QAAQ,gBAAgB;AACrC,SAASC,WAAW,EAAEC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACtE,OAAO,oBAAoB;AAC3B,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,oBAAoB,EAAEC,eAAe,EAAEC,oBAAoB,EAAEC,mBAAmB,QAAQ,gCAAgC;AACjI,OAAOC,oBAAoB,MAAM,sCAAsC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACxE,MAAMC,MAAM,IAAAC,qBAAA,GAAGC,OAAO,CAACC,GAAG,CAACC,oBAAoB,cAAAH,qBAAA,uBAAhCA,qBAAA,CAAkCI,IAAI,CAAC,CAAC;AACvDC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEL,OAAO,CAACC,GAAG,CAACC,oBAAoB,CAAC;AACjEE,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,OAAOP,MAAM,CAAC;AAC1CM,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEP,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEQ,MAAM,CAAC;AAC7CF,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEP,MAAM,CAAC;AACtCM,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEP,MAAM,KAAK,YAAY,CAAC;AACzDM,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEP,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAES,KAAK,CAAC,EAAE,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9E;;AAEA,SAASC,YAAYA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,gBAAA;EACtB,MAAMC,QAAQ,GAAG9B,WAAW,CAAC,CAAC;EAC9B,MAAM+B,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE8B;EAAS,CAAC,GAAG/B,SAAS,CAAC,CAAC;EAChC;EACA,MAAMgC,eAAe,GAAGD,QAAQ,GAAG7B,UAAU,CAAC6B,QAAQ,CAAC,GAAG,IAAI;EAC9D;EACA;;EAEA,MAAME,SAAS,IAAAN,eAAA,GAAGE,QAAQ,CAACK,KAAK,cAAAP,eAAA,uBAAdA,eAAA,CAAgBM,SAAS;EAC3C,MAAME,iBAAiB,IAAAP,gBAAA,GAAGC,QAAQ,CAACK,KAAK,cAAAN,gBAAA,uBAAdA,gBAAA,CAAgBQ,UAAU;EAEpD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG3C,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC8C,eAAe,EAAEC,kBAAkB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACgD,QAAQ,EAAEC,WAAW,CAAC,GAAGjD,QAAQ,CAAC;IACvCkD,WAAW,EAAE,EAAE;IACfC,iBAAiB,EAAE,EAAE;IACrBC,WAAW,EAAE,cAAc;IAC3BC,cAAc,EAAE,SAAS;IACzBC,cAAc,EAAE,EAAE;IAClBC,SAAS,EAAE,IAAI;IACfC,gBAAgB,EAAE,EAAE;IACpBC,eAAe,EAAE,EAAE;IACnBC,UAAU,EAAE,MAAM;IAClBC,QAAQ,EAAE1C,MAAM,KAAK,YAAY,GAAG,KAAK,GAAIA,MAAM,KAAK,sBAAsB,IAAIA,MAAM,KAAK,0BAA0B,GAAI,KAAK,GAAG,KAAK;IACxI2C,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,EAAE;IACRC,UAAU,EAAE;EACd,CAAC,CAAC;EAEF7D,SAAS,CAAC,MAAM;IACd,IAAIqC,SAAS,IAAIE,iBAAiB,EAAE;MAClCS,WAAW,CAACT,iBAAiB,CAAC;IAChC;EACF,CAAC,EAAE,CAACF,SAAS,EAAEE,iBAAiB,CAAC,CAAC;;EAElC;EACAvC,SAAS,CAAC,MAAM;IACd,MAAM8D,kBAAkB,GAAG,MAAAA,CAAA,KAAY;MACrC,IAAI3B,QAAQ,IAAIC,eAAe,EAAE;QAC/BU,kBAAkB,CAAC,IAAI,CAAC;QACxB,IAAI;UACFxB,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEa,eAAe,CAAC;UAC/D,MAAM2B,QAAQ,GAAG,MAAMtD,oBAAoB,CAAC;YAAEuD,SAAS,EAAE5B;UAAgB,CAAC,CAAC;UAE3Ed,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEwC,QAAQ,CAAC;UAErD,IAAIA,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,CAACC,MAAM,EAAE;YAAA,IAAAC,qBAAA;YAC5C,MAAMD,MAAM,GAAGJ,QAAQ,CAACG,IAAI,CAACC,MAAM;YACnC7C,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE4C,MAAM,CAAC;;YAE9C;YACAnB,WAAW,CAAC;cACVC,WAAW,EAAEkB,MAAM,CAACE,WAAW,IAAI,EAAE;cACrCnB,iBAAiB,EAAEiB,MAAM,CAACG,WAAW,IAAI,EAAE;cAC3CnB,WAAW,EAAEgB,MAAM,CAACI,MAAM,IAAI,EAAE;cAChCnB,cAAc,EAAEe,MAAM,CAACK,eAAe,IAAI,EAAE;cAC5CnB,cAAc,EAAEc,MAAM,CAACM,eAAe,IAAI,EAAE;cAC5CnB,SAAS,EAAE,IAAI;cAAE;cACjBC,gBAAgB,EAAEY,MAAM,CAACO,YAAY,IAAI,EAAE;cAC3ClB,eAAe,EAAE,EAAAY,qBAAA,GAAAD,MAAM,CAACQ,uBAAuB,cAAAP,qBAAA,uBAA9BA,qBAAA,CAAgCQ,QAAQ,CAAC,CAAC,KAAI,EAAE;cACjEnB,UAAU,EAAEU,MAAM,CAACU,WAAW,IAAI,MAAM;cACxCnB,QAAQ,EAAES,MAAM,CAACT,QAAQ,KAAK1C,MAAM,KAAK,YAAY,GAAG,KAAK,GAAIA,MAAM,KAAK,sBAAsB,IAAIA,MAAM,KAAK,0BAA0B,GAAI,KAAK,GAAG,KAAK,CAAC;cAC7J2C,KAAK,EAAEQ,MAAM,CAACW,YAAY,IAAI,EAAE;cAChClB,IAAI,EAAEmB,KAAK,CAACC,OAAO,CAACb,MAAM,CAACP,IAAI,CAAC,GAAGO,MAAM,CAACP,IAAI,GAAG,EAAE;cACnDC,UAAU,EAAEkB,KAAK,CAACC,OAAO,CAACb,MAAM,CAACc,WAAW,CAAC,GAAGd,MAAM,CAACc,WAAW,GAAG;YACvE,CAAC,CAAC;YAEF3D,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;UAC7C,CAAC,MAAM;YAAA,IAAA2D,cAAA,EAAAC,eAAA;YACL7D,OAAO,CAAC8D,KAAK,CAAC,iCAAiC,GAAAF,cAAA,GAAEnB,QAAQ,CAACG,IAAI,cAAAgB,cAAA,uBAAbA,cAAA,CAAeG,SAAS,CAAC;YAC1EC,KAAK,CAAC,iCAAiC,IAAI,EAAAH,eAAA,GAAApB,QAAQ,CAACG,IAAI,cAAAiB,eAAA,uBAAbA,eAAA,CAAeE,SAAS,KAAI,eAAe,CAAC,CAAC;UAC1F;QACF,CAAC,CAAC,OAAOD,KAAK,EAAE;UACd9D,OAAO,CAAC8D,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;UACtDE,KAAK,CAAC,iDAAiD,CAAC;QAC1D,CAAC,SAAS;UACRxC,kBAAkB,CAAC,KAAK,CAAC;QAC3B;MACF;IACF,CAAC;IAEDgB,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAAC3B,QAAQ,EAAEC,eAAe,CAAC,CAAC;EAE/B,MAAM,CAACmD,eAAe,EAAEC,kBAAkB,CAAC,GAAGzF,QAAQ,CAAC,EAAE,CAAC;EAE1D,MAAM0F,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,MAAM1B,QAAQ,GAAG,MAAMxD,oBAAoB,CAAC,CAAC;MAC7C;MACA,IAAIwD,QAAQ,CAACE,OAAO,EAAE;QACpBuB,kBAAkB,CAACzB,QAAQ,CAACG,IAAI,CAACwB,SAAS,CAAC;MAC7C,CAAC,MAAM;QACL;QACAF,kBAAkB,CAAC,EAAE,CAAC,CAAC,CAAC;MAC1B;IACF,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACd9D,OAAO,CAAC8D,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxDI,kBAAkB,CAAC,EAAE,CAAC,CAAC,CAAC;IAC1B;EACF,CAAC;EAEDxF,SAAS,CAAC,MAAM;IACdyF,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM,CAACE,QAAQ,EAAEC,WAAW,CAAC,GAAG7F,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC8F,SAAS,EAAEC,YAAY,CAAC,GAAG/F,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACgG,WAAW,EAAEC,cAAc,CAAC,GAAGjG,QAAQ,CAAC,IAAI,CAAC;;EAEpD;EACA,MAAMkG,oBAAoB,GAAGhG,WAAW,CAAEiG,CAAC,IAAK;IAC9CN,WAAW,CAACM,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC7B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,qBAAqB,GAAGpG,WAAW,CAAEiG,CAAC,IAAK;IAC/CJ,YAAY,CAACI,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC9B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAME,iBAAiB,GAAGrG,WAAW,CAAEiG,CAAC,IAAK;IAC3C,MAAM;MAAEK,IAAI;MAAEH;IAAM,CAAC,GAAGF,CAAC,CAACC,MAAM;IAChCnD,WAAW,CAACwD,SAAS,KAAK;MACxB,GAAGA,SAAS;MACZ,CAACD,IAAI,GAAGH;IACV,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,qBAAqB,GAAIP,CAAC,IAAK;IACnC,MAAMQ,IAAI,GAAGR,CAAC,CAACC,MAAM,CAACQ,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAID,IAAI,EAAE;MACR1D,WAAW,CAACwD,SAAS,KAAK;QACxB,GAAGA,SAAS;QACZlD,SAAS,EAAEoD,IAAI;QACfnD,gBAAgB,EAAEqD,GAAG,CAACC,eAAe,CAACH,IAAI;MAC5C,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMI,gBAAgB,GAAIC,IAAI,IAAK;IACjC,IAAIA,IAAI,KAAK,WAAW,EAAE;MACxB/D,WAAW,CAACwD,SAAS,KAAK;QACxB,GAAGA,SAAS;QACZlD,SAAS,EAAE,IAAI;QACfC,gBAAgB,EAAE;MACpB,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMyD,YAAY,GAAG/G,WAAW,CAAEiG,CAAC,IAAK;IACtCA,CAAC,CAACe,cAAc,CAAC,CAAC;IAClB,MAAMC,GAAG,GAAGvB,QAAQ,CAACtE,IAAI,CAAC,CAAC;IAC3B,IAAI6F,GAAG,IAAI,CAACnE,QAAQ,CAACa,IAAI,CAACuD,QAAQ,CAACD,GAAG,CAAC,EAAE;MACvC,IAAIA,GAAG,CAAC1F,MAAM,GAAG,CAAC,EAAE;QAClB8D,KAAK,CAAC,wCAAwC,CAAC;QAC/C;MACF;MACA,IAAIvC,QAAQ,CAACa,IAAI,CAACpC,MAAM,IAAI,CAAC,EAAE;QAC7B8D,KAAK,CAAC,wBAAwB,CAAC;QAC/B;MACF;MACAtC,WAAW,CAACoE,IAAI,KAAK;QACnB,GAAGA,IAAI;QACPxD,IAAI,EAAE,CAAC,GAAGwD,IAAI,CAACxD,IAAI,EAAEsD,GAAG;MAC1B,CAAC,CAAC,CAAC;MACHtB,WAAW,CAAC,EAAE,CAAC;IACjB;EACF,CAAC,EAAE,CAACD,QAAQ,EAAE5C,QAAQ,CAACa,IAAI,CAAC,CAAC;EAE7B,MAAMyD,eAAe,GAAIC,WAAW,IAAK;IACvCtE,WAAW,CAACoE,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPxD,IAAI,EAAEwD,IAAI,CAACxD,IAAI,CAAC2D,MAAM,CAACL,GAAG,IAAIA,GAAG,KAAKI,WAAW;IACnD,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,qBAAqB,GAAItB,CAAC,IAAK;IACnC,IAAIA,CAAC,CAACuB,GAAG,KAAK,OAAO,EAAE;MACrBvB,CAAC,CAACe,cAAc,CAAC,CAAC;MAClBD,YAAY,CAACd,CAAC,CAAC;IACjB;EACF,CAAC;EAED,MAAMwB,aAAa,GAAGzH,WAAW,CAAEiG,CAAC,IAAK;IACvCA,CAAC,CAACe,cAAc,CAAC,CAAC;IAClB,IAAIpB,SAAS,CAACxE,IAAI,CAAC,CAAC,EAAE;MACpB,IAAI0E,WAAW,KAAK,IAAI,EAAE;QACxB/C,WAAW,CAACoE,IAAI,KAAK;UACnB,GAAGA,IAAI;UACPvD,UAAU,EAAEuD,IAAI,CAACvD,UAAU,CAACnC,GAAG,CAAC,CAACiG,IAAI,EAAEC,KAAK,KAC1CA,KAAK,KAAK7B,WAAW,GAAGF,SAAS,CAACxE,IAAI,CAAC,CAAC,GAAGsG,IAC7C;QACF,CAAC,CAAC,CAAC;QACH3B,cAAc,CAAC,IAAI,CAAC;MACtB,CAAC,MAAM;QACLhD,WAAW,CAACoE,IAAI,KAAK;UACnB,GAAGA,IAAI;UACPvD,UAAU,EAAE,CAAC,GAAGuD,IAAI,CAACvD,UAAU,EAAEgC,SAAS,CAACxE,IAAI,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;MACL;MACAyE,YAAY,CAAC,EAAE,CAAC;IAClB;EACF,CAAC,EAAE,CAACD,SAAS,EAAEE,WAAW,CAAC,CAAC;EAE5B,MAAM8B,cAAc,GAAID,KAAK,IAAK;IAChC9B,YAAY,CAAC/C,QAAQ,CAACc,UAAU,CAAC+D,KAAK,CAAC,CAAC;IACxC5B,cAAc,CAAC4B,KAAK,CAAC;EACvB,CAAC;EAED,MAAME,gBAAgB,GAAIF,KAAK,IAAK;IAClC5E,WAAW,CAACoE,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPvD,UAAU,EAAEuD,IAAI,CAACvD,UAAU,CAAC0D,MAAM,CAAC,CAACQ,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKJ,KAAK;IAC1D,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMK,sBAAsB,GAAI/B,CAAC,IAAK;IACpC,IAAIA,CAAC,CAACuB,GAAG,KAAK,OAAO,EAAE;MACrBvB,CAAC,CAACe,cAAc,CAAC,CAAC;MAClBS,aAAa,CAACxB,CAAC,CAAC;IAClB;EACF,CAAC;EAED,MAAMgC,KAAK,GAAG,CACZ;IAAEC,MAAM,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAqB,CAAC,EAC7C;IAAED,MAAM,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAe,CAAC,EACvC;IAAED,MAAM,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAyB,CAAC,EACjD;IAAED,MAAM,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAU,CAAC,EAClC;IAAED,MAAM,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAmB,CAAC,CAC5C;;EAED;EACA,MAAMC,aAAa,GAAGA,CAAA,KAAM;IAC1B,OAAOtF,QAAQ,CAACE,WAAW,CAAC5B,IAAI,CAAC,CAAC,KAAK,EAAE,IAClC0B,QAAQ,CAACG,iBAAiB,CAAC7B,IAAI,CAAC,CAAC,KAAK,EAAE,IACxC0B,QAAQ,CAACI,WAAW,KAAK,EAAE,IAC3BJ,QAAQ,CAACK,cAAc,KAAK,EAAE,IAC9BL,QAAQ,CAACM,cAAc,KAAK,EAAE;EACvC,CAAC;EAED,MAAMiF,aAAa,GAAGA,CAAA,KAAM;IAC1B;IACA,IAAInG,QAAQ,IAAIC,eAAe,EAAE;MAC/B,OAAO,CAACW,QAAQ,CAACO,SAAS,KAAK,IAAI,IAAIP,QAAQ,CAACQ,gBAAgB,KAAK,EAAE,KAChER,QAAQ,CAACS,eAAe,KAAK,EAAE;IACxC;IACA;IACA,OAAOT,QAAQ,CAACO,SAAS,KAAK,IAAI,IAAIP,QAAQ,CAACS,eAAe,KAAK,EAAE;EACvE,CAAC;EAED,MAAM+E,aAAa,GAAGA,CAAA,KAAM;IAC1B;IACA,IAAIpG,QAAQ,IAAIC,eAAe,EAAE;MAC/B,OAAO,IAAI,CAAC,CAAC;IACf;IACA;IACA,OAAOW,QAAQ,CAACa,IAAI,CAACpC,MAAM,GAAG,CAAC,IAAIuB,QAAQ,CAACc,UAAU,CAACrC,MAAM,GAAG,CAAC;EACnE,CAAC;EAED,MAAMgH,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIzF,QAAQ,CAACU,UAAU,KAAK,MAAM,EAAE;MAClC,OAAO,IAAI;IACb;IACA,OAAOV,QAAQ,CAACY,KAAK,KAAK,EAAE,IAAI8E,UAAU,CAAC1F,QAAQ,CAACY,KAAK,CAAC,GAAG,CAAC;EAChE,CAAC;EAED,MAAM+E,aAAa,GAAGA,CAAA,KAAM;IAC1B;IACA,OAAOL,aAAa,CAAC,CAAC,IAAIC,aAAa,CAAC,CAAC,IAAIC,aAAa,CAAC,CAAC,IAAIC,aAAa,CAAC,CAAC;EACjF,CAAC;EAED,MAAMG,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,QAAQlG,WAAW;MACjB,KAAK,CAAC;QACJ,OAAO4F,aAAa,CAAC,CAAC;MACxB,KAAK,CAAC;QACJ,OAAOC,aAAa,CAAC,CAAC;MACxB,KAAK,CAAC;QACJ,OAAOC,aAAa,CAAC,CAAC;MACxB,KAAK,CAAC;QACJ,OAAOC,aAAa,CAAC,CAAC;MACxB,KAAK,CAAC;QACJ,OAAOE,aAAa,CAAC,CAAC;MACxB;QACE,OAAO,KAAK;IAChB;EACF,CAAC;EAED,MAAME,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAInG,WAAW,GAAGyF,KAAK,CAAC1G,MAAM,EAAE;MAC9BkB,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;IACjC;EACF,CAAC;EAED,MAAMoG,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIpG,WAAW,GAAG,CAAC,EAAE;MACnBC,cAAc,CAACD,WAAW,GAAG,CAAC,CAAC;IACjC;EACF,CAAC;EAED,MAAMqG,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAIH,kBAAkB,CAAC,CAAC,EAAE;MACxB/F,eAAe,CAAC,IAAI,CAAC;MAErB,IAAI;QACF;QACA,MAAMmG,cAAc,GAAG,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;QAExE;QACA,MAAME,UAAU,GAAG,IAAIC,QAAQ,CAAC,CAAC;;QAEjC;QACAD,UAAU,CAACE,MAAM,CAAC,aAAa,EAAEtG,QAAQ,CAACE,WAAW,CAAC;QACtDkG,UAAU,CAACE,MAAM,CAAC,mBAAmB,EAAEtG,QAAQ,CAACG,iBAAiB,CAAC;QAClEiG,UAAU,CAACE,MAAM,CAAC,aAAa,EAAEtG,QAAQ,CAACI,WAAW,CAAC;QACtDgG,UAAU,CAACE,MAAM,CAAC,gBAAgB,EAAEtG,QAAQ,CAACK,cAAc,CAAC;QAC5D+F,UAAU,CAACE,MAAM,CAAC,gBAAgB,EAAEtG,QAAQ,CAACM,cAAc,CAAC;QAC5D8F,UAAU,CAACE,MAAM,CAAC,iBAAiB,EAAEtG,QAAQ,CAACS,eAAe,CAAC;QAC9D2F,UAAU,CAACE,MAAM,CAAC,YAAY,EAAEtG,QAAQ,CAACU,UAAU,CAAC;QACpD0F,UAAU,CAACE,MAAM,CAAC,UAAU,EAAEtG,QAAQ,CAACW,QAAQ,CAAC;QAChDyF,UAAU,CAACE,MAAM,CAAC,OAAO,EAAEtG,QAAQ,CAACY,KAAK,CAAC;QAC1CwF,UAAU,CAACE,MAAM,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACxG,QAAQ,CAACa,IAAI,CAAC,CAAC;QACxDuF,UAAU,CAACE,MAAM,CAAC,YAAY,EAAEC,IAAI,CAACC,SAAS,CAACxG,QAAQ,CAACc,UAAU,CAAC,CAAC;QAEpEvC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE4H,UAAU,CAAC;;QAEvC;QACA,IAAIpG,QAAQ,CAACO,SAAS,EAAE;UACtB6F,UAAU,CAACE,MAAM,CAAC,cAAc,EAAEtG,QAAQ,CAACO,SAAS,CAAC;QACvD;QAEAhC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxCD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEY,QAAQ,IAAIC,eAAe,CAAC;;QAE5D;QACA,IAAIoH,OAAO;QACX,IAAIrH,QAAQ,IAAIC,eAAe,EAAE;UAC/B;UACAoH,OAAO,GAAG9I,mBAAmB,CAAC0B,eAAe,EAAE+G,UAAU,CAAC;QAC5D,CAAC,MAAM;UACL;UACAK,OAAO,GAAGhJ,eAAe,CAAC2I,UAAU,CAAC;QACvC;;QAEA;QACA,MAAM,CAACpF,QAAQ,CAAC,GAAG,MAAMiF,OAAO,CAACS,GAAG,CAAC,CAACD,OAAO,EAAET,cAAc,CAAC,CAAC;QAE/D,IAAIhF,QAAQ,CAACE,OAAO,EAAE;UACpB3C,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEwC,QAAQ,CAACG,IAAI,CAAC;UAC1D;UACAhC,QAAQ,CAAC,gBAAgB,CAAC;QAC5B,CAAC,MAAM;UAAA,IAAAwH,eAAA;UACL9G,eAAe,CAAC,KAAK,CAAC;UACtB,MAAM+G,QAAQ,GAAGxH,QAAQ,IAAIC,eAAe,GAAG,yBAAyB,GAAG,yBAAyB;UACpGkD,KAAK,CAACqE,QAAQ,GAAG,IAAI,IAAI,EAAAD,eAAA,GAAA3F,QAAQ,CAACG,IAAI,cAAAwF,eAAA,uBAAbA,eAAA,CAAerE,SAAS,KAAI,eAAe,CAAC,CAAC;QACxE;MACF,CAAC,CAAC,OAAOD,KAAK,EAAE;QACdxC,eAAe,CAAC,KAAK,CAAC;QACtBtB,OAAO,CAAC8D,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACpD,MAAMuE,QAAQ,GAAGxH,QAAQ,IAAIC,eAAe,GAAG,uBAAuB,GAAG,uBAAuB;QAChGkD,KAAK,CAACqE,QAAQ,GAAG,qBAAqB,CAAC;MACzC;IACF,CAAC,MAAM;MACLrE,KAAK,CAAC,uDAAuD,CAAC;IAChE;EACF,CAAC;EAED,MAAMsE,2BAA2B,GAAGA,CAAA,kBAClC/I,OAAA;IAAKgJ,SAAS,EAAC,oBAAoB;IAAAC,QAAA,eACjCjJ,OAAA;MAAKgJ,SAAS,EAAC,KAAK;MAAAC,QAAA,gBAClBjJ,OAAA;QAAKgJ,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BjJ,OAAA;UAAOkJ,OAAO,EAAC,aAAa;UAACF,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1EtJ,OAAA;UACEkG,IAAI,EAAC,MAAM;UACX8C,SAAS,EAAC,cAAc;UACxBO,EAAE,EAAC,aAAa;UAChB7D,IAAI,EAAC,aAAa;UAClB8D,WAAW,EAAC,oBAAoB;UAChCjE,KAAK,EAAErD,QAAQ,CAACE,WAAY;UAC5BqH,QAAQ,EAAEhE,iBAAkB;UAC5BiE,SAAS,EAAE,GAAI;UACfC,QAAQ;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFtJ,OAAA;UAAOgJ,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAwF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3H,CAAC,eAENtJ,OAAA;QAAKgJ,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BjJ,OAAA;UAAOkJ,OAAO,EAAC,mBAAmB;UAACF,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAoB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACtFtJ,OAAA;UACEgJ,SAAS,EAAC,cAAc;UACxBO,EAAE,EAAC,mBAAmB;UACtB7D,IAAI,EAAC,mBAAmB;UACxBkE,IAAI,EAAC,GAAG;UACRJ,WAAW,EAAC,0BAA0B;UACtCjE,KAAK,EAAErD,QAAQ,CAACG,iBAAkB;UAClCoH,QAAQ,EAAEhE,iBAAkB;UAC5BiE,SAAS,EAAE,IAAK;UAChBC,QAAQ;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACZtJ,OAAA;UAAOgJ,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAA+B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC,eAENtJ,OAAA;QAAKgJ,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BjJ,OAAA;UAAOkJ,OAAO,EAAC,aAAa;UAACF,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1EtJ,OAAA;UACEgJ,SAAS,EAAC,aAAa;UACvBO,EAAE,EAAC,aAAa;UAChB7D,IAAI,EAAC,aAAa;UAClBH,KAAK,EAAErD,QAAQ,CAACI,WAAY;UAC5BmH,QAAQ,EAAEhE,iBAAkB;UAC5BkE,QAAQ;UAAAV,QAAA,gBAERjJ,OAAA;YAAQuF,KAAK,EAAC,EAAE;YAAA0D,QAAA,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtCtJ,OAAA;YAAQuF,KAAK,EAAC,UAAU;YAAA0D,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1CtJ,OAAA;YAAQuF,KAAK,EAAC,cAAc;YAAA0D,QAAA,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAClDtJ,OAAA;YAAQuF,KAAK,EAAC,UAAU;YAAA0D,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1CtJ,OAAA;YAAQuF,KAAK,EAAC,YAAY;YAAA0D,QAAA,EAAC;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENtJ,OAAA;QAAKgJ,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BjJ,OAAA;UAAOkJ,OAAO,EAAC,gBAAgB;UAACF,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAChFtJ,OAAA;UACEgJ,SAAS,EAAC,aAAa;UACvBO,EAAE,EAAC,gBAAgB;UACnB7D,IAAI,EAAC,gBAAgB;UACrBH,KAAK,EAAErD,QAAQ,CAACK,cAAe;UAC/BkH,QAAQ,EAAEhE,iBAAkB;UAC5BkE,QAAQ;UAAAV,QAAA,gBAERjJ,OAAA;YAAQuF,KAAK,EAAC,EAAE;YAAA0D,QAAA,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACzCtJ,OAAA;YAAQuF,KAAK,EAAC,SAAS;YAAA0D,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxCtJ,OAAA;YAAQuF,KAAK,EAAC,SAAS;YAAA0D,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxCtJ,OAAA;YAAQuF,KAAK,EAAC,QAAQ;YAAA0D,QAAA,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtCtJ,OAAA;YAAQuF,KAAK,EAAC,QAAQ;YAAA0D,QAAA,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACtCtJ,OAAA;YAAQuF,KAAK,EAAC,SAAS;YAAA0D,QAAA,EAAC;UAAO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACxCtJ,OAAA;YAAQuF,KAAK,EAAC,UAAU;YAAA0D,QAAA,EAAC;UAAQ;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1CtJ,OAAA;YAAQuF,KAAK,EAAC,OAAO;YAAA0D,QAAA,EAAC;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENtJ,OAAA;QAAKgJ,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BjJ,OAAA;UAAOkJ,OAAO,EAAC,gBAAgB;UAACF,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAiB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAChFtJ,OAAA;UACEgJ,SAAS,EAAC,aAAa;UACvBO,EAAE,EAAC,gBAAgB;UACnB7D,IAAI,EAAC,gBAAgB;UACrBH,KAAK,EAAErD,QAAQ,CAACM,cAAe;UAC/BiH,QAAQ,EAAEhE,iBAAkB;UAC5BkE,QAAQ;UAAAV,QAAA,gBAERjJ,OAAA;YAAQuF,KAAK,EAAC,EAAE;YAAA0D,QAAA,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EACxCxJ,oBAAoB,CAAC+J,UAAU,CAAChJ,GAAG,CAAC,CAACiJ,QAAQ,EAAE/C,KAAK,kBACnD/G,OAAA;YAAoBuF,KAAK,EAAEuE,QAAS;YAAAb,QAAA,EACjCa;UAAQ,GADE/C,KAAK;YAAAoC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEV,CACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMS,qBAAqB,GAAGA,CAAA,kBAC5B/J,OAAA;IAAKgJ,SAAS,EAAC,cAAc;IAAAC,QAAA,eAC3BjJ,OAAA;MAAKgJ,SAAS,EAAC,KAAK;MAAAC,QAAA,gBAElBjJ,OAAA;QAAKgJ,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BjJ,OAAA;UAAOgJ,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAkB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxDtJ,OAAA;UAAKgJ,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBjJ,OAAA;YAAKgJ,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvB,CAAC/G,QAAQ,CAACQ,gBAAgB,gBACzB1C,OAAA;cAAKgJ,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BjJ,OAAA;gBACEkG,IAAI,EAAC,MAAM;gBACXqD,EAAE,EAAC,WAAW;gBACdP,SAAS,EAAC,QAAQ;gBAClBgB,MAAM,EAAC,SAAS;gBAChBP,QAAQ,EAAE7D;cAAsB;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACFtJ,OAAA;gBACEkJ,OAAO,EAAC,WAAW;gBACnBF,SAAS,EAAC,kFAAkF;gBAC5FiB,KAAK,EAAE;kBAAEC,KAAK,EAAE,aAAa;kBAAEC,MAAM,EAAE;gBAAU,CAAE;gBAAAlB,QAAA,gBAEnDjJ,OAAA,CAACX,IAAI;kBAAC+K,IAAI,EAAC,6BAA6B;kBAACF,KAAK,EAAC,IAAI;kBAACG,MAAM,EAAC;gBAAI;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAClEtJ,OAAA;kBAAAiJ,QAAA,EAAM;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC7BtJ,OAAA;kBAAOgJ,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAA4B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,gBAENtJ,OAAA;cAAKgJ,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCjJ,OAAA;gBACEsK,GAAG,EAAEpI,QAAQ,CAACQ,gBAAiB;gBAC/B6H,GAAG,EAAC,kBAAkB;gBACtBvB,SAAS,EAAC,mBAAmB;gBAC7BiB,KAAK,EAAE;kBAAEC,KAAK,EAAE,MAAM;kBAAEG,MAAM,EAAE;gBAAO;cAAE;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eACFtJ,OAAA;gBACEgJ,SAAS,EAAC,uFAAuF;gBACjGwB,OAAO,EAAEA,CAAA,KAAMvE,gBAAgB,CAAC,WAAW,CAAE;gBAC7CC,IAAI,EAAC,QAAQ;gBACb+D,KAAK,EAAE;kBACLC,KAAK,EAAE,MAAM;kBACbG,MAAM,EAAE,MAAM;kBACdI,GAAG,EAAE,KAAK;kBACVC,KAAK,EAAE,KAAK;kBACZC,QAAQ,EAAE,OAAO;kBACjBC,YAAY,EAAE;gBAChB,CAAE;gBAAA3B,QAAA,eAEFjJ,OAAA,CAACX,IAAI;kBAAC+K,IAAI,EAAC,0BAA0B;kBAACF,KAAK,EAAC,IAAI;kBAACG,MAAM,EAAC;gBAAI;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtJ,OAAA;QAAKgJ,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BjJ,OAAA;UAAOgJ,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAkB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACxDtJ,OAAA;UAAKgJ,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBjJ,OAAA;YAAKgJ,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBjJ,OAAA;cACEgJ,SAAS,EAAC,kBAAkB;cAC5BtD,IAAI,EAAC,iBAAiB;cACtBH,KAAK,EAAErD,QAAQ,CAACS,eAAgB;cAChC8G,QAAQ,EAAEhE,iBAAkB;cAC5BkE,QAAQ;cAAAV,QAAA,gBAERjJ,OAAA;gBAAQuF,KAAK,EAAC,EAAE;gBAAA0D,QAAA,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAChDpF,KAAK,CAACC,OAAO,CAACO,eAAe,CAAC,IAAIA,eAAe,CAAC7D,GAAG,CAAEgK,IAAI,iBAC1D7K,OAAA;gBAAkCuF,KAAK,EAAEsF,IAAI,CAACtB,EAAE,IAAIsB,IAAI,CAACC,GAAI;gBAAA7B,QAAA,EAC1D4B,IAAI,CAACE,aAAa,IAAI;cAAqB,GADjCF,IAAI,CAACtB,EAAE,IAAIsB,IAAI,CAACC,GAAG;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAExB,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,EAERpH,QAAQ,CAACS,eAAe,iBACvB3C,OAAA;cAAKgJ,SAAS,EAAC,0CAA0C;cAAAC,QAAA,eACvDjJ,OAAA;gBAAKgJ,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,gBAC9CjJ,OAAA,CAACX,IAAI;kBAAC+K,IAAI,EAAC,+BAA+B;kBAACF,KAAK,EAAC,IAAI;kBAACG,MAAM,EAAC,IAAI;kBAACrB,SAAS,EAAC;gBAAc;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC7FtJ,OAAA;kBAAAiJ,QAAA,gBACEjJ,OAAA;oBAAIgJ,SAAS,EAAC,MAAM;oBAAAC,QAAA,GAAE/G,QAAQ,CAACS,eAAe,CAACqI,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG/I,QAAQ,CAACS,eAAe,CAACuI,KAAK,CAAC,CAAC,CAAC,EAAC,cAAY;kBAAA;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5HtJ,OAAA;oBAAOgJ,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAqC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAM6B,+BAA+B,GAAGA,CAAA,kBACtCnL,OAAA;IAAKgJ,SAAS,EAAC,KAAK;IAAAC,QAAA,eAClBjJ,OAAA;MAAKgJ,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1BjJ,OAAA;QAAKgJ,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBjJ,OAAA;UAAKgJ,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBjJ,OAAA;YAAKgJ,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBjJ,OAAA;cAAOgJ,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnDtJ,OAAA;cAAKgJ,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBjJ,OAAA;gBAAKgJ,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBACtBjJ,OAAA;kBAAKgJ,SAAS,EAAC,QAAQ;kBAAAC,QAAA,eACrBjJ,OAAA;oBACEkG,IAAI,EAAC,MAAM;oBACX8C,SAAS,EAAC,sCAAsC;oBAChDQ,WAAW,EAAC,mDAAmD;oBAC/DjE,KAAK,EAAET,QAAS;oBAChB2E,QAAQ,EAAErE,oBAAqB;oBAC/BgG,SAAS,EAAEzE;kBAAsB;oBAAAwC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNtJ,OAAA;kBAAKgJ,SAAS,EAAC,OAAO;kBAAAC,QAAA,eACpBjJ,OAAA;oBACEgJ,SAAS,EAAC,6CAA6C;oBACvDwB,OAAO,EAAErE,YAAa;oBAAA8C,QAAA,EACvB;kBAED;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNtJ,OAAA;gBAAKgJ,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBjJ,OAAA;kBAAOgJ,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAC;gBAAmC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtFtJ,OAAA;kBAAOgJ,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,GAAC,qCAEpC,eAAAjJ,OAAA;oBAAAmJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,iCAEN,eAAAtJ,OAAA;oBAAAmJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,WACJ,EAAC,CAAC,GAAGpH,QAAQ,CAACa,IAAI,CAACpC,MAAM,EAAC,iBAC9B;gBAAA;kBAAAwI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNtJ,OAAA;cAAKgJ,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EACzC/G,QAAQ,CAACa,IAAI,CAAClC,GAAG,CAAC,CAACwF,GAAG,EAAEU,KAAK,kBAC5B/G,OAAA;gBAEEgJ,SAAS,EAAC,2EAA2E;gBAAAC,QAAA,GAEpF5C,GAAG,eACJrG,OAAA;kBACEkG,IAAI,EAAC,QAAQ;kBACb8C,SAAS,EAAC,wDAAwD;kBAClEwB,OAAO,EAAEA,CAAA,KAAMhE,eAAe,CAACH,GAAG,CAAE;kBACpC4D,KAAK,EAAE;oBAAEU,QAAQ,EAAE,MAAM;oBAAEN,MAAM,EAAE;kBAAO,CAAE;kBAAApB,QAAA,eAE5CjJ,OAAA,CAACX,IAAI;oBAAC+K,IAAI,EAAC,2BAA2B;oBAACF,KAAK,EAAC,IAAI;oBAACG,MAAM,EAAC;kBAAI;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CAAC;cAAA,GAXJvC,KAAK;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAYP,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtJ,OAAA;YAAKgJ,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBjJ,OAAA;cAAOgJ,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnDtJ,OAAA;cAAKgJ,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBjJ,OAAA;gBAAKgJ,SAAS,EAAC,SAAS;gBAAAC,QAAA,gBACtBjJ,OAAA;kBAAKgJ,SAAS,EAAC,QAAQ;kBAAAC,QAAA,eACrBjJ,OAAA;oBACEkG,IAAI,EAAC,MAAM;oBACX8C,SAAS,EAAC,sCAAsC;oBAChDQ,WAAW,EAAC,4EAA4E;oBACxFjE,KAAK,EAAEP,SAAU;oBACjByE,QAAQ,EAAEjE,qBAAsB;oBAChC4F,SAAS,EAAEhE;kBAAuB;oBAAA+B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNtJ,OAAA;kBAAKgJ,SAAS,EAAC,OAAO;kBAAAC,QAAA,eACpBjJ,OAAA;oBACEgJ,SAAS,EAAC,6CAA6C;oBACvDwB,OAAO,EAAE3D,aAAc;oBAAAoC,QAAA,EAEtB/D,WAAW,KAAK,IAAI,GAAG,QAAQ,GAAG;kBAAU;oBAAAiE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNtJ,OAAA;gBAAKgJ,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnBjJ,OAAA;kBAAOgJ,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAC;gBAEtC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENtJ,OAAA;cAAKgJ,SAAS,EAAC,MAAM;cAAAC,QAAA,EAClB/G,QAAQ,CAACc,UAAU,CAACnC,GAAG,CAAC,CAACiG,IAAI,EAAEC,KAAK,kBACnC/G,OAAA;gBAEEgJ,SAAS,EAAC,yDAAyD;gBAAAC,QAAA,gBAEnEjJ,OAAA,CAACX,IAAI;kBACH+K,IAAI,EAAC,wBAAwB;kBAC7BpB,SAAS,EAAC,4BAA4B;kBACtCkB,KAAK,EAAC,IAAI;kBACVG,MAAM,EAAC;gBAAI;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACFtJ,OAAA;kBAAMgJ,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAEnC;gBAAI;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC3CtJ,OAAA;kBAAKgJ,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3BjJ,OAAA;oBACEkG,IAAI,EAAC,QAAQ;oBACb8C,SAAS,EAAC,yDAAyD;oBACnEwB,OAAO,EAAEA,CAAA,KAAMxD,cAAc,CAACD,KAAK,CAAE;oBACrCkD,KAAK,EAAE;sBAAEU,QAAQ,EAAE,MAAM;sBAAEN,MAAM,EAAE;oBAAO,CAAE;oBAAApB,QAAA,eAE5CjJ,OAAA,CAACX,IAAI;sBAAC+K,IAAI,EAAC,wBAAwB;sBAACF,KAAK,EAAC,IAAI;sBAACG,MAAM,EAAC;oBAAI;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD,CAAC,eACTtJ,OAAA;oBACEkG,IAAI,EAAC,QAAQ;oBACb8C,SAAS,EAAC,wDAAwD;oBAClEwB,OAAO,EAAEA,CAAA,KAAMvD,gBAAgB,CAACF,KAAK,CAAE;oBACvCkD,KAAK,EAAE;sBAAEU,QAAQ,EAAE,MAAM;sBAAEN,MAAM,EAAE;oBAAO,CAAE;oBAAApB,QAAA,eAE5CjJ,OAAA,CAACX,IAAI;sBAAC+K,IAAI,EAAC,0BAA0B;sBAACF,KAAK,EAAC,IAAI;sBAACG,MAAM,EAAC;oBAAI;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA,GA3BDvC,KAAK;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA4BP,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAM+B,iBAAiB,GAAGA,CAAA,kBACxBrL,OAAA;IAAKgJ,SAAS,EAAC,KAAK;IAAAC,QAAA,eAClBjJ,OAAA;MAAKgJ,SAAS,EAAC,QAAQ;MAAAC,QAAA,eACrBjJ,OAAA;QAAKgJ,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBjJ,OAAA;UAAKgJ,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BjJ,OAAA;YAAIgJ,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAClBjJ,OAAA,CAACX,IAAI;cAAC+K,IAAI,EAAC,uBAAuB;cAACF,KAAK,EAAC,IAAI;cAACG,MAAM,EAAC,IAAI;cAACrB,SAAS,EAAC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,kBAE/E;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLtJ,OAAA;YAAGgJ,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAC;UAEpC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNtJ,OAAA;UAAKgJ,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAExBjJ,OAAA;YAAKgJ,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBjJ,OAAA;cAAIgJ,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC/BjJ,OAAA,CAACX,IAAI;gBAAC+K,IAAI,EAAC,wBAAwB;gBAACF,KAAK,EAAC,IAAI;gBAACG,MAAM,EAAC,IAAI;gBAACrB,SAAS,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,sBAEhF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLtJ,OAAA;cAAKgJ,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClBjJ,OAAA;gBAAKgJ,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BjJ,OAAA;kBAAOgJ,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7DtJ,OAAA;kBAAGgJ,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAE/G,QAAQ,CAACE,WAAW,IAAI;gBAAe;kBAAA+G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE,CAAC,eACNtJ,OAAA;gBAAKgJ,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BjJ,OAAA;kBAAOgJ,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAe;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAChEtJ,OAAA;kBAAGgJ,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAE/G,QAAQ,CAACM,cAAc,IAAI;gBAAe;kBAAA2G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC,eACNtJ,OAAA;gBAAKgJ,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BjJ,OAAA;kBAAOgJ,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnEtJ,OAAA;kBAAGgJ,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAE/G,QAAQ,CAACG,iBAAiB,IAAI;gBAAe;kBAAA8G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC,eACNtJ,OAAA;gBAAKgJ,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BjJ,OAAA;kBAAOgJ,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtDtJ,OAAA;kBAAGgJ,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,EAAE/G,QAAQ,CAACI,WAAW,IAAI;gBAAe;kBAAA6G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC,eACNtJ,OAAA;gBAAKgJ,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BjJ,OAAA;kBAAOgJ,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzDtJ,OAAA;kBAAGgJ,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,EAAE/G,QAAQ,CAACK,cAAc,IAAI;gBAAe;kBAAA4G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF,CAAC,eACNtJ,OAAA;gBAAKgJ,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BjJ,OAAA;kBAAOgJ,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5DtJ,OAAA;kBAAGgJ,SAAS,EAAC,MAAM;kBAAAC,QAAA,EAAE/G,QAAQ,CAACS,eAAe,GAAG,mBAAmBT,QAAQ,CAACS,eAAe,EAAE,GAAG;gBAAe;kBAAAwG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtJ,OAAA;YAAAmJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAGNtJ,OAAA;YAAKgJ,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBjJ,OAAA;cAAIgJ,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC/BjJ,OAAA,CAACX,IAAI;gBAAC+K,IAAI,EAAC,yBAAyB;gBAACF,KAAK,EAAC,IAAI;gBAACG,MAAM,EAAC,IAAI;gBAACrB,SAAS,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEjF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLtJ,OAAA;cAAKgJ,SAAS,EAAC,KAAK;cAAAC,QAAA,eAClBjJ,OAAA;gBAAKgJ,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BjJ,OAAA;kBAAOgJ,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EAChEpH,QAAQ,CAACQ,gBAAgB,gBACxB1C,OAAA;kBAAAiJ,QAAA,eACEjJ,OAAA;oBACEsK,GAAG,EAAEpI,QAAQ,CAACQ,gBAAiB;oBAC/B6H,GAAG,EAAC,kBAAkB;oBACtBvB,SAAS,EAAC,0BAA0B;oBACpCiB,KAAK,EAAE;sBAAEqB,SAAS,EAAE,OAAO;sBAAEpB,KAAK,EAAE;oBAAO;kBAAE;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,gBAENtJ,OAAA;kBAAGgJ,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAqB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CACxD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtJ,OAAA;YAAAmJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAGNtJ,OAAA;YAAKgJ,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBjJ,OAAA;cAAIgJ,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC/BjJ,OAAA,CAACX,IAAI;gBAAC+K,IAAI,EAAC,uBAAuB;gBAACF,KAAK,EAAC,IAAI;gBAACG,MAAM,EAAC,IAAI;gBAACrB,SAAS,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,0BAE/E;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLtJ,OAAA;cAAKgJ,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClBjJ,OAAA;gBAAKgJ,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BjJ,OAAA;kBAAOgJ,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EACpDpH,QAAQ,CAACa,IAAI,CAACpC,MAAM,GAAG,CAAC,gBACvBX,OAAA;kBAAKgJ,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,EACpC/G,QAAQ,CAACa,IAAI,CAAClC,GAAG,CAAC,CAACwF,GAAG,EAAEU,KAAK,kBAC5B/G,OAAA;oBAAkBgJ,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAC1D5C;kBAAG,GADKU,KAAK;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEV,CACP;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,gBAENtJ,OAAA;kBAAGgJ,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAAa;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAChD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNtJ,OAAA;gBAAKgJ,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BjJ,OAAA;kBAAOgJ,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAyB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EACzEpH,QAAQ,CAACc,UAAU,CAACrC,MAAM,GAAG,CAAC,gBAC7BX,OAAA;kBAAIgJ,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAC/B/G,QAAQ,CAACc,UAAU,CAACnC,GAAG,CAAC,CAACiG,IAAI,EAAEC,KAAK,kBACnC/G,OAAA;oBAAgBgJ,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBAC9BjJ,OAAA,CAACX,IAAI;sBAAC+K,IAAI,EAAC,oCAAoC;sBAACF,KAAK,EAAC,IAAI;sBAACG,MAAM,EAAC,IAAI;sBAACrB,SAAS,EAAC;oBAAmB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACtGxC,IAAI;kBAAA,GAFEC,KAAK;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAGV,CACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,gBAELtJ,OAAA;kBAAGgJ,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAAC;gBAA2B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAC9D;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtJ,OAAA;YAAAmJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAGNtJ,OAAA;YAAKgJ,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBjJ,OAAA;cAAIgJ,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC/BjJ,OAAA,CAACX,IAAI;gBAAC+K,IAAI,EAAC,2BAA2B;gBAACF,KAAK,EAAC,IAAI;gBAACG,MAAM,EAAC,IAAI;gBAACrB,SAAS,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,uBAEnF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLtJ,OAAA;cAAKgJ,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClBjJ,OAAA;gBAAKgJ,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BjJ,OAAA;kBAAOgJ,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAW;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC5DtJ,OAAA;kBAAGgJ,SAAS,EAAC,MAAM;kBAAAC,QAAA,eACjBjJ,OAAA;oBAAMgJ,SAAS,EAAE,SAAS9G,QAAQ,CAACU,UAAU,KAAK,MAAM,GAAG,YAAY,GAAG,YAAY,EAAG;oBAAAqG,QAAA,EACtF/G,QAAQ,CAACU,UAAU,KAAK,MAAM,GAAG,aAAa,GAAG;kBAAa;oBAAAuG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,EACLpH,QAAQ,CAACU,UAAU,KAAK,MAAM,iBAC7B5C,OAAA,CAAAE,SAAA;gBAAA+I,QAAA,gBACEjJ,OAAA;kBAAKgJ,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BjJ,OAAA;oBAAOgJ,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACzDtJ,OAAA;oBAAGgJ,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAE/G,QAAQ,CAACW;kBAAQ;oBAAAsG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACNtJ,OAAA;kBAAKgJ,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBAC5BjJ,OAAA;oBAAOgJ,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAK;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtDtJ,OAAA;oBAAGgJ,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,GAC1B/G,QAAQ,CAACW,QAAQ,KAAK,KAAK,IAAI,GAAG,EAClCX,QAAQ,CAACW,QAAQ,KAAK,KAAK,IAAI,GAAG,EAClCX,QAAQ,CAACW,QAAQ,KAAK,KAAK,IAAI,GAAG,EAClCX,QAAQ,CAACW,QAAQ,KAAK,KAAK,IAAI,GAAG,EAClCX,QAAQ,CAACW,QAAQ,KAAK,KAAK,IAAI,IAAI,EACnCX,QAAQ,CAACY,KAAK;kBAAA;oBAAAqG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACd,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA,eACN,CACH;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMiC,iBAAiB,GAAGA,CAAA,kBACxBvL,OAAA;IAAKgJ,SAAS,EAAC,KAAK;IAAAC,QAAA,eAClBjJ,OAAA;MAAKgJ,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1BjJ,OAAA;QAAKgJ,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBjJ,OAAA;UAAKgJ,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAExBjJ,OAAA;YAAKgJ,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBjJ,OAAA;cAAOgJ,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnDtJ,OAAA;cAAKgJ,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BjJ,OAAA;gBAAKgJ,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjJ,OAAA;kBACEkG,IAAI,EAAC,OAAO;kBACZ8C,SAAS,EAAC,kBAAkB;kBAC5BO,EAAE,EAAC,MAAM;kBACT7D,IAAI,EAAC,YAAY;kBACjBH,KAAK,EAAC,MAAM;kBACZiG,OAAO,EAAEtJ,QAAQ,CAACU,UAAU,KAAK,MAAO;kBACxC6G,QAAQ,EAAGpE,CAAC,IAAKlD,WAAW,CAACoE,IAAI,KAAK;oBACpC,GAAGA,IAAI;oBACP3D,UAAU,EAAEyC,CAAC,CAACC,MAAM,CAACC,KAAK;oBAC1BzC,KAAK,EAAE,EAAE;oBACTD,QAAQ,EAAE1C,MAAM,KAAK,YAAY,GAAG,KAAK,GAAIA,MAAM,KAAK,sBAAsB,IAAIA,MAAM,KAAK,0BAA0B,GAAI,KAAK,GAAG;kBACrI,CAAC,CAAC;gBAAE;kBAAAgJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACFtJ,OAAA;kBAAOgJ,SAAS,EAAC,kBAAkB;kBAACE,OAAO,EAAC,MAAM;kBAAAD,QAAA,gBAChDjJ,OAAA;oBAAAiJ,QAAA,EAAQ;kBAAW;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5BtJ,OAAA;oBAAAmJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNtJ,OAAA;oBAAOgJ,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAkC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNtJ,OAAA;gBAAKgJ,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBjJ,OAAA;kBACEkG,IAAI,EAAC,OAAO;kBACZ8C,SAAS,EAAC,kBAAkB;kBAC5BO,EAAE,EAAC,MAAM;kBACT7D,IAAI,EAAC,YAAY;kBACjBH,KAAK,EAAC,MAAM;kBACZiG,OAAO,EAAEtJ,QAAQ,CAACU,UAAU,KAAK,MAAO;kBACxC6G,QAAQ,EAAGpE,CAAC,IAAKlD,WAAW,CAACoE,IAAI,KAAK;oBACpC,GAAGA,IAAI;oBACP3D,UAAU,EAAEyC,CAAC,CAACC,MAAM,CAACC;kBACvB,CAAC,CAAC;gBAAE;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACFtJ,OAAA;kBAAOgJ,SAAS,EAAC,kBAAkB;kBAACE,OAAO,EAAC,MAAM;kBAAAD,QAAA,gBAChDjJ,OAAA;oBAAAiJ,QAAA,EAAQ;kBAAW;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5BtJ,OAAA;oBAAAmJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACNtJ,OAAA;oBAAOgJ,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAwC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAELpH,QAAQ,CAACU,UAAU,KAAK,MAAM,iBACzC5C,OAAA,CAAAE,SAAA;YAAA+I,QAAA,gBACEjJ,OAAA;cAAKgJ,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBjJ,OAAA;gBAAOgJ,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrDtJ,OAAA;gBAAKgJ,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBjJ,OAAA;kBAAOgJ,SAAS,EAAC,YAAY;kBAAAC,QAAA,GAAC,oBAAiB,EAAC9I,MAAM,EAAC,IAAC;gBAAA;kBAAAgJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAChEtJ,OAAA;kBAAAmJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNtJ,OAAA;kBAAOgJ,SAAS,EAAC,YAAY;kBAAAC,QAAA,GAAC,aACjB,EAAC9I,MAAM,KAAK,YAAY,GAAG,MAAM,GAAG,OAAO,EAAC,kBAC1C,EAACA,MAAM,KAAK,sBAAsB,GAAG,MAAM,GAAG,OAAO,EAAC,oBACpD,EAACA,MAAM,KAAK,0BAA0B,GAAG,MAAM,GAAG,OAAO;gBAAA;kBAAAgJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNtJ,OAAA;gBACEgJ,SAAS,EAAC,aAAa;gBACvBzD,KAAK,EAAErD,QAAQ,CAACW,QAAS;gBACzB4G,QAAQ,EAAGpE,CAAC,IACVlD,WAAW,CAAEoE,IAAI,KAAM;kBACrB,GAAGA,IAAI;kBACP1D,QAAQ,EAAEwC,CAAC,CAACC,MAAM,CAACC;gBACrB,CAAC,CAAC,CACH;gBAAA0D,QAAA,EAEA9I,MAAM,KAAK,YAAY,gBACtBH,OAAA;kBAAQuF,KAAK,EAAC,KAAK;kBAAA0D,QAAA,EAAC;gBAAQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,GAClCnJ,MAAM,KAAK,sBAAsB,IAAIA,MAAM,KAAK,0BAA0B,gBAC7EH,OAAA;kBAAQuF,KAAK,EAAC,KAAK;kBAAA0D,QAAA,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,gBAEpCtJ,OAAA,CAAAE,SAAA;kBAAA+I,QAAA,gBACEjJ,OAAA;oBAAQuF,KAAK,EAAC,KAAK;oBAAA0D,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACpCtJ,OAAA;oBAAQuF,KAAK,EAAC,KAAK;oBAAA0D,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACpCtJ,OAAA;oBAAQuF,KAAK,EAAC,KAAK;oBAAA0D,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACpCtJ,OAAA;oBAAQuF,KAAK,EAAC,KAAK;oBAAA0D,QAAA,EAAC;kBAAO;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA,eACpC;cACH;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC,eAETtJ,OAAA;gBAAOgJ,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAC1B9I,MAAM,KAAK,YAAY,GAAG,6CAA6C,GACtEA,MAAM,KAAK,sBAAsB,IAAIA,MAAM,KAAK,0BAA0B,GAAI,6CAA6C,GAC5H;cAA8C;gBAAAgJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAENtJ,OAAA;cAAKgJ,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBjJ,OAAA;gBAAOgJ,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAc;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpDtJ,OAAA;gBAAKgJ,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BjJ,OAAA;kBAAMgJ,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,GAC/B/G,QAAQ,CAACW,QAAQ,KAAK,KAAK,IAAI,GAAG,EAClCX,QAAQ,CAACW,QAAQ,KAAK,KAAK,IAAI,IAAI,EACnCX,QAAQ,CAACW,QAAQ,KAAK,KAAK,IAAI,GAAG,EAClCX,QAAQ,CAACW,QAAQ,KAAK,KAAK,IAAI,GAAG,EAClCX,QAAQ,CAACW,QAAQ,KAAK,KAAK,IAAI,GAAG;gBAAA;kBAAAsG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eACPtJ,OAAA;kBACEkG,IAAI,EAAC,QAAQ;kBACb8C,SAAS,EAAC,cAAc;kBACxBQ,WAAW,EAAC,oBAAoB;kBAChCjE,KAAK,EAAErD,QAAQ,CAACY,KAAM;kBACtB2G,QAAQ,EAAGpE,CAAC,IACVlD,WAAW,CAAEoE,IAAI,KAAM;oBACrB,GAAGA,IAAI;oBACPzD,KAAK,EAAEuC,CAAC,CAACC,MAAM,CAACC;kBAClB,CAAC,CAAC,CACH;kBACDoE,QAAQ;gBAAA;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,eACN,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGc;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,MAAMmC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,QAAQ7J,WAAW;MACjB,KAAK,CAAC;QACJ,OAAOmH,2BAA2B,CAAC,CAAC;MACtC,KAAK,CAAC;QACJ,OAAOgB,qBAAqB,CAAC,CAAC;MAChC,KAAK,CAAC;QACJ,OAAOoB,+BAA+B,CAAC,CAAC;MAC1C,KAAK,CAAC;QACJ,OAAOI,iBAAiB,CAAC,CAAC;MAC5B,KAAK,CAAC;QACJ,OAAOF,iBAAiB,CAAC,CAAC;MAC5B;QACE,OAAO,IAAI;IACf;EACF,CAAC;;EAED;EACA,MAAMK,wBAAwB,GAAGA,CAAA,KAAM;IACrC,MAAMC,UAAU,GAAGrK,QAAQ,IAAIC,eAAe;IAC9C,oBACEvB,OAAA;MAAKgJ,SAAS,EAAC,2FAA2F;MACrGiB,KAAK,EAAE;QACL2B,eAAe,EAAE,oBAAoB;QACrCC,MAAM,EAAE;MACV,CAAE;MAAA5C,QAAA,eACLjJ,OAAA;QAAKgJ,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrCjJ,OAAA;UAAKgJ,SAAS,EAAC,kCAAkC;UAAC8C,IAAI,EAAC,QAAQ;UAAC7B,KAAK,EAAE;YAAEC,KAAK,EAAE,MAAM;YAAEG,MAAM,EAAE;UAAO,CAAE;UAAApB,QAAA,eACvGjJ,OAAA;YAAMgJ,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eACNtJ,OAAA;UAAIgJ,SAAS,EAAC,MAAM;UAAAC,QAAA,EAAE0C,UAAU,GAAG,yBAAyB,GAAG;QAAyB;UAAAxC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC9FtJ,OAAA;UAAGgJ,SAAS,EAAC,MAAM;UAAAC,QAAA,EAAC;QAAqD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;;EAED;EACA,MAAMyC,oBAAoB,GAAGA,CAAA,kBAC3B/L,OAAA;IAAKgJ,SAAS,EAAC,2FAA2F;IACrGiB,KAAK,EAAE;MACL2B,eAAe,EAAE,oBAAoB;MACrCC,MAAM,EAAE;IACV,CAAE;IAAA5C,QAAA,eACLjJ,OAAA;MAAKgJ,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBACrCjJ,OAAA;QAAKgJ,SAAS,EAAC,kCAAkC;QAAC8C,IAAI,EAAC,QAAQ;QAAC7B,KAAK,EAAE;UAAEC,KAAK,EAAE,MAAM;UAAEG,MAAM,EAAE;QAAO,CAAE;QAAApB,QAAA,eACvGjJ,OAAA;UAAMgJ,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAU;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC,eACNtJ,OAAA;QAAIgJ,SAAS,EAAC,MAAM;QAAAC,QAAA,EAAC;MAAyB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnDtJ,OAAA;QAAGgJ,SAAS,EAAC,MAAM;QAAAC,QAAA,EAAC;MAAkD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,oBACEtJ,OAAA;IAAKgJ,SAAS,EAAC,WAAW;IAAAC,QAAA,GAEvBnH,YAAY,iBAAI9B,OAAA,CAAC0L,wBAAwB;MAAAvC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAC5CtH,eAAe,iBAAIhC,OAAA,CAAC+L,oBAAoB;MAAA5C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAG5CtJ,OAAA;MAAKgJ,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC3BjJ,OAAA;QAAKgJ,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAE9BjJ,OAAA;UAAKgJ,SAAS,EAAC;QAAiB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAEvCtJ,OAAA;UACEgJ,SAAS,EAAC,uBAAuB;UACjCiB,KAAK,EAAE;YACLC,KAAK,EAAE,GAAI,CAACtI,WAAW,GAAG,CAAC,KAAKyF,KAAK,CAAC1G,MAAM,GAAG,CAAC,CAAC,GAAI,GAAG;UAC1D;QAAE;UAAAwI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGPtJ,OAAA;UAAKgJ,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAC7B5B,KAAK,CAACxG,GAAG,CAAC,CAACmL,IAAI,EAAEjF,KAAK,kBACrB/G,OAAA;YAEEgJ,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAExBjJ,OAAA;cACEgJ,SAAS,EAAE,kBACTpH,WAAW,GAAGmF,KAAK,GAAG,CAAC,GAAG,WAAW,GACrCnF,WAAW,KAAKmF,KAAK,GAAG,CAAC,GAAG,QAAQ,GAAG,EAAE,EACxC;cAAAkC,QAAA,EAEF+C,IAAI,CAAC1E;YAAM;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACNtJ,OAAA;cAAKgJ,SAAS,EAAE,iBAAiBpH,WAAW,KAAKmF,KAAK,GAAG,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;cAAAkC,QAAA,EAC1E+C,IAAI,CAACzE;YAAK;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA,GAbD0C,IAAI,CAAC1E,MAAM;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcb,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtJ,OAAA;MAAKgJ,SAAS,EAAC,cAAc;MAAAC,QAAA,EAC1BwC,iBAAiB,CAAC;IAAC;MAAAtC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,eAGNtJ,OAAA;MAAKgJ,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC3BjJ,OAAA;QAAKgJ,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BjJ,OAAA;UACEgJ,SAAS,EAAC,cAAc;UACxBwB,OAAO,EAAExC,cAAe;UACxBiE,QAAQ,EAAErK,WAAW,KAAK,CAAC,IAAIE,YAAa;UAAAmH,QAAA,gBAE5CjJ,OAAA,CAACX,IAAI;YAAC+K,IAAI,EAAC,8BAA8B;YAACF,KAAK,EAAC,IAAI;YAACG,MAAM,EAAC;UAAI;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,YAErE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACR1H,WAAW,KAAKyF,KAAK,CAAC1G,MAAM,gBAC3BX,OAAA;UACEgJ,SAAS,EAAC,UAAU;UACpBwB,OAAO,EAAEvC,YAAa;UACtBgE,QAAQ,EAAE,CAACnE,kBAAkB,CAAC,CAAC,IAAIhG,YAAa;UAAAmH,QAAA,EAE/CnH,YAAY,gBACX9B,OAAA,CAAAE,SAAA;YAAA+I,QAAA,gBACEjJ,OAAA;cAAKgJ,SAAS,EAAC,uCAAuC;cAAC8C,IAAI,EAAC,QAAQ;cAAA7C,QAAA,eAClEjJ,OAAA;gBAAMgJ,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAC;cAAU;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,EACLhI,QAAQ,IAAIC,eAAe,GAAG,oBAAoB,GAAG,oBAAoB;UAAA,eAC1E,CAAC,gBAEHvB,OAAA,CAAAE,SAAA;YAAA+I,QAAA,gBACEjJ,OAAA,CAACX,IAAI;cAAC+K,IAAI,EAAC,6BAA6B;cAACF,KAAK,EAAC,IAAI;cAACG,MAAM,EAAC,IAAI;cAACrB,SAAS,EAAC;YAAM;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAClFhI,QAAQ,IAAIC,eAAe,GAAG,eAAe,GAAG,eAAe;UAAA,eAChE;QACH;UAAA4H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC,gBAETtJ,OAAA;UACEgJ,SAAS,EAAC,UAAU;UACpBwB,OAAO,EAAEzC,UAAW;UACpBkE,QAAQ,EAAE,CAACnE,kBAAkB,CAAC,CAAE;UAAAmB,QAAA,GAE/BrH,WAAW,KAAKyF,KAAK,CAAC1G,MAAM,GAAG,CAAC,GAAG,SAAS,GAAG,MAAM,eACtDX,OAAA,CAACX,IAAI;YAAC+K,IAAI,EAAC,+BAA+B;YAACF,KAAK,EAAC,IAAI;YAACG,MAAM,EAAC;UAAI;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEH,CAAC;AAEV;AAACrI,EAAA,CAvmCQD,YAAY;EAAA,QACF1B,WAAW,EACXE,WAAW,EACPD,SAAS;AAAA;AAAA2M,EAAA,GAHvBlL,YAAY;AAymCrB,eAAeA,YAAY;AAAC,IAAAkL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}