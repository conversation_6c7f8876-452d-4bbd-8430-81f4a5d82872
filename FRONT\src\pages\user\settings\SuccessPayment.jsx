import React, { useEffect, useState } from "react";
import { successPayment } from "../../../services/userService";
import { useNavigate, useSearchParams } from "react-router-dom";
import { Icon } from '@iconify/react';
import './SuccessPayment.css';

// Helper function to get currency symbol
const getCurrencySymbol = (currency) => {
  switch (currency?.toUpperCase()) {
    case 'INR':
      return '₹';
    case 'USD':
      return '$';
    case 'SGD':
      return 'S$';
    case 'EUR':
      return '€';
    case 'GBP':
      return '£';
    default:
      return '$'; // Default to USD symbol
  }
};

const SuccessPayment = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const course_id = searchParams.get("course_id");
  const session_id = searchParams.get("session_id");

  const [data, setData] = useState({});
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  const sendtovalidation = async () => {
    try {
      const response = await successPayment(course_id, session_id);
      console.log("Success payment response:", response);
      if (response.success) {
        setData(response.data);
      } else {
        setHasError(true);
      }
    } catch (error) {
      console.error("Error in success payment:", error);
      setHasError(true);
    } finally {
      setIsLoading(false);
    }
  };

  function redirectToCourse(){
    navigate(`/user/courses`);
  }

  useEffect(() => {
    if (course_id && session_id) {
      sendtovalidation();
    }
  }, [course_id, session_id]);

  if (isLoading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: "60vh" }}>
        <div className="text-center">
          <div className="spinner-border text-primary mb-3" role="status" />
          <p className="text-muted">Verifying your payment...</p>
        </div>
      </div>
    );
  }

  if (hasError) {
    return (
      <div className="payment-error-container">
        <div className="error-content text-center">
          <Icon icon="mdi:alert-circle" className="text-danger mb-3" width="64" height="64" />
          <h3>Oops! Something went wrong.</h3>
          <p className="text-muted mb-4">We couldn't verify your payment. Please contact support.</p>
          <button className="btn btn-primary px-4 py-2" onClick={() => navigate('/')}>
            Back to Home
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="success-payment-container">
      <div className="success-payment-card">
        {/* Success Icon */}
        <div className="success-icon-wrapper mb-4">
          <Icon icon="mdi:check-circle" className="text-success" width="64" height="64" />
        </div>

        {/* Success Message */}
        <h2 className="success-title">Payment Successful!</h2>
        <p className="success-subtitle">Your transaction has been completed successfully.</p>

        {/* Amount */}
        <div className="amount-display my-4">
          <span className="amount-value">{getCurrencySymbol(data.currency)}{data.price ?? "0"}</span>
        </div>

        {/* Transaction Details */}
        <div className="transaction-details">
          <div className="detail-row">
            <span className="detail-label">Transaction ID</span>
            <span className="detail-value">
              #{data.payment_id ? data.payment_id.toUpperCase().slice(0, 15) : "-"}
            </span>
          </div>
          <div className="detail-row">
            <span className="detail-label">Date</span>
            <span className="detail-value">
              {new Date().toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </span>
          </div>
          <div className="detail-row">
            <span className="detail-label">Status</span>
            <span className="detail-value status-success">
              <Icon icon="mdi:check-circle" className="me-1" />
              Completed
            </span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="action-buttons mt-4">
          <button 
            className="btn btn-primary btn-lg w-100 mb-3"
            onClick={redirectToCourse}
          >
            <Icon icon="mdi:play-circle" className="me-2" />
           Go Back to Course
          </button>
    
        </div>
      </div>
    </div>
  );
};

export default SuccessPayment;
