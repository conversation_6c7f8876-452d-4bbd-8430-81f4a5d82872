const crypto = require('crypto');
const { mysqlServerConnection } = require('../../../db/db');
const { generateInvoicePDF } = require('../../../tools/tools');
const { uploadStripeReceiptToS3 } = require('../../../tools/aws');

console.log('=== PayU_Fixed.js Module Loading ===');
console.log('Loading required modules...');
console.log('crypto module loaded:', !!crypto);
console.log('mysqlServerConnection loaded:', !!mysqlServerConnection);
console.log('generateInvoicePDF loaded:', !!generateInvoicePDF);
console.log('uploadStripeReceiptToS3 loaded:', !!uploadStripeReceiptToS3);

const PAY_U_MERCHANT_KEY = process.env.PAY_U_MERCHANT_KEY;
const PAY_U_MERCHANT_SALT = process.env.PAY_U_MERCHANT_SALT;
const PAYU_BASE_URL = process.env.PAYU_BASE_URL;

console.log('=== Environment Variables Loading ===');
console.log('Loading PayU environment variables...');

// Enhanced environment variable logging
console.log('=== PayU Environment Variables Check ===');
console.log('PAY_U_MERCHANT_KEY:', PAY_U_MERCHANT_KEY ? `${PAY_U_MERCHANT_KEY.substring(0, 8)}...` : 'NOT SET');
console.log('PAY_U_MERCHANT_SALT:', PAY_U_MERCHANT_SALT ? `${PAY_U_MERCHANT_SALT.substring(0, 8)}...` : 'NOT SET');
console.log('PAYU_BASE_URL:', PAYU_BASE_URL || 'NOT SET');

// Check AWS S3 environment variables
console.log('=== AWS S3 Environment Variables Check ===');
console.log('S3_AWS_BUCKET_NAME:', process.env.S3_AWS_BUCKET_NAME || 'NOT SET');
console.log('S3_AWS_REGION:', process.env.S3_AWS_REGION || 'NOT SET');
console.log('S3_AWS_ACCESS_KEY_ID:', process.env.S3_AWS_ACCESS_KEY_ID ? `${process.env.S3_AWS_ACCESS_KEY_ID.substring(0, 8)}...` : 'NOT SET');
console.log('S3_AWS_SECRET_ACCESS_KEY:', process.env.S3_AWS_SECRET_ACCESS_KEY ? 'SET' : 'NOT SET');

// Check Company information variables
console.log('=== Company Information Variables Check ===');
console.log('COMPANY_NAME:', process.env.COMPANY_NAME || 'NOT SET');
console.log('COMPANY_EMAIL:', process.env.COMPANY_EMAIL || 'NOT SET');
console.log('COMPANY_PHONE:', process.env.COMPANY_PHONE || 'NOT SET');
console.log('PAYU_BACKEND_URL:', process.env.PAYU_BACKEND_URL || 'NOT SET');
console.log('========================================');

// Generate hash for PayU
const generateHash = (data) => {
    console.log('=== Generating PayU Hash ===');
    console.log('Hash function called with data:', JSON.stringify(data, null, 2));
    console.log('Hash Input Data:', {
        key: data.key,
        txnid: data.txnid,
        amount: data.amount,
        productinfo: data.productinfo,
        firstname: data.firstname,
        email: data.email
    });
    
    console.log('Validating hash input data...');
    if (!data.key) console.log('WARNING: key is missing');
    if (!data.txnid) console.log('WARNING: txnid is missing');
    if (!data.amount) console.log('WARNING: amount is missing');
    if (!data.productinfo) console.log('WARNING: productinfo is missing');
    if (!data.firstname) console.log('WARNING: firstname is missing');
    if (!data.email) console.log('WARNING: email is missing');
    if (!PAY_U_MERCHANT_SALT) console.log('WARNING: PAY_U_MERCHANT_SALT is missing');
    
    const hashString = `${PAY_U_MERCHANT_KEY}|${data.txnid}|${data.amount}|${data.productinfo}|${data.firstname}|${data.email}|||||||||||${PAY_U_MERCHANT_SALT}`;
    console.log('Hash String (masked):', hashString.replace(PAY_U_MERCHANT_SALT, '***SALT***'));
    console.log('Hash String length:', hashString.length);
    
    console.log('Creating SHA512 hash...');
    const hash = crypto.createHash('sha512').update(hashString).digest('hex');
    console.log('Generated Hash:', hash);
    console.log('Hash length:', hash.length);
    console.log('============================');
    return hash;
};

// Generate unique transaction ID
const generateTxnId = () => {
    console.log('=== Generating Transaction ID ===');
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000);
    const txnid = 'TXN' + timestamp + random;
    console.log('Generated Transaction ID:', txnid);
    console.log('Timestamp used:', timestamp);
    console.log('Random number used:', random);
    console.log('Transaction ID length:', txnid.length);
    console.log('============================');
    return txnid;
};

const processPayUPayment = async (req, res) => {
    console.log('=== PayU Payment Processing Started ===');
    console.log('Function: processPayUPayment');
    console.log('Timestamp:', new Date().toISOString());
    console.log('Request Method:', req.method);
    console.log('Request URL:', req.url);
    console.log('Request Headers:', JSON.stringify(req.headers, null, 2));
    console.log('Request Body:', JSON.stringify(req.body, null, 2));
    console.log('Request Query:', JSON.stringify(req.query, null, 2));
    console.log('Request Params:', JSON.stringify(req.params, null, 2));
    console.log('User Info:', {
        userId: req.user?.userId,
        db_name: req.user?.db_name,
        email: req.user?.email,
        role: req.user?.role
    });
    
    try {
        console.log('=== Extracting Request Data ===');
        const { amount, currency, origin, courseName, points, course_id } = req.body;
        console.log('Extracted request data:', {
            amount: amount,
            amountType: typeof amount,
            currency: currency,
            origin: origin,
            courseName: courseName,
            points: points,
            course_id: course_id
        });
        
        console.log(currency, 'currency----------------------');
        const user = req.user;

        console.log('Extracted Data:', {
            amount,
            currency,
            origin,
            courseName,
            points,
            course_id
        });

        // Validate required fields
        console.log('=== Validating Required Fields ===');
        const requiredFields = { amount, currency, origin, courseName, course_id };
        const missingFields = Object.keys(requiredFields).filter(key => !requiredFields[key]);
        
        console.log('Required fields check:', {
            amount: !!amount,
            currency: !!currency,
            origin: !!origin,
            courseName: !!courseName,
            course_id: !!course_id
        });
        
        if (missingFields.length > 0) {
            console.log('Missing Required Fields:', missingFields);
            console.log('Validation failed - returning error response');
            return res.status(400).json({
                success: false,
                data: { error_msg: 'Missing required fields', missing: missingFields }
            });
        }
        console.log('All required fields are present');

        // Check if the course is already purchased
        console.log('=== Checking Course Purchase Status ===');
        const courseCheckQuery = `SELECT * FROM ${user.db_name}.mycourses WHERE course_id = ? AND user_id = ? LIMIT 1`;
        console.log('Course Check Query:', courseCheckQuery);
        console.log('Query Parameters:', [course_id, user.userId]);
        console.log('Database name:', user.db_name);
        console.log('User ID:', user.userId);
        console.log('Course ID:', course_id);
        
        console.log('Executing database query...');
        const [isCourseExistRows] = await mysqlServerConnection.query(courseCheckQuery, [course_id, user.userId]);
        console.log('Course Check Result:', isCourseExistRows);
        console.log('Number of rows returned:', isCourseExistRows.length);

        if (isCourseExistRows.length > 0) {
            console.log('Course already purchased by user');
            console.log('Existing course record:', isCourseExistRows[0]);
            return res.status(409).json({
                success: false,
                data: { error_msg: 'Course already purchased' }
            });
        }
        console.log('Course not previously purchased');

        // Get user profile for payment details
        console.log('=== Fetching User Profile ===');
        const userQuery = `SELECT name, email, mobile FROM ${user.db_name}.users WHERE id = ? LIMIT 1`;
        console.log('User Query:', userQuery);
        console.log('User Query Parameters:', [user.userId]);
        
        console.log('Executing user profile query...');
        const [userRows] = await mysqlServerConnection.query(userQuery, [user.userId]);
        console.log('User Profile Result:', userRows);
        console.log('Number of user rows returned:', userRows.length);

        if (userRows.length === 0) {
            console.log('User not found in database');
            console.log('User ID searched:', user.userId);
            console.log('Database searched:', user.db_name);
            return res.status(404).json({
                success: false,
                data: { error_msg: 'User not found' }
            });
        }

        const userProfile = userRows[0];
        console.log('User Profile Retrieved:', {
            name: userProfile.name,
            email: userProfile.email,
            mobile: userProfile.mobile
        });

        console.log('=== Generating Transaction ID ===');
        const txnid = generateTxnId();
        console.log('Generated transaction ID:', txnid);

        // Convert amount from cents to INR (assuming amount comes in cents)
        console.log('=== Converting Amount ===');
        console.log('Original amount:', amount);
        console.log('Original amount type:', typeof amount);
        const amtInINR = (amount / 100).toFixed(2);
        console.log('Amount Conversion:', {
            originalAmount: amount,
            convertedAmount: amtInINR,
            currency: 'INR'
        });

        // Prepare PayU payment data
        console.log('=== Preparing PayU Payment Data ===');
        
        // Use backend URL for PayU callbacks
        const backendUrl = process.env.PAYU_BACKEND_URL || `${req.protocol}://${req.get('host')}`;
        console.log('Backend URL for callbacks:', backendUrl);
        console.log('Request protocol:', req.protocol);
        console.log('Request host:', req.get('host'));
        
        const paymentData = {
            key: PAY_U_MERCHANT_KEY,
            txnid: txnid,
            amount: amtInINR,
            productinfo: courseName,
            firstname: userProfile.name || 'User',
            email: userProfile.email,
            phone: userProfile.mobile || '**********',
            surl: `${backendUrl}/org/payu/success?course_id=${course_id}&txnid=${txnid}`,
            furl: `${backendUrl}/org/payu/failure?course_id=${course_id}&txnid=${txnid}`,
            service_provider: 'payu_paisa'
        };

        console.log('Payment Data Prepared:', {
            ...paymentData,
            key: paymentData.key ? `${paymentData.key.substring(0, 8)}...` : 'NOT SET'
        });

        // Generate hash
        console.log('=== Generating Payment Hash ===');
        console.log('Calling generateHash function...');
        paymentData.hash = generateHash(paymentData);
        console.log('Hash generated successfully');

        // Insert payment record into the database
        console.log('=== Inserting Payment Record ===');
        const insertQuery = `INSERT INTO ${user.db_name}.payments (user_id, amount, currency, payment_method, payment_uid, course_name, discount_point, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)`;
        const insertParams = [user.userId, amtInINR, 'INR', 'payu', txnid, courseName, points || 0, 'pending'];
        
        console.log('Payment Insert Query:', insertQuery);
        console.log('Payment Insert Parameters:', insertParams);
        console.log('Database name:', user.db_name);
        
        console.log('Executing payment insert query...');
        await mysqlServerConnection.query(insertQuery, insertParams);
        console.log('Payment record inserted successfully');

        console.log('=== PayU Payment Processing Completed Successfully ===');
        console.log('Final Payment Data:', {
            ...paymentData,
            key: paymentData.key ? `${paymentData.key.substring(0, 8)}...` : 'NOT SET',
            hash: paymentData.hash ? `${paymentData.hash.substring(0, 16)}...` : 'NOT SET'
        });

        // Return payment data for frontend to submit form
        console.log('Sending success response to client...');
        return res.status(200).json({
            success: true,
            paymentData: paymentData,
            payuUrl: PAYU_BASE_URL
        });

    } catch (error) {
        console.log('=== PayU Payment Processing Error ===');
        console.error('Error occurred in processPayUPayment function');
        console.error('Error Details:', {
            message: error.message,
            stack: error.stack,
            code: error.code,
            errno: error.errno,
            sqlMessage: error.sqlMessage,
            sqlState: error.sqlState
        });
        console.log('Request Body that caused error:', JSON.stringify(req.body, null, 2));
        console.log('User Info at error:', {
            userId: req.user?.userId,
            db_name: req.user?.db_name
        });
        console.log('Error timestamp:', new Date().toISOString());
        
        return res.status(500).json({
            success: false,
            error_msg: "PayU payment processing failed",
            details: error.message,
        });
    }
};

const successPayUPayment = async (req, res) => {
    console.log('=== PayU Success Callback Started ===');
    console.log('Function: successPayUPayment');
    console.log('Timestamp:', new Date().toISOString());
    console.log('Request Method:', req.method);
    console.log('Request URL:', req.url);
    console.log('Query Parameters:', JSON.stringify(req.query, null, 2));
    console.log('Request Headers:', JSON.stringify(req.headers, null, 2));
    console.log('User Info:', {
        userId: req.user?.userId,
        db_name: req.user?.db_name,
        email: req.user?.email
    });
    
    try {
        console.log('=== Extracting Success Callback Data ===');
        const db_name = req.user.db_name;
        const user_id = req.user.userId;
        const course_id = req.query.course_id;
        const txnid = req.query.txnid;
        console.log('Extracted Success Validation Data:', {
            db_name,
            user_id,
            course_id,
            txnid
        });

        // Validate required parameters
        console.log('=== Validating Required Parameters ===');
        console.log('Checking required parameters...');
        if (!course_id || !txnid) {
            console.log('Missing required parameters');
            console.log('course_id present:', !!course_id);
            console.log('txnid present:', !!txnid);
            return res.status(400).json({
                success: false,
                data: { error_msg: 'Missing course_id or transaction ID' }
            });
        }
        console.log('Required parameters validated - proceeding with payment verification');

        // Get payment record
        console.log('=== Fetching Payment Record ===');
        const paymentQuery = `SELECT * FROM ${db_name}.payments WHERE user_id = ? AND payment_uid = ?`;
        console.log('Payment Query:', paymentQuery);
        console.log('Payment Query Parameters:', [user_id, txnid]);
        console.log('Database name:', db_name);
        console.log('User ID:', user_id);
        console.log('Transaction ID:', txnid);
        
        console.log('Executing payment query...');
        const [payment] = await mysqlServerConnection.query(paymentQuery, [user_id, txnid]);
        console.log('Payment Record Result:', payment);
        console.log('Number of payment rows returned:', payment.length);

        if (payment.length === 0) {
            console.log('Payment record not found');
            console.log('Searched for user_id:', user_id);
            console.log('Searched for payment_uid:', txnid);
            return res.status(404).json({
                success: false,
                data: { error_msg: 'Payment record not found' }
            });
        }
        console.log('Payment record found:', payment[0]);

        // Get course details
        console.log('=== Fetching Course Details ===');
        const courseQuery = `SELECT * FROM ${db_name}.courses WHERE id = ? LIMIT 1`;
        console.log('Course Query:', courseQuery);
        console.log('Course Query Parameters:', [course_id]);
        
        console.log('Executing course query...');
        const [courseRows] = await mysqlServerConnection.query(courseQuery, [course_id]);
        console.log('Course Details Result:', courseRows);
        console.log('Number of course rows returned:', courseRows.length);

        if (courseRows.length === 0) {
            console.log('Course not found');
            console.log('Course ID searched:', course_id);
            return res.status(404).json({
                success: false,
                data: { error_msg: 'Course not found' }
            });
        }
        console.log('Course found:', courseRows[0]);

        const course = courseRows[0];

        // Get user profile for PDF generation
        console.log('=== Fetching User Profile for PDF ===');
        const userQuery = `SELECT name, email, mobile, mobileno_code FROM ${db_name}.users WHERE id = ? LIMIT 1`;
        console.log('User Profile Query:', userQuery);
        console.log('User Profile Query Parameters:', [user_id]);
        
        console.log('Executing user profile query...');
        const [userRows] = await mysqlServerConnection.query(userQuery, [user_id]);
        console.log('User Profile Result:', userRows);
        console.log('Number of user rows returned:', userRows.length);
        
        if (userRows.length === 0) {
            console.log('User profile not found for PDF generation');
            console.log('User ID searched:', user_id);
            return res.status(404).json({
                success: false,
                data: { error_msg: 'User profile not found' }
            });
        }
        
        const userProfile = userRows[0];
        console.log('User Profile Retrieved for PDF:', {
            name: userProfile.name,
            email: userProfile.email,
            mobile: userProfile.mobile,
            mobileno_code: userProfile.mobileno_code
        });

        // Check if course already enrolled to prevent duplicates
        console.log('=== Checking Existing Enrollment ===');
        const enrollmentCheckQuery = `SELECT * FROM ${db_name}.mycourses WHERE user_id = ? AND course_id = ? LIMIT 1`;
        console.log('Enrollment Check Query:', enrollmentCheckQuery);
        console.log('Enrollment Check Parameters:', [user_id, course_id]);
        
        console.log('Executing enrollment check query...');
        const [existingEnrollment] = await mysqlServerConnection.query(enrollmentCheckQuery, [user_id, course_id]);
        console.log('Existing Enrollment Result:', existingEnrollment);
        console.log('Number of existing enrollment rows:', existingEnrollment.length);
        
        if (existingEnrollment.length === 0) {
            // Add course to user's courses (using actual mycourses table columns)
            console.log('=== Adding Course to User Courses ===');
            const mycoursesQuery = `INSERT INTO ${db_name}.mycourses (user_id, course_id) VALUES (?, ?)`;
            const mycoursesParams = [user_id, course_id];
            
            console.log('MyCourses Insert Query:', mycoursesQuery);
            console.log('MyCourses Insert Parameters:', mycoursesParams);
            
            console.log('Executing mycourses insert query...');
            await mysqlServerConnection.query(mycoursesQuery, mycoursesParams);
            console.log('Course added to user courses successfully');
        } else {
            console.log('Course already enrolled, skipping enrollment');
            console.log('Existing enrollment record:', existingEnrollment[0]);
        }

        // Generate and upload invoice PDF
        console.log('=== Generating Invoice PDF ===');
        let pdfPath = null;
        try {
            console.log('Preparing invoice data for PDF generation...');
            const invoiceData = {
                transactionId: txnid,
                amount: payment[0].amount,
                currency: payment[0].currency || 'INR',
                paymentMethod: 'PayU',
                courseName: course.course_name,
                courseDescription: course.course_description || 'Online Course',
                coursePrice: course.course_price,
                userName: userProfile.name,
                userEmail: userProfile.email,
                userPhone: `${userProfile.mobileno_code || ''}${userProfile.mobile || ''}`,
                paymentDate: new Date().toISOString(),
                discountPoints: payment[0].discount_point || 0
            };

            console.log('Invoice Data for PDF generation:', JSON.stringify(invoiceData, null, 2));

            // Validate invoice data before PDF generation
            console.log('=== Validating Invoice Data ===');
            const missingFields = [];
            if (!invoiceData.transactionId) missingFields.push('transactionId');
            if (!invoiceData.amount) missingFields.push('amount');
            if (!invoiceData.courseName) missingFields.push('courseName');
            if (!invoiceData.userName) missingFields.push('userName');
            if (!invoiceData.userEmail) missingFields.push('userEmail');

            console.log('Missing fields check:', missingFields);

            if (missingFields.length > 0) {
                throw new Error(`Missing required invoice data for PDF generation: ${missingFields.join(', ')}`);
            }

            // Validate data types and values
            console.log('=== Validating Data Types ===');
            console.log('Amount type:', typeof invoiceData.amount);
            console.log('Amount value:', invoiceData.amount);
            
            if (typeof invoiceData.amount !== 'number' && typeof invoiceData.amount !== 'string') {
                throw new Error('Invoice amount must be a number or string');
            }

            if (parseFloat(invoiceData.amount) <= 0) {
                throw new Error('Invoice amount must be greater than 0');
            }

            console.log('All validations passed, calling generateInvoicePDF function...');
            const pdfBuffer = await generateInvoicePDF(invoiceData);
            console.log('PDF generation completed');

            if (!pdfBuffer || pdfBuffer.length === 0) {
                throw new Error('PDF generation returned empty buffer');
            }

            console.log('PDF generated successfully, buffer size:', pdfBuffer.length, 'bytes');
            console.log('PDF buffer first 100 bytes:', pdfBuffer.slice(0, 100).toString('hex'));

            // Upload PDF to AWS S3
            console.log('=== Uploading PDF to AWS S3 ===');
            console.log('Calling uploadStripeReceiptToS3 function...');
            const uploadResult = await uploadStripeReceiptToS3(pdfBuffer, txnid);
            console.log('S3 upload result:', uploadResult);
            pdfPath = uploadResult.path;
            console.log('PDF uploaded to S3 successfully:', pdfPath);

            // Update payment record with PDF URL
            console.log('=== Updating Payment with PDF URL ===');
            const updateReceiptQuery = `UPDATE ${db_name}.payments SET receipt_url = ? WHERE user_id = ? AND payment_uid = ?`;
            console.log('Update Receipt Query:', updateReceiptQuery);
            console.log('Update Receipt Parameters:', [pdfPath, user_id, txnid]);
            
            console.log('Executing receipt update query...');
            await mysqlServerConnection.query(updateReceiptQuery, [pdfPath, user_id, txnid]);
            console.log('Payment record updated with PDF URL successfully');

        } catch (pdfError) {
            console.error('=== PDF Generation/Upload Error ===');
            console.error('PDF error occurred during processing');
            console.error('Error message:', pdfError.message);
            console.error('Error stack:', pdfError.stack);
            console.error('Error name:', pdfError.name);
            console.error('Error code:', pdfError.code);
            console.error('Invoice data that caused error:', JSON.stringify(invoiceData || {}, null, 2));
            console.error('Payment data:', JSON.stringify(payment[0] || {}, null, 2));
            console.error('Course data:', JSON.stringify(course || {}, null, 2));
            console.error('User profile data:', JSON.stringify(userProfile || {}, null, 2));

            // Try to update payment record with error message
            try {
                console.log('Attempting to update payment record with PDF error message...');
                const errorMessage = `PDF generation failed: ${pdfError.message}`;
                const updateErrorQuery = `UPDATE ${db_name}.payments SET receipt_url = ? WHERE user_id = ? AND payment_uid = ?`;
                console.log('Error Update Query:', updateErrorQuery);
                console.log('Error Update Parameters:', [errorMessage, user_id, txnid]);
                
                await mysqlServerConnection.query(updateErrorQuery, [errorMessage, user_id, txnid]);
                console.log('Payment record updated with PDF error message');
            } catch (updateError) {
                console.error('Failed to update payment record with error message:', updateError);
                console.error('Update error details:', {
                    message: updateError.message,
                    stack: updateError.stack
                });
            }

            console.log('Continuing with payment process despite PDF error');
            // Continue with payment process even if PDF generation fails
        }

        // Update payment status to completed
        console.log('=== Updating Payment Status ===');
        const updatePaymentQuery = `UPDATE ${db_name}.payments SET status = 'completed' WHERE user_id = ? AND payment_uid = ?`;
        const updatePaymentParams = [user_id, txnid];

        console.log('Payment Update Query:', updatePaymentQuery);
        console.log('Payment Update Parameters:', updatePaymentParams);

        console.log('Executing payment status update query...');
        await mysqlServerConnection.query(updatePaymentQuery, updatePaymentParams);
        console.log('Payment status updated to completed');

        console.log('=== PayU Success Validation Completed ===');
        console.log('Final Success Response:', {
            payment_id: txnid,
            price: course.course_price,
            currency: course.currency,
            transaction_id: txnid,
            course_name: course.course_name,
            receipt_pdf_path: pdfPath
        });

        console.log('Sending success response to client...');
        return res.status(200).json({
            success: true,
            data: {
                payment_id: txnid,
                price: course.course_price,
                currency: course.currency,
                transaction_id: txnid,
                course_name: course.course_name,
                receipt_pdf_path: pdfPath,
                message: 'Payment verified, course enrolled, and invoice generated successfully!'
            }
        });

    } catch (error) {
        console.log('=== PayU Success Callback Error ===');
        console.error('Error occurred in successPayUPayment function');
        console.error('Error Details:', {
            message: error.message,
            stack: error.stack,
            code: error.code,
            errno: error.errno,
            sqlMessage: error.sqlMessage,
            sqlState: error.sqlState,
            name: error.name
        });
        console.log('Query Parameters that caused error:', JSON.stringify(req.query, null, 2));
        console.log('User Info at error:', {
            userId: req.user?.userId,
            db_name: req.user?.db_name
        });
        console.log('Error timestamp:', new Date().toISOString());
        
        return res.status(500).json({
            success: false,
            error_msg: "PayU payment verification failed",
            details: error.message,
        });
    }
};

const cancelPayUPayment = async (req, res) => {
    console.log('=== PayU Cancel Callback Started ===');
    console.log('Function: cancelPayUPayment');
    console.log('Timestamp:', new Date().toISOString());
    console.log('Request Method:', req.method);
    console.log('Request URL:', req.url);
    console.log('Query Parameters:', JSON.stringify(req.query, null, 2));
    console.log('Request Headers:', JSON.stringify(req.headers, null, 2));
    console.log('User Info:', {
        userId: req.user?.userId,
        db_name: req.user?.db_name,
        email: req.user?.email
    });
    
    try {
        console.log('=== Extracting Cancel Callback Data ===');
        const db_name = req.user.db_name;
        const user_id = req.user.userId;
        const txnid = req.query.txnid;

        console.log('Extracted Cancel Data:', {
            db_name,
            user_id,
            txnid
        });

        // Update payment status to failed (since 'cancelled' is not in ENUM)
        console.log('=== Updating Payment Status to Failed ===');
        const cancelQuery = `UPDATE ${db_name}.payments SET status = 'failed' WHERE user_id = ? AND payment_uid = ?`;
        console.log('Cancel Update Query:', cancelQuery);
        console.log('Cancel Update Parameters:', [user_id, txnid]);
        console.log('Database name:', db_name);
        console.log('User ID:', user_id);
        console.log('Transaction ID:', txnid);
        
        console.log('Executing cancel update query...');
        await mysqlServerConnection.query(cancelQuery, [user_id, txnid]);
        console.log('Payment status updated to failed successfully');

        console.log('=== PayU Cancel Callback Completed ===');
        console.log('Final Cancel Response:', {
            transaction_id: txnid,
            message: 'Payment cancelled'
        });

        console.log('Sending cancel response to client...');
        return res.status(200).json({
            success: true,
            data: {
                transaction_id: txnid,
                message: 'Payment cancelled'
            }
        });

    } catch (error) {
        console.log('=== PayU Cancel Callback Error ===');
        console.error('Error occurred in cancelPayUPayment function');
        console.error('Error Details:', {
            message: error.message,
            stack: error.stack,
            code: error.code,
            errno: error.errno,
            sqlMessage: error.sqlMessage,
            sqlState: error.sqlState,
            name: error.name
        });
        console.log('Query Parameters that caused error:', JSON.stringify(req.query, null, 2));
        console.log('User Info at error:', {
            userId: req.user?.userId,
            db_name: req.user?.db_name
        });
        console.log('Error timestamp:', new Date().toISOString());
        
        return res.status(500).json({
            success: false,
            error_msg: "Payment cancellation processing failed",
            details: error.message,
        });
    }
};

console.log('=== PayU_Fixed.js Module Export ===');
console.log('Exporting functions: processPayUPayment, successPayUPayment, cancelPayUPayment');

module.exports = {
    processPayUPayment,
    successPayUPayment,
    cancelPayUPayment
};

console.log('=== PayU_Fixed.js Module Loaded Successfully ===');
