import { BrowserRouter, Routes, Route, Navigate, useNavigate } from 'react-router-dom';
import { useEffect, useRef } from 'react';

import AuthRoutes from './routes/AuthRoutes.jsx';
import UserRoutes from './routes/UserRoutes.jsx';
import AdminRoutes from './routes/AdminRoutes.jsx';
import PublicCourseDetails from './pages/public/PublicCourseDetails.jsx';
import { NotificationProvider } from './context/NotificationContext';
import { PermissionsProvider } from './context/PermissionsContext';

import 'bootstrap/dist/css/bootstrap.min.css';
import 'bootstrap/dist/js/bootstrap.bundle.min.js';
import '../src/assets/styles/custom.css';
import './App.css';

function AppRouter() {
  const token = localStorage.getItem('token');
  const role = localStorage.getItem('role');
  const navigate = useNavigate();
  const initialLoadRef = useRef(true);
  
  useEffect(() => {
    // Only redirect on initial load
    if (initialLoadRef.current && token && window.location.pathname === '/') {
      initialLoadRef.current = false;
      if (role === 'trainee') {
        navigate('/user/dashboard');
      } else {
        // For any other role (admin, trainer, etc.)
        navigate('/admin/dashboard');
      }
    }
  }, [token, role, navigate]);

  return (
    <>
      {/* Public routes - accessible without authentication */}
      <Routes>
        <Route path="/public/courseDetails/:encodedId" element={<PublicCourseDetails />} />
      </Routes>
      
      {/* Protected routes based on authentication */}
      {!token && <AuthRoutes />}
      {token && role === 'trainee' && <UserRoutes />}
      {token && role !== 'trainee' && <AdminRoutes />}
    </>
  );
}

function App() {
  return (
    <PermissionsProvider>
    <NotificationProvider>
      <BrowserRouter>
        <AppRouter />
      </BrowserRouter>
    </NotificationProvider>
    </PermissionsProvider>
  );
}

export default App;
