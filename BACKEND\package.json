{"name": "lms_sample", "version": "1.0.0", "description": "", "main": "src/app/server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon src/app/server.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-dynamodb": "^3.744.0", "@aws-sdk/client-s3": "^3.744.0", "@aws-sdk/client-ses": "^3.744.0", "aws-sdk": "^2.1692.0", "axios": "^1.10.0", "bcrypt": "^5.1.1", "body-parser": "^1.20.2", "canvas": "^3.1.0", "cors": "^2.8.5", "cron": "^3.3.2", "date-fns": "^3.6.0", "docxtemplater": "^3.60.2", "dotenv": "^16.4.5", "express": "^4.19.2", "express-validator": "^7.1.0", "firebase": "^10.12.3", "firebase-admin": "^12.2.0", "get-video-duration": "^4.1.0", "google-auth-library": "^9.13.0", "jimp": "^1.6.0", "jsonwebtoken": "^9.0.2", "libreoffice-convert": "^1.6.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.9.7", "nodemailer": "^6.9.14", "nodemon": "^3.1.0", "pdf-lib": "^1.17.1", "pdf-poppler": "^0.2.1", "pdfkit": "^0.17.1", "pizzip": "^3.1.8", "sequelize": "^6.37.3", "socket.io": "^4.7.5", "stripe": "^17.6.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^10.0.0", "web-push": "^3.6.7", "xlsx": "^0.18.5"}}