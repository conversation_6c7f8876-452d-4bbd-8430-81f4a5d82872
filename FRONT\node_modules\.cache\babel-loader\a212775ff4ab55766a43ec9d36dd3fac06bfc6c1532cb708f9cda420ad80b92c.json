{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NEW_LMS_FIXING\\\\FRONT\\\\src\\\\components\\\\user\\\\Breadcrumbs.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\nimport { Icon } from '@iconify/react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Breadcrumbs() {\n  _s();\n  const location = useLocation();\n  const navigate = useNavigate();\n\n  // Filter out empty strings and 'user'\n  const pathnames = location.pathname.split('/').filter(x => x !== '' && x !== 'user');\n  const handleBack = () => {\n    // Special handling for success payment pages - redirect to courses\n    if (pathnames.includes('successPayment')) {\n      navigate('/user/courses');\n    } else {\n      navigate(-1);\n    }\n  };\n\n  // Create a mapping of path segments to readable names and icons\n  const pathMap = {\n    'dashboard': {\n      name: 'Dashboard',\n      icon: 'fluent:home-24-regular'\n    },\n    'profile': {\n      name: 'Profile',\n      icon: 'fluent:person-24-regular'\n    },\n    'courses': {\n      name: 'Courses',\n      icon: 'fluent:book-24-regular'\n    },\n    'courseDetails': {\n      name: 'Course Details',\n      icon: 'fluent:book-information-24-regular'\n    },\n    'WatchCourse': {\n      name: 'Course Content',\n      icon: 'fluent:book-open-24-regular'\n    },\n    'orderDetails': {\n      name: 'Order Details',\n      icon: 'fluent:receipt-24-regular'\n    },\n    'orderDetailsWithPayu': {\n      name: 'Order Details',\n      icon: 'fluent:receipt-24-regular'\n    },\n    'successPayment': {\n      name: 'Payment Successful',\n      icon: 'fluent:checkmark-circle-24-regular'\n    },\n    'AllClassroom': {\n      name: 'Classrooms',\n      icon: 'fluent:class-24-regular'\n    },\n    'certificates': {\n      name: 'Certificates',\n      icon: 'fluent:certificate-24-regular'\n    },\n    'notifications': {\n      name: 'Notifications',\n      icon: 'fluent:alert-24-regular'\n    },\n    'notificationDetails': {\n      name: 'Notification Details',\n      icon: 'fluent:alert-urgent-24-regular'\n    },\n    'settings': {\n      name: 'Settings',\n      icon: 'fluent:settings-24-regular'\n    },\n    'help': {\n      name: 'Help',\n      icon: 'fluent:question-circle-24-regular'\n    },\n    'faq': {\n      name: 'FAQ',\n      icon: 'fluent:book-question-mark-24-regular'\n    },\n    'ticket': {\n      name: 'Tickets',\n      icon: 'fluent:ticket-24-regular'\n    },\n    'classroomDetails': {\n      name: 'Classroom Dashboard',\n      icon: 'fluent:classroom-24-regular'\n    },\n    'assignmentsQuestions': {\n      name: 'Assignment',\n      icon: 'fluent:task-list-24-regular'\n    }\n  };\n  const getPathInfo = path => {\n    // Handle encoded IDs\n    if (path.includes('%') || path.includes('eyJ')) {\n      const pathType = pathnames[pathnames.indexOf(path) - 1];\n      switch (pathType) {\n        case 'courses':\n        case 'courseDetails':\n          return {\n            name: 'Course Details',\n            icon: 'fluent:book-information-24-regular'\n          };\n        case 'WatchCourse':\n          return {\n            name: 'Course Content',\n            icon: 'fluent:book-open-24-regular'\n          };\n        case 'AllClassroom':\n        case 'classroomDetails':\n          return {\n            name: 'Classroom Dashboard',\n            icon: 'fluent:classroom-24-regular'\n          };\n        case 'assignmentsQuestions':\n          return {\n            name: 'Assignment',\n            icon: 'fluent:task-list-24-regular'\n          };\n        case 'notificationDetails':\n          return {\n            name: 'Notification Details',\n            icon: 'fluent:alert-urgent-24-regular'\n          };\n        default:\n          return {\n            name: path,\n            icon: 'fluent:document-24-regular'\n          };\n      }\n    }\n    return pathMap[path] || {\n      name: path.charAt(0).toUpperCase() + path.slice(1),\n      icon: 'fluent:document-24-regular'\n    };\n  };\n\n  // Get the current page title\n  const getPageTitle = () => {\n    if (pathnames.length === 0) return 'Dashboard';\n    const lastSegment = pathnames[pathnames.length - 1];\n    const secondLastSegment = pathnames[pathnames.length - 2];\n\n    // Get classroom ID from query params if it exists\n    const params = new URLSearchParams(location.search);\n    const classroomId = params.get('classroomid');\n\n    // Special cases for different paths\n    if (pathnames.includes('assignmentsQuestions')) {\n      return 'Assignment';\n    } else if (secondLastSegment === 'AllClassroom') {\n      return 'Classroom Dashboard';\n    } else if (secondLastSegment === 'WatchCourse') {\n      return 'Course Content';\n    } else if (secondLastSegment === 'courses' && (lastSegment === 'courseDetails' || lastSegment.includes('%') || lastSegment.includes('eyJ'))) {\n      return 'Course Details';\n    } else if (lastSegment === 'notificationDetails' || secondLastSegment === 'notifications' && (lastSegment.includes('%') || lastSegment.includes('eyJ'))) {\n      return 'Notification Details';\n    } else if (secondLastSegment === 'help') {\n      if (lastSegment === 'faq') return 'FAQ';\n      if (lastSegment === 'ticket') return 'Tickets';\n    } else if (lastSegment === 'orderDetails') {\n      return 'Order Details';\n    } else if (lastSegment === 'orderDetailsWithPayu') {\n      return 'Order Details';\n    } else if (lastSegment === 'successPayment') {\n      return 'Payment Successful';\n    }\n    return getPathInfo(lastSegment).name;\n  };\n\n  // Get breadcrumb items based on current path\n  const getBreadcrumbItems = () => {\n    const items = [{\n      name: 'Dashboard',\n      icon: 'fluent:home-24-regular',\n      path: '/user/dashboard'\n    }];\n\n    // Get classroom ID from query params if it exists\n    const params = new URLSearchParams(location.search);\n    const classroomId = params.get('classroomid');\n\n    // Special handling for assignments questions with classroom context\n    if (pathnames.includes('assignmentsQuestions') && classroomId) {\n      return [...items, {\n        name: 'Classrooms',\n        icon: 'fluent:class-24-regular',\n        path: '/user/AllClassroom'\n      }, {\n        name: 'Classroom Dashboard',\n        icon: 'fluent:classroom-24-regular',\n        path: `/user/AllClassroom/classroomDetails/${classroomId}`\n      }, {\n        name: 'Assignment',\n        icon: 'fluent:task-list-24-regular',\n        path: location.pathname + location.search\n      }];\n    }\n\n    // Special handling for classroom details\n    if (pathnames.includes('classroomDetails') || pathnames.includes('AllClassroom') && (pathnames[pathnames.length - 1].includes('%') || pathnames[pathnames.length - 1].includes('eyJ'))) {\n      return [...items, {\n        name: 'Classrooms',\n        icon: 'fluent:class-24-regular',\n        path: '/user/AllClassroom'\n      }, {\n        name: 'Classroom Dashboard',\n        icon: 'fluent:classroom-24-regular',\n        path: location.pathname\n      }];\n    }\n\n    // Special handling for success payment\n    if (pathnames.includes('successPayment')) {\n      return [...items, {\n        name: 'Courses',\n        icon: 'fluent:book-24-regular',\n        path: '/user/courses'\n      }, {\n        name: 'Payment Successful',\n        icon: 'fluent:checkmark-circle-24-regular',\n        path: location.pathname + location.search\n      }];\n    }\n\n    // Special handling for order details\n    if (pathnames.includes('orderDetails')) {\n      return [...items, {\n        name: 'Courses',\n        icon: 'fluent:book-24-regular',\n        path: '/user/courses'\n      }, {\n        name: 'Order Details',\n        icon: 'fluent:receipt-24-regular',\n        path: location.pathname\n      }];\n    }\n\n    // Special handling for order details with PayU\n    if (pathnames.includes('orderDetailsWithPayu')) {\n      return [...items, {\n        name: 'Courses',\n        icon: 'fluent:book-24-regular',\n        path: '/user/courses'\n      }, {\n        name: 'Order Details',\n        icon: 'fluent:receipt-24-regular',\n        path: location.pathname\n      }];\n    }\n\n    // Special handling for help pages\n    if (pathnames.includes('help')) {\n      const helpItems = [{\n        name: 'Help',\n        icon: 'fluent:question-circle-24-regular',\n        path: '/user/help'\n      }];\n\n      // Add FAQ or Ticket based on the path\n      if (pathnames.includes('faq')) {\n        helpItems.push({\n          name: 'FAQ',\n          icon: 'fluent:book-question-mark-24-regular',\n          path: location.pathname\n        });\n      } else if (pathnames.includes('ticket')) {\n        helpItems.push({\n          name: 'Tickets',\n          icon: 'fluent:ticket-24-regular',\n          path: location.pathname\n        });\n      }\n      return [...items, ...helpItems];\n    }\n\n    // Special handling for notification details\n    if (pathnames.includes('notificationDetails') || pathnames.includes('notifications') && pathnames[pathnames.length - 1].includes('%')) {\n      return [...items, {\n        name: 'Notifications',\n        icon: 'fluent:alert-24-regular',\n        path: '/user/notifications'\n      }, {\n        name: 'Notification Details',\n        icon: 'fluent:alert-urgent-24-regular',\n        path: location.pathname\n      }];\n    }\n\n    // Special handling for WatchCourse\n    if (pathnames.includes('WatchCourse')) {\n      return [...items, {\n        name: 'Courses',\n        icon: 'fluent:book-24-regular',\n        path: '/user/courses'\n      }, {\n        name: 'Course Content',\n        icon: 'fluent:book-open-24-regular',\n        path: location.pathname\n      }];\n    }\n\n    // Special handling for course details\n    if (pathnames.includes('courseDetails') || pathnames.includes('courses') && (pathnames[pathnames.length - 1].includes('%') || pathnames[pathnames.length - 1].includes('eyJ'))) {\n      return [...items, {\n        name: 'Courses',\n        icon: 'fluent:book-24-regular',\n        path: '/user/courses'\n      }, {\n        name: 'Course Details',\n        icon: 'fluent:book-information-24-regular',\n        path: location.pathname\n      }];\n    }\n\n    // Add current path item\n    if (pathnames.length > 0) {\n      const currentPath = pathnames[pathnames.length - 1];\n      const pathInfo = getPathInfo(currentPath);\n      items.push({\n        name: pathInfo.name,\n        icon: pathInfo.icon,\n        path: location.pathname\n      });\n    }\n    return items;\n  };\n  const breadcrumbItems = getBreadcrumbItems();\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    \"aria-label\": \"breadcrumb\",\n    className: \"bg-white py-3 px-4 mb-3 rounded shadow-sm\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"d-flex justify-content-between align-items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex align-items-center\",\n        children: [pathnames.length > 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleBack,\n          className: \"btn btn-icon btn-light rounded-circle me-2\",\n          style: {\n            width: '32px',\n            height: '32px',\n            padding: '0',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            border: '1px solid #e0e0e0'\n          },\n          children: /*#__PURE__*/_jsxDEV(Icon, {\n            icon: \"fluent:arrow-left-24-regular\",\n            width: \"20\",\n            height: \"20\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"mb-0 fw-bold d-flex align-items-center\",\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            icon: getPathInfo(pathnames[pathnames.length - 1] || 'dashboard').icon,\n            className: \"me-2\",\n            width: \"24\",\n            height: \"24\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 13\n          }, this), getPageTitle()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 392,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"ol\", {\n        className: \"breadcrumb mb-0 d-flex align-items-center d-none d-md-flex\",\n        children: breadcrumbItems.map((item, index) => /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: [index > 0 && /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"mx-1 text-muted\",\n            children: /*#__PURE__*/_jsxDEV(Icon, {\n              icon: \"fluent:chevron-right-24-regular\",\n              width: \"16\",\n              height: \"16\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            className: `breadcrumb-item ${index === breadcrumbItems.length - 1 ? 'active fw-semibold' : ''}`,\n            children: index === breadcrumbItems.length - 1 ? /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-primary d-flex align-items-center\",\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                icon: item.icon,\n                className: \"me-1\",\n                width: \"18\",\n                height: \"18\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 21\n              }, this), item.name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 21\n            }, this) : /*#__PURE__*/_jsxDEV(Link, {\n              to: item.path,\n              className: \"text-decoration-none text-secondary d-flex align-items-center\",\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                icon: item.icon,\n                className: \"me-1\",\n                width: \"18\",\n                height: \"18\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 441,\n                columnNumber: 21\n              }, this), item.name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 15\n          }, this)]\n        }, item.path, true, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 422,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 390,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 389,\n    columnNumber: 5\n  }, this);\n}\n_s(Breadcrumbs, \"ZmJpvzBBf8J7VCgSKqUUk0eHjAY=\", false, function () {\n  return [useLocation, useNavigate];\n});\n_c = Breadcrumbs;\nexport default Breadcrumbs;\nvar _c;\n$RefreshReg$(_c, \"Breadcrumbs\");", "map": {"version": 3, "names": ["React", "Link", "useLocation", "useNavigate", "Icon", "jsxDEV", "_jsxDEV", "Breadcrumbs", "_s", "location", "navigate", "pathnames", "pathname", "split", "filter", "x", "handleBack", "includes", "pathMap", "name", "icon", "getPathInfo", "path", "pathType", "indexOf", "char<PERSON>t", "toUpperCase", "slice", "getPageTitle", "length", "lastSegment", "secondLastSegment", "params", "URLSearchParams", "search", "classroomId", "get", "getBreadcrumbItems", "items", "helpItems", "push", "currentPath", "pathInfo", "breadcrumbItems", "className", "children", "onClick", "style", "width", "height", "padding", "display", "alignItems", "justifyContent", "border", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "index", "Fragment", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/NEW_LMS_FIXING/FRONT/src/components/user/Breadcrumbs.jsx"], "sourcesContent": ["import React from 'react';\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\nimport { Icon } from '@iconify/react';\n\nfunction Breadcrumbs() {\n  const location = useLocation();\n  const navigate = useNavigate();\n  \n  // Filter out empty strings and 'user'\n  const pathnames = location.pathname\n    .split('/')\n    .filter((x) => x !== '' && x !== 'user');\n\n  const handleBack = () => {\n    // Special handling for success payment pages - redirect to courses\n    if (pathnames.includes('successPayment')) {\n      navigate('/user/courses');\n    } else {\n      navigate(-1);\n    }\n  };\n\n  // Create a mapping of path segments to readable names and icons\n  const pathMap = {\n    'dashboard': {\n      name: 'Dashboard',\n      icon: 'fluent:home-24-regular'\n    },\n    'profile': {\n      name: 'Profile',\n      icon: 'fluent:person-24-regular'\n    },\n    'courses': {\n      name: 'Courses',\n      icon: 'fluent:book-24-regular'\n    },\n    'courseDetails': {\n      name: 'Course Details',\n      icon: 'fluent:book-information-24-regular'\n    },\n    'WatchCourse': {\n      name: 'Course Content',\n      icon: 'fluent:book-open-24-regular'\n    },\n    'orderDetails': {\n      name: 'Order Details',\n      icon: 'fluent:receipt-24-regular'\n    },\n    'orderDetailsWithPayu': {\n      name: 'Order Details',\n      icon: 'fluent:receipt-24-regular'\n    },\n    'successPayment': {\n      name: 'Payment Successful',\n      icon: 'fluent:checkmark-circle-24-regular'\n    },\n    'AllClassroom': {\n      name: 'Classrooms',\n      icon: 'fluent:class-24-regular'\n    },\n    'certificates': {\n      name: 'Certificates',\n      icon: 'fluent:certificate-24-regular'\n    },\n    'notifications': {\n      name: 'Notifications',\n      icon: 'fluent:alert-24-regular'\n    },\n    'notificationDetails': {\n      name: 'Notification Details',\n      icon: 'fluent:alert-urgent-24-regular'\n    },\n    'settings': {\n      name: 'Settings',\n      icon: 'fluent:settings-24-regular'\n    },\n    'help': {\n      name: 'Help',\n      icon: 'fluent:question-circle-24-regular'\n    },\n    'faq': {\n      name: 'FAQ',\n      icon: 'fluent:book-question-mark-24-regular'\n    },\n    'ticket': {\n      name: 'Tickets',\n      icon: 'fluent:ticket-24-regular'\n    },\n    'classroomDetails': {\n      name: 'Classroom Dashboard',\n      icon: 'fluent:classroom-24-regular'\n    },\n    'assignmentsQuestions': {\n      name: 'Assignment',\n      icon: 'fluent:task-list-24-regular'\n    }\n  };\n\n  const getPathInfo = (path) => {\n    // Handle encoded IDs\n    if (path.includes('%') || path.includes('eyJ')) {\n      const pathType = pathnames[pathnames.indexOf(path) - 1];\n      switch (pathType) {\n        case 'courses':\n        case 'courseDetails':\n          return {\n            name: 'Course Details',\n            icon: 'fluent:book-information-24-regular'\n          };\n        case 'WatchCourse':\n          return {\n            name: 'Course Content',\n            icon: 'fluent:book-open-24-regular'\n          };\n        case 'AllClassroom':\n        case 'classroomDetails':\n          return {\n            name: 'Classroom Dashboard',\n            icon: 'fluent:classroom-24-regular'\n          };\n        case 'assignmentsQuestions':\n          return {\n            name: 'Assignment',\n            icon: 'fluent:task-list-24-regular'\n          };\n        case 'notificationDetails':\n          return {\n            name: 'Notification Details',\n            icon: 'fluent:alert-urgent-24-regular'\n          };\n        default:\n          return {\n            name: path,\n            icon: 'fluent:document-24-regular'\n          };\n      }\n    }\n    \n    return pathMap[path] || {\n      name: path.charAt(0).toUpperCase() + path.slice(1),\n      icon: 'fluent:document-24-regular'\n    };\n  };\n\n  // Get the current page title\nconst getPageTitle = () => {\n  if (pathnames.length === 0) return 'Dashboard';\n    \n    const lastSegment = pathnames[pathnames.length - 1];\n    const secondLastSegment = pathnames[pathnames.length - 2];\n    \n    // Get classroom ID from query params if it exists\n    const params = new URLSearchParams(location.search);\n    const classroomId = params.get('classroomid');\n    \n    // Special cases for different paths\n    if (pathnames.includes('assignmentsQuestions')) {\n      return 'Assignment';\n    } else if (secondLastSegment === 'AllClassroom') {\n      return 'Classroom Dashboard';\n    } else if (secondLastSegment === 'WatchCourse') {\n      return 'Course Content';\n    } else if (secondLastSegment === 'courses' && \n        (lastSegment === 'courseDetails' || lastSegment.includes('%') || lastSegment.includes('eyJ'))) {\n    return 'Course Details';\n    } else if (lastSegment === 'notificationDetails' || \n              (secondLastSegment === 'notifications' && (lastSegment.includes('%') || lastSegment.includes('eyJ')))) {\n      return 'Notification Details';\n    } else if (secondLastSegment === 'help') {\n      if (lastSegment === 'faq') return 'FAQ';\n      if (lastSegment === 'ticket') return 'Tickets';\n    } else if (lastSegment === 'orderDetails') {\n      return 'Order Details';\n    } else if (lastSegment === 'orderDetailsWithPayu') {\n      return 'Order Details';\n    } else if (lastSegment === 'successPayment') {\n      return 'Payment Successful';\n    }\n    \n    return getPathInfo(lastSegment).name;\n  };\n\n  // Get breadcrumb items based on current path\n  const getBreadcrumbItems = () => {\n    const items = [\n      {\n        name: 'Dashboard',\n        icon: 'fluent:home-24-regular',\n        path: '/user/dashboard'\n      }\n    ];\n\n    // Get classroom ID from query params if it exists\n    const params = new URLSearchParams(location.search);\n    const classroomId = params.get('classroomid');\n\n    // Special handling for assignments questions with classroom context\n    if (pathnames.includes('assignmentsQuestions') && classroomId) {\n      return [\n        ...items,\n        {\n          name: 'Classrooms',\n          icon: 'fluent:class-24-regular',\n          path: '/user/AllClassroom'\n        },\n        {\n          name: 'Classroom Dashboard',\n          icon: 'fluent:classroom-24-regular',\n          path: `/user/AllClassroom/classroomDetails/${classroomId}`\n        },\n        {\n          name: 'Assignment',\n          icon: 'fluent:task-list-24-regular',\n          path: location.pathname + location.search\n        }\n      ];\n    }\n\n    // Special handling for classroom details\n    if (pathnames.includes('classroomDetails') || \n        (pathnames.includes('AllClassroom') && \n         (pathnames[pathnames.length - 1].includes('%') || pathnames[pathnames.length - 1].includes('eyJ')))) {\n      return [\n        ...items,\n        {\n          name: 'Classrooms',\n          icon: 'fluent:class-24-regular',\n          path: '/user/AllClassroom'\n        },\n        {\n          name: 'Classroom Dashboard',\n          icon: 'fluent:classroom-24-regular',\n          path: location.pathname\n        }\n      ];\n    }\n\n    // Special handling for success payment\n    if (pathnames.includes('successPayment')) {\n      return [\n        ...items,\n        {\n          name: 'Courses',\n          icon: 'fluent:book-24-regular',\n          path: '/user/courses'\n        },\n        {\n          name: 'Payment Successful',\n          icon: 'fluent:checkmark-circle-24-regular',\n          path: location.pathname + location.search\n        }\n      ];\n    }\n\n    // Special handling for order details\n    if (pathnames.includes('orderDetails')) {\n      return [\n        ...items,\n        {\n          name: 'Courses',\n          icon: 'fluent:book-24-regular',\n          path: '/user/courses'\n        },\n        {\n          name: 'Order Details',\n          icon: 'fluent:receipt-24-regular',\n          path: location.pathname\n        }\n      ];\n    }\n\n    // Special handling for order details with PayU\n    if (pathnames.includes('orderDetailsWithPayu')) {\n      return [\n        ...items,\n        {\n          name: 'Courses',\n          icon: 'fluent:book-24-regular',\n          path: '/user/courses'\n        },\n        {\n          name: 'Order Details',\n          icon: 'fluent:receipt-24-regular',\n          path: location.pathname\n        }\n      ];\n    }\n\n    // Special handling for help pages\n    if (pathnames.includes('help')) {\n      const helpItems = [\n        {\n          name: 'Help',\n          icon: 'fluent:question-circle-24-regular',\n          path: '/user/help'\n        }\n      ];\n\n      // Add FAQ or Ticket based on the path\n      if (pathnames.includes('faq')) {\n        helpItems.push({\n          name: 'FAQ',\n          icon: 'fluent:book-question-mark-24-regular',\n          path: location.pathname\n        });\n      } else if (pathnames.includes('ticket')) {\n        helpItems.push({\n          name: 'Tickets',\n          icon: 'fluent:ticket-24-regular',\n          path: location.pathname\n        });\n      }\n\n      return [...items, ...helpItems];\n    }\n\n    // Special handling for notification details\n    if (pathnames.includes('notificationDetails') || \n        (pathnames.includes('notifications') && pathnames[pathnames.length - 1].includes('%'))) {\n      return [\n        ...items,\n        {\n          name: 'Notifications',\n          icon: 'fluent:alert-24-regular',\n          path: '/user/notifications'\n        },\n        {\n          name: 'Notification Details',\n          icon: 'fluent:alert-urgent-24-regular',\n          path: location.pathname\n        }\n      ];\n    }\n\n    // Special handling for WatchCourse\n    if (pathnames.includes('WatchCourse')) {\n      return [\n        ...items,\n        {\n          name: 'Courses',\n          icon: 'fluent:book-24-regular',\n          path: '/user/courses'\n        },\n        {\n          name: 'Course Content',\n          icon: 'fluent:book-open-24-regular',\n          path: location.pathname\n        }\n      ];\n    }\n\n    // Special handling for course details\n    if (pathnames.includes('courseDetails') || \n        (pathnames.includes('courses') && \n         (pathnames[pathnames.length - 1].includes('%') || pathnames[pathnames.length - 1].includes('eyJ')))) {\n      return [\n        ...items,\n        {\n          name: 'Courses',\n          icon: 'fluent:book-24-regular',\n          path: '/user/courses'\n        },\n        {\n          name: 'Course Details',\n          icon: 'fluent:book-information-24-regular',\n          path: location.pathname\n        }\n      ];\n    }\n\n    // Add current path item\n    if (pathnames.length > 0) {\n      const currentPath = pathnames[pathnames.length - 1];\n      const pathInfo = getPathInfo(currentPath);\n      \n      items.push({\n        name: pathInfo.name,\n        icon: pathInfo.icon,\n        path: location.pathname\n      });\n    }\n\n    return items;\n  };\n\n  const breadcrumbItems = getBreadcrumbItems();\n\n  return (\n    <nav aria-label=\"breadcrumb\" className=\"bg-white py-3 px-4 mb-3 rounded shadow-sm\">\n      <div className=\"d-flex justify-content-between align-items-center\">\n        {/* Page Title with Back Button */}\n        <div className=\"d-flex align-items-center\">\n          {pathnames.length > 0 && (\n            <button \n              onClick={handleBack}\n              className=\"btn btn-icon btn-light rounded-circle me-2\"\n              style={{ \n                width: '32px', \n                height: '32px', \n                padding: '0',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                border: '1px solid #e0e0e0'\n              }}\n            >\n              <Icon icon=\"fluent:arrow-left-24-regular\" width=\"20\" height=\"20\" />\n            </button>\n          )}\n          <h4 className=\"mb-0 fw-bold d-flex align-items-center\">\n            <Icon\n              icon={getPathInfo(pathnames[pathnames.length - 1] || 'dashboard').icon}\n              className=\"me-2\"\n              width=\"24\"\n              height=\"24\"\n            />\n            {getPageTitle()}\n          </h4>\n        </div>\n\n        {/* Breadcrumbs on the right */}\n        <ol className=\"breadcrumb mb-0 d-flex align-items-center d-none d-md-flex\">\n          {breadcrumbItems.map((item, index) => (\n            <React.Fragment key={item.path}>\n              {index > 0 && (\n                <li className=\"mx-1 text-muted\">\n                  <Icon icon=\"fluent:chevron-right-24-regular\" width=\"16\" height=\"16\" />\n                </li>\n              )}\n              <li className={`breadcrumb-item ${index === breadcrumbItems.length - 1 ? 'active fw-semibold' : ''}`}>\n                {index === breadcrumbItems.length - 1 ? (\n                    <span className=\"text-primary d-flex align-items-center\">\n                    <Icon icon={item.icon} className=\"me-1\" width=\"18\" height=\"18\" />\n                    {item.name}\n                    </span>\n                ) : (\n                    <Link \n                    to={item.path}\n                      className=\"text-decoration-none text-secondary d-flex align-items-center\"\n                    >\n                    <Icon icon={item.icon} className=\"me-1\" width=\"18\" height=\"18\" />\n                    {item.name}\n                    </Link>\n                  )}\n                  </li>\n              </React.Fragment>\n          ))}\n        </ol>\n      </div>\n    </nav>\n  );\n}\n\nexport default Breadcrumbs;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,SAASC,IAAI,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,SAASC,WAAWA,CAAA,EAAG;EAAAC,EAAA;EACrB,MAAMC,QAAQ,GAAGP,WAAW,CAAC,CAAC;EAC9B,MAAMQ,QAAQ,GAAGP,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMQ,SAAS,GAAGF,QAAQ,CAACG,QAAQ,CAChCC,KAAK,CAAC,GAAG,CAAC,CACVC,MAAM,CAAEC,CAAC,IAAKA,CAAC,KAAK,EAAE,IAAIA,CAAC,KAAK,MAAM,CAAC;EAE1C,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB;IACA,IAAIL,SAAS,CAACM,QAAQ,CAAC,gBAAgB,CAAC,EAAE;MACxCP,QAAQ,CAAC,eAAe,CAAC;IAC3B,CAAC,MAAM;MACLA,QAAQ,CAAC,CAAC,CAAC,CAAC;IACd;EACF,CAAC;;EAED;EACA,MAAMQ,OAAO,GAAG;IACd,WAAW,EAAE;MACXC,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE;IACR,CAAC;IACD,SAAS,EAAE;MACTD,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE;IACR,CAAC;IACD,SAAS,EAAE;MACTD,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE;IACR,CAAC;IACD,eAAe,EAAE;MACfD,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE;IACR,CAAC;IACD,aAAa,EAAE;MACbD,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAE;IACR,CAAC;IACD,cAAc,EAAE;MACdD,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE;IACR,CAAC;IACD,sBAAsB,EAAE;MACtBD,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE;IACR,CAAC;IACD,gBAAgB,EAAE;MAChBD,IAAI,EAAE,oBAAoB;MAC1BC,IAAI,EAAE;IACR,CAAC;IACD,cAAc,EAAE;MACdD,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE;IACR,CAAC;IACD,cAAc,EAAE;MACdD,IAAI,EAAE,cAAc;MACpBC,IAAI,EAAE;IACR,CAAC;IACD,eAAe,EAAE;MACfD,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE;IACR,CAAC;IACD,qBAAqB,EAAE;MACrBD,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAE;IACR,CAAC;IACD,UAAU,EAAE;MACVD,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE;IACR,CAAC;IACD,MAAM,EAAE;MACND,IAAI,EAAE,MAAM;MACZC,IAAI,EAAE;IACR,CAAC;IACD,KAAK,EAAE;MACLD,IAAI,EAAE,KAAK;MACXC,IAAI,EAAE;IACR,CAAC;IACD,QAAQ,EAAE;MACRD,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE;IACR,CAAC;IACD,kBAAkB,EAAE;MAClBD,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,EAAE;IACR,CAAC;IACD,sBAAsB,EAAE;MACtBD,IAAI,EAAE,YAAY;MAClBC,IAAI,EAAE;IACR;EACF,CAAC;EAED,MAAMC,WAAW,GAAIC,IAAI,IAAK;IAC5B;IACA,IAAIA,IAAI,CAACL,QAAQ,CAAC,GAAG,CAAC,IAAIK,IAAI,CAACL,QAAQ,CAAC,KAAK,CAAC,EAAE;MAC9C,MAAMM,QAAQ,GAAGZ,SAAS,CAACA,SAAS,CAACa,OAAO,CAACF,IAAI,CAAC,GAAG,CAAC,CAAC;MACvD,QAAQC,QAAQ;QACd,KAAK,SAAS;QACd,KAAK,eAAe;UAClB,OAAO;YACLJ,IAAI,EAAE,gBAAgB;YACtBC,IAAI,EAAE;UACR,CAAC;QACH,KAAK,aAAa;UAChB,OAAO;YACLD,IAAI,EAAE,gBAAgB;YACtBC,IAAI,EAAE;UACR,CAAC;QACH,KAAK,cAAc;QACnB,KAAK,kBAAkB;UACrB,OAAO;YACLD,IAAI,EAAE,qBAAqB;YAC3BC,IAAI,EAAE;UACR,CAAC;QACH,KAAK,sBAAsB;UACzB,OAAO;YACLD,IAAI,EAAE,YAAY;YAClBC,IAAI,EAAE;UACR,CAAC;QACH,KAAK,qBAAqB;UACxB,OAAO;YACLD,IAAI,EAAE,sBAAsB;YAC5BC,IAAI,EAAE;UACR,CAAC;QACH;UACE,OAAO;YACLD,IAAI,EAAEG,IAAI;YACVF,IAAI,EAAE;UACR,CAAC;MACL;IACF;IAEA,OAAOF,OAAO,CAACI,IAAI,CAAC,IAAI;MACtBH,IAAI,EAAEG,IAAI,CAACG,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGJ,IAAI,CAACK,KAAK,CAAC,CAAC,CAAC;MAClDP,IAAI,EAAE;IACR,CAAC;EACH,CAAC;;EAED;EACF,MAAMQ,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIjB,SAAS,CAACkB,MAAM,KAAK,CAAC,EAAE,OAAO,WAAW;IAE5C,MAAMC,WAAW,GAAGnB,SAAS,CAACA,SAAS,CAACkB,MAAM,GAAG,CAAC,CAAC;IACnD,MAAME,iBAAiB,GAAGpB,SAAS,CAACA,SAAS,CAACkB,MAAM,GAAG,CAAC,CAAC;;IAEzD;IACA,MAAMG,MAAM,GAAG,IAAIC,eAAe,CAACxB,QAAQ,CAACyB,MAAM,CAAC;IACnD,MAAMC,WAAW,GAAGH,MAAM,CAACI,GAAG,CAAC,aAAa,CAAC;;IAE7C;IACA,IAAIzB,SAAS,CAACM,QAAQ,CAAC,sBAAsB,CAAC,EAAE;MAC9C,OAAO,YAAY;IACrB,CAAC,MAAM,IAAIc,iBAAiB,KAAK,cAAc,EAAE;MAC/C,OAAO,qBAAqB;IAC9B,CAAC,MAAM,IAAIA,iBAAiB,KAAK,aAAa,EAAE;MAC9C,OAAO,gBAAgB;IACzB,CAAC,MAAM,IAAIA,iBAAiB,KAAK,SAAS,KACrCD,WAAW,KAAK,eAAe,IAAIA,WAAW,CAACb,QAAQ,CAAC,GAAG,CAAC,IAAIa,WAAW,CAACb,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MACnG,OAAO,gBAAgB;IACvB,CAAC,MAAM,IAAIa,WAAW,KAAK,qBAAqB,IACrCC,iBAAiB,KAAK,eAAe,KAAKD,WAAW,CAACb,QAAQ,CAAC,GAAG,CAAC,IAAIa,WAAW,CAACb,QAAQ,CAAC,KAAK,CAAC,CAAE,EAAE;MAC/G,OAAO,sBAAsB;IAC/B,CAAC,MAAM,IAAIc,iBAAiB,KAAK,MAAM,EAAE;MACvC,IAAID,WAAW,KAAK,KAAK,EAAE,OAAO,KAAK;MACvC,IAAIA,WAAW,KAAK,QAAQ,EAAE,OAAO,SAAS;IAChD,CAAC,MAAM,IAAIA,WAAW,KAAK,cAAc,EAAE;MACzC,OAAO,eAAe;IACxB,CAAC,MAAM,IAAIA,WAAW,KAAK,sBAAsB,EAAE;MACjD,OAAO,eAAe;IACxB,CAAC,MAAM,IAAIA,WAAW,KAAK,gBAAgB,EAAE;MAC3C,OAAO,oBAAoB;IAC7B;IAEA,OAAOT,WAAW,CAACS,WAAW,CAAC,CAACX,IAAI;EACtC,CAAC;;EAED;EACA,MAAMkB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,KAAK,GAAG,CACZ;MACEnB,IAAI,EAAE,WAAW;MACjBC,IAAI,EAAE,wBAAwB;MAC9BE,IAAI,EAAE;IACR,CAAC,CACF;;IAED;IACA,MAAMU,MAAM,GAAG,IAAIC,eAAe,CAACxB,QAAQ,CAACyB,MAAM,CAAC;IACnD,MAAMC,WAAW,GAAGH,MAAM,CAACI,GAAG,CAAC,aAAa,CAAC;;IAE7C;IACA,IAAIzB,SAAS,CAACM,QAAQ,CAAC,sBAAsB,CAAC,IAAIkB,WAAW,EAAE;MAC7D,OAAO,CACL,GAAGG,KAAK,EACR;QACEnB,IAAI,EAAE,YAAY;QAClBC,IAAI,EAAE,yBAAyB;QAC/BE,IAAI,EAAE;MACR,CAAC,EACD;QACEH,IAAI,EAAE,qBAAqB;QAC3BC,IAAI,EAAE,6BAA6B;QACnCE,IAAI,EAAE,uCAAuCa,WAAW;MAC1D,CAAC,EACD;QACEhB,IAAI,EAAE,YAAY;QAClBC,IAAI,EAAE,6BAA6B;QACnCE,IAAI,EAAEb,QAAQ,CAACG,QAAQ,GAAGH,QAAQ,CAACyB;MACrC,CAAC,CACF;IACH;;IAEA;IACA,IAAIvB,SAAS,CAACM,QAAQ,CAAC,kBAAkB,CAAC,IACrCN,SAAS,CAACM,QAAQ,CAAC,cAAc,CAAC,KACjCN,SAAS,CAACA,SAAS,CAACkB,MAAM,GAAG,CAAC,CAAC,CAACZ,QAAQ,CAAC,GAAG,CAAC,IAAIN,SAAS,CAACA,SAAS,CAACkB,MAAM,GAAG,CAAC,CAAC,CAACZ,QAAQ,CAAC,KAAK,CAAC,CAAE,EAAE;MACxG,OAAO,CACL,GAAGqB,KAAK,EACR;QACEnB,IAAI,EAAE,YAAY;QAClBC,IAAI,EAAE,yBAAyB;QAC/BE,IAAI,EAAE;MACR,CAAC,EACD;QACEH,IAAI,EAAE,qBAAqB;QAC3BC,IAAI,EAAE,6BAA6B;QACnCE,IAAI,EAAEb,QAAQ,CAACG;MACjB,CAAC,CACF;IACH;;IAEA;IACA,IAAID,SAAS,CAACM,QAAQ,CAAC,gBAAgB,CAAC,EAAE;MACxC,OAAO,CACL,GAAGqB,KAAK,EACR;QACEnB,IAAI,EAAE,SAAS;QACfC,IAAI,EAAE,wBAAwB;QAC9BE,IAAI,EAAE;MACR,CAAC,EACD;QACEH,IAAI,EAAE,oBAAoB;QAC1BC,IAAI,EAAE,oCAAoC;QAC1CE,IAAI,EAAEb,QAAQ,CAACG,QAAQ,GAAGH,QAAQ,CAACyB;MACrC,CAAC,CACF;IACH;;IAEA;IACA,IAAIvB,SAAS,CAACM,QAAQ,CAAC,cAAc,CAAC,EAAE;MACtC,OAAO,CACL,GAAGqB,KAAK,EACR;QACEnB,IAAI,EAAE,SAAS;QACfC,IAAI,EAAE,wBAAwB;QAC9BE,IAAI,EAAE;MACR,CAAC,EACD;QACEH,IAAI,EAAE,eAAe;QACrBC,IAAI,EAAE,2BAA2B;QACjCE,IAAI,EAAEb,QAAQ,CAACG;MACjB,CAAC,CACF;IACH;;IAEA;IACA,IAAID,SAAS,CAACM,QAAQ,CAAC,sBAAsB,CAAC,EAAE;MAC9C,OAAO,CACL,GAAGqB,KAAK,EACR;QACEnB,IAAI,EAAE,SAAS;QACfC,IAAI,EAAE,wBAAwB;QAC9BE,IAAI,EAAE;MACR,CAAC,EACD;QACEH,IAAI,EAAE,eAAe;QACrBC,IAAI,EAAE,2BAA2B;QACjCE,IAAI,EAAEb,QAAQ,CAACG;MACjB,CAAC,CACF;IACH;;IAEA;IACA,IAAID,SAAS,CAACM,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC9B,MAAMsB,SAAS,GAAG,CAChB;QACEpB,IAAI,EAAE,MAAM;QACZC,IAAI,EAAE,mCAAmC;QACzCE,IAAI,EAAE;MACR,CAAC,CACF;;MAED;MACA,IAAIX,SAAS,CAACM,QAAQ,CAAC,KAAK,CAAC,EAAE;QAC7BsB,SAAS,CAACC,IAAI,CAAC;UACbrB,IAAI,EAAE,KAAK;UACXC,IAAI,EAAE,sCAAsC;UAC5CE,IAAI,EAAEb,QAAQ,CAACG;QACjB,CAAC,CAAC;MACJ,CAAC,MAAM,IAAID,SAAS,CAACM,QAAQ,CAAC,QAAQ,CAAC,EAAE;QACvCsB,SAAS,CAACC,IAAI,CAAC;UACbrB,IAAI,EAAE,SAAS;UACfC,IAAI,EAAE,0BAA0B;UAChCE,IAAI,EAAEb,QAAQ,CAACG;QACjB,CAAC,CAAC;MACJ;MAEA,OAAO,CAAC,GAAG0B,KAAK,EAAE,GAAGC,SAAS,CAAC;IACjC;;IAEA;IACA,IAAI5B,SAAS,CAACM,QAAQ,CAAC,qBAAqB,CAAC,IACxCN,SAAS,CAACM,QAAQ,CAAC,eAAe,CAAC,IAAIN,SAAS,CAACA,SAAS,CAACkB,MAAM,GAAG,CAAC,CAAC,CAACZ,QAAQ,CAAC,GAAG,CAAE,EAAE;MAC1F,OAAO,CACL,GAAGqB,KAAK,EACR;QACEnB,IAAI,EAAE,eAAe;QACrBC,IAAI,EAAE,yBAAyB;QAC/BE,IAAI,EAAE;MACR,CAAC,EACD;QACEH,IAAI,EAAE,sBAAsB;QAC5BC,IAAI,EAAE,gCAAgC;QACtCE,IAAI,EAAEb,QAAQ,CAACG;MACjB,CAAC,CACF;IACH;;IAEA;IACA,IAAID,SAAS,CAACM,QAAQ,CAAC,aAAa,CAAC,EAAE;MACrC,OAAO,CACL,GAAGqB,KAAK,EACR;QACEnB,IAAI,EAAE,SAAS;QACfC,IAAI,EAAE,wBAAwB;QAC9BE,IAAI,EAAE;MACR,CAAC,EACD;QACEH,IAAI,EAAE,gBAAgB;QACtBC,IAAI,EAAE,6BAA6B;QACnCE,IAAI,EAAEb,QAAQ,CAACG;MACjB,CAAC,CACF;IACH;;IAEA;IACA,IAAID,SAAS,CAACM,QAAQ,CAAC,eAAe,CAAC,IAClCN,SAAS,CAACM,QAAQ,CAAC,SAAS,CAAC,KAC5BN,SAAS,CAACA,SAAS,CAACkB,MAAM,GAAG,CAAC,CAAC,CAACZ,QAAQ,CAAC,GAAG,CAAC,IAAIN,SAAS,CAACA,SAAS,CAACkB,MAAM,GAAG,CAAC,CAAC,CAACZ,QAAQ,CAAC,KAAK,CAAC,CAAE,EAAE;MACxG,OAAO,CACL,GAAGqB,KAAK,EACR;QACEnB,IAAI,EAAE,SAAS;QACfC,IAAI,EAAE,wBAAwB;QAC9BE,IAAI,EAAE;MACR,CAAC,EACD;QACEH,IAAI,EAAE,gBAAgB;QACtBC,IAAI,EAAE,oCAAoC;QAC1CE,IAAI,EAAEb,QAAQ,CAACG;MACjB,CAAC,CACF;IACH;;IAEA;IACA,IAAID,SAAS,CAACkB,MAAM,GAAG,CAAC,EAAE;MACxB,MAAMY,WAAW,GAAG9B,SAAS,CAACA,SAAS,CAACkB,MAAM,GAAG,CAAC,CAAC;MACnD,MAAMa,QAAQ,GAAGrB,WAAW,CAACoB,WAAW,CAAC;MAEzCH,KAAK,CAACE,IAAI,CAAC;QACTrB,IAAI,EAAEuB,QAAQ,CAACvB,IAAI;QACnBC,IAAI,EAAEsB,QAAQ,CAACtB,IAAI;QACnBE,IAAI,EAAEb,QAAQ,CAACG;MACjB,CAAC,CAAC;IACJ;IAEA,OAAO0B,KAAK;EACd,CAAC;EAED,MAAMK,eAAe,GAAGN,kBAAkB,CAAC,CAAC;EAE5C,oBACE/B,OAAA;IAAK,cAAW,YAAY;IAACsC,SAAS,EAAC,2CAA2C;IAAAC,QAAA,eAChFvC,OAAA;MAAKsC,SAAS,EAAC,mDAAmD;MAAAC,QAAA,gBAEhEvC,OAAA;QAAKsC,SAAS,EAAC,2BAA2B;QAAAC,QAAA,GACvClC,SAAS,CAACkB,MAAM,GAAG,CAAC,iBACnBvB,OAAA;UACEwC,OAAO,EAAE9B,UAAW;UACpB4B,SAAS,EAAC,4CAA4C;UACtDG,KAAK,EAAE;YACLC,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE,GAAG;YACZC,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxBC,MAAM,EAAE;UACV,CAAE;UAAAT,QAAA,eAEFvC,OAAA,CAACF,IAAI;YAACgB,IAAI,EAAC,8BAA8B;YAAC4B,KAAK,EAAC,IAAI;YAACC,MAAM,EAAC;UAAI;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CACT,eACDpD,OAAA;UAAIsC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACpDvC,OAAA,CAACF,IAAI;YACHgB,IAAI,EAAEC,WAAW,CAACV,SAAS,CAACA,SAAS,CAACkB,MAAM,GAAG,CAAC,CAAC,IAAI,WAAW,CAAC,CAACT,IAAK;YACvEwB,SAAS,EAAC,MAAM;YAChBI,KAAK,EAAC,IAAI;YACVC,MAAM,EAAC;UAAI;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,EACD9B,YAAY,CAAC,CAAC;QAAA;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGNpD,OAAA;QAAIsC,SAAS,EAAC,4DAA4D;QAAAC,QAAA,EACvEF,eAAe,CAACgB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC/BvD,OAAA,CAACN,KAAK,CAAC8D,QAAQ;UAAAjB,QAAA,GACZgB,KAAK,GAAG,CAAC,iBACRvD,OAAA;YAAIsC,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC7BvC,OAAA,CAACF,IAAI;cAACgB,IAAI,EAAC,iCAAiC;cAAC4B,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC;YAAI;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CACL,eACDpD,OAAA;YAAIsC,SAAS,EAAE,mBAAmBiB,KAAK,KAAKlB,eAAe,CAACd,MAAM,GAAG,CAAC,GAAG,oBAAoB,GAAG,EAAE,EAAG;YAAAgB,QAAA,EAClGgB,KAAK,KAAKlB,eAAe,CAACd,MAAM,GAAG,CAAC,gBACjCvB,OAAA;cAAMsC,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACxDvC,OAAA,CAACF,IAAI;gBAACgB,IAAI,EAAEwC,IAAI,CAACxC,IAAK;gBAACwB,SAAS,EAAC,MAAM;gBAACI,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC;cAAI;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAChEE,IAAI,CAACzC,IAAI;YAAA;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,gBAEPpD,OAAA,CAACL,IAAI;cACL8D,EAAE,EAAEH,IAAI,CAACtC,IAAK;cACZsB,SAAS,EAAC,+DAA+D;cAAAC,QAAA,gBAE3EvC,OAAA,CAACF,IAAI;gBAACgB,IAAI,EAAEwC,IAAI,CAACxC,IAAK;gBAACwB,SAAS,EAAC,MAAM;gBAACI,KAAK,EAAC,IAAI;gBAACC,MAAM,EAAC;cAAI;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAChEE,IAAI,CAACzC,IAAI;YAAA;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UACP;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA,GArBUE,IAAI,CAACtC,IAAI;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsBZ,CACnB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAClD,EAAA,CA/bQD,WAAW;EAAA,QACDL,WAAW,EACXC,WAAW;AAAA;AAAA6D,EAAA,GAFrBzD,WAAW;AAicpB,eAAeA,WAAW;AAAC,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}