{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\NEW_LMS_FIXING\\\\FRONT\\\\src\\\\routes\\\\PublicRoutes.jsx\";\nimport React from 'react';\nimport { Routes, Route } from 'react-router-dom';\nimport PublicCourseDetails from '../pages/public/PublicCourseDetails';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction PublicRoutes() {\n  return /*#__PURE__*/_jsxDEV(Routes, {\n    children: /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/public/course/:subdomain/:encodedId\",\n      element: /*#__PURE__*/_jsxDEV(PublicCourseDetails, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 67\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n}\n_c = PublicRoutes;\nexport default PublicRoutes;\nvar _c;\n$RefreshReg$(_c, \"PublicRoutes\");", "map": {"version": 3, "names": ["React", "Routes", "Route", "PublicCourseDetails", "jsxDEV", "_jsxDEV", "PublicRoutes", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/NEW_LMS_FIXING/FRONT/src/routes/PublicRoutes.jsx"], "sourcesContent": ["import React from 'react';\nimport { Routes, Route } from 'react-router-dom';\nimport PublicCourseDetails from '../pages/public/PublicCourseDetails';\n\nfunction PublicRoutes() {\n  return (\n    <Routes>\n      <Route path=\"/public/course/:subdomain/:encodedId\" element={<PublicCourseDetails />} />\n    </Routes>\n  );\n}\n\nexport default PublicRoutes;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AAChD,OAAOC,mBAAmB,MAAM,qCAAqC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtE,SAASC,YAAYA,CAAA,EAAG;EACtB,oBACED,OAAA,CAACJ,MAAM;IAAAM,QAAA,eACLF,OAAA,CAACH,KAAK;MAACM,IAAI,EAAC,sCAAsC;MAACC,OAAO,eAAEJ,OAAA,CAACF,mBAAmB;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACjF,CAAC;AAEb;AAACC,EAAA,GANQR,YAAY;AAQrB,eAAeA,YAAY;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}